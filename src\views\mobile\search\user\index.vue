<template>
  <div class="mobile-user-search">
    <!-- 搜索栏 -->
    <SearchBar
      v-model="searchKeyword"
      placeholder="搜索用户姓名或工号"
      :show-filter="true"
      :filters="filterConfig"
      @search="handleSearch"
      @filter="handleFilter"
      @clear="handleClear"
    >
      <template #filters="{ filters, updateFilter }">
        <FilterPanel :filters="filterConfig" @update="updateFilter" />
      </template>
    </SearchBar>

    <!-- 搜索结果 -->
    <div class="search-results">
      <!-- 结果统计 -->
      <div v-if="hasSearched" class="result-stats">
        <span class="stats-text">找到 {{ total }} 个用户</span>
        <div class="view-toggle">
          <button
            class="toggle-btn"
            :class="{ active: viewMode === 'list' }"
            @click="viewMode = 'list'"
          >
            <svg-icon icon-class="list" size="14px" />
          </button>
          <button
            class="toggle-btn"
            :class="{ active: viewMode === 'grid' }"
            @click="viewMode = 'grid'"
          >
            <svg-icon icon-class="grid" size="14px" />
          </button>
        </div>
      </div>

      <!-- 用户列表 -->
      <div class="user-list" :class="viewMode">
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <span>搜索中...</span>
        </div>

        <div v-else-if="userList.length === 0 && hasSearched" class="empty-result">
          <svg-icon icon-class="empty" size="48px" />
          <div class="empty-text">未找到相关用户</div>
          <div class="empty-tips">请尝试调整搜索条件</div>
        </div>

        <div v-else class="user-items">
          <!-- 列表视图 -->
          <div
            v-if="viewMode === 'list'"
            v-for="user in userList"
            :key="user.id"
            class="user-item list-item"
            @click="viewUserDetail(user)"
          >
            <div class="user-avatar">
              <img :src="user.avatar || defaultAvatar" :alt="user.nickname" />
              <div v-if="user.status === 1" class="online-dot"></div>
            </div>
            
            <div class="user-info">
              <div class="user-header">
                <span class="user-name">{{ user.nickname }}</span>
                <span class="user-status" :class="getStatusClass(user.status)">
                  {{ getStatusText(user.status) }}
                </span>
              </div>
              
              <div class="user-details">
                <div class="detail-row">
                  <span class="detail-label">工号:</span>
                  <span class="detail-value">{{ user.username }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">部门:</span>
                  <span class="detail-value">{{ user.deptName || '-' }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">角色:</span>
                  <span class="detail-value">{{ user.roleNames || '-' }}</span>
                </div>
              </div>
            </div>

            <div class="user-actions">
              <button class="action-btn" @click.stop="contactUser(user)">
                <svg-icon icon-class="phone" size="14px" />
              </button>
              <button class="action-btn" @click.stop="messageUser(user)">
                <svg-icon icon-class="message" size="14px" />
              </button>
            </div>
          </div>

          <!-- 网格视图 -->
          <div
            v-else
            v-for="user in userList"
            :key="user.id"
            class="user-item grid-item"
            @click="viewUserDetail(user)"
          >
            <div class="user-avatar">
              <img :src="user.avatar || defaultAvatar" :alt="user.nickname" />
              <div v-if="user.status === 1" class="online-dot"></div>
            </div>
            
            <div class="user-name">{{ user.nickname }}</div>
            <div class="user-dept">{{ user.deptName || '-' }}</div>
            <div class="user-role">{{ user.roleNames || '-' }}</div>
            
            <div class="grid-actions">
              <button class="grid-action-btn" @click.stop="contactUser(user)">
                联系
              </button>
            </div>
          </div>
        </div>

        <!-- 加载更多 -->
        <div v-if="hasMore && !loading" class="load-more">
          <button class="load-more-btn" @click="loadMore">
            加载更多
          </button>
        </div>
      </div>
    </div>

    <!-- 用户详情弹窗 -->
    <div v-if="showUserDetail" class="user-detail-overlay" @click="closeUserDetail">
      <div class="user-detail-panel" @click.stop>
        <div class="detail-header">
          <span class="detail-title">用户详情</span>
          <button class="close-btn" @click="closeUserDetail">
            <svg-icon icon-class="close" size="16px" />
          </button>
        </div>
        
        <div class="detail-content">
          <div class="detail-avatar">
            <img :src="selectedUser?.avatar || defaultAvatar" :alt="selectedUser?.nickname" />
          </div>
          
          <div class="detail-info">
            <div class="info-item">
              <span class="info-label">姓名</span>
              <span class="info-value">{{ selectedUser?.nickname }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">工号</span>
              <span class="info-value">{{ selectedUser?.username }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">部门</span>
              <span class="info-value">{{ selectedUser?.deptName || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">角色</span>
              <span class="info-value">{{ selectedUser?.roleNames || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">邮箱</span>
              <span class="info-value">{{ selectedUser?.email || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">手机</span>
              <span class="info-value">{{ selectedUser?.mobile || '-' }}</span>
            </div>
          </div>
          
          <div class="detail-actions">
            <button class="detail-action-btn primary" @click="contactUser(selectedUser)">
              <svg-icon icon-class="phone" size="16px" />
              联系
            </button>
            <button class="detail-action-btn" @click="messageUser(selectedUser)">
              <svg-icon icon-class="message" size="16px" />
              消息
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import SearchBar from '@/components/mobile/SearchBar/index.vue'
import FilterPanel from '@/components/mobile/FilterPanel/index.vue'
import UserAPI, { type UserQuery } from '@/api/system/user'

defineOptions({
  name: 'MobileUserSearch'
})

// 搜索相关
const searchKeyword = ref('')
const hasSearched = ref(false)
const loading = ref(false)
const viewMode = ref<'list' | 'grid'>('list')

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const hasMore = computed(() => userList.value.length < total.value)

// 数据
const userList = ref<any[]>([])
const selectedUser = ref<any>(null)
const showUserDetail = ref(false)
const defaultAvatar = ref(new URL("../../../../assets/logo.png", import.meta.url).href)

// 查询参数
const queryParams = reactive<UserQuery>({
  pageNum: 1,
  pageSize: 20,
  keywords: '',
  status: undefined,
  deptId: undefined
})

// 筛选配置
const filterConfig = ref([
  {
    key: 'status',
    label: '用户状态',
    type: 'radio' as const,
    value: '',
    options: [
      { label: '全部', value: '' },
      { label: '正常', value: '1' },
      { label: '禁用', value: '0' }
    ]
  },
  {
    key: 'deptId',
    label: '所属部门',
    type: 'select' as const,
    value: '',
    options: [
      { label: '全部部门', value: '' },
      { label: '技术部', value: '1' },
      { label: '销售部', value: '2' },
      { label: '人事部', value: '3' },
      { label: '财务部', value: '4' }
    ]
  }
])

// 处理搜索
const handleSearch = (keyword: string) => {
  searchKeyword.value = keyword
  resetPagination()
  performSearch()
}

// 处理筛选
const handleFilter = (filters: any[]) => {
  filters.forEach(filter => {
    switch (filter.key) {
      case 'status':
        queryParams.status = filter.value ? Number(filter.value) : undefined
        break
      case 'deptId':
        queryParams.deptId = filter.value ? Number(filter.value) : undefined
        break
    }
  })
  
  resetPagination()
  performSearch()
}

// 处理清空
const handleClear = () => {
  searchKeyword.value = ''
  resetFilters()
  resetPagination()
  userList.value = []
  hasSearched.value = false
}

// 执行搜索
const performSearch = async () => {
  loading.value = true
  hasSearched.value = true
  
  try {
    queryParams.keywords = searchKeyword.value
    queryParams.pageNum = currentPage.value
    queryParams.pageSize = pageSize.value
    
    const response = await UserAPI.getPage(queryParams)
    
    if (currentPage.value === 1) {
      userList.value = response.list || []
    } else {
      userList.value.push(...(response.list || []))
    }
    
    total.value = response.total || 0
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = () => {
  currentPage.value++
  performSearch()
}

// 重置分页
const resetPagination = () => {
  currentPage.value = 1
  queryParams.pageNum = 1
}

// 重置筛选
const resetFilters = () => {
  filterConfig.value.forEach(filter => {
    filter.value = ''
  })
  queryParams.status = undefined
  queryParams.deptId = undefined
}

// 查看用户详情
const viewUserDetail = (user: any) => {
  selectedUser.value = user
  showUserDetail.value = true
}

// 关闭用户详情
const closeUserDetail = () => {
  showUserDetail.value = false
  selectedUser.value = null
}

// 联系用户
const contactUser = (user: any) => {
  if (user.mobile) {
    window.location.href = `tel:${user.mobile}`
  } else {
    ElMessage.warning('该用户未设置手机号')
  }
}

// 发送消息
const messageUser = (user: any) => {
  ElMessage.info('消息功能开发中')
}

// 获取状态样式类
const getStatusClass = (status: number) => {
  return status === 1 ? 'status-active' : 'status-inactive'
}

// 获取状态文本
const getStatusText = (status: number) => {
  return status === 1 ? '正常' : '禁用'
}

onMounted(() => {
  // 可以在这里加载默认数据
})
</script>

<style lang="scss" scoped>
.mobile-user-search {
  background: #f5f5f5;
  min-height: 100vh;
}

.search-results {
  padding: 0 16px;
}

.result-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 12px;
}

.stats-text {
  font-size: 14px;
  color: #666;
}

.view-toggle {
  display: flex;
  background: white;
  border-radius: 16px;
  padding: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggle-btn {
  padding: 6px 12px;
  border: none;
  background: transparent;
  border-radius: 14px;
  color: #666;
  cursor: pointer;
  
  &.active {
    background: var(--el-color-primary);
    color: white;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: #666;
  font-size: 14px;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f0f0f0;
  border-top: 2px solid var(--el-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.empty-result {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-text {
  font-size: 16px;
  margin: 16px 0 8px;
}

.empty-tips {
  font-size: 12px;
}

// 列表视图样式
.user-list.list .user-items {
  .list-item {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    
    &:active {
      background: #f8f8f8;
    }
  }
}

// 网格视图样式
.user-list.grid .user-items {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  
  .grid-item {
    background: white;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    
    &:active {
      background: #f8f8f8;
    }
  }
}

.user-avatar {
  position: relative;
  width: 48px;
  height: 48px;
  margin-right: 12px;
  
  img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
  }
  
  .grid-item & {
    margin: 0 auto 8px;
  }
}

.online-dot {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #52c41a;
  border: 2px solid white;
  border-radius: 50%;
}

.user-info {
  flex: 1;
}

.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.user-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  
  .grid-item & {
    font-size: 14px;
    margin-bottom: 4px;
  }
}

.user-status {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  
  &.status-active {
    background: #f6ffed;
    color: #52c41a;
  }
  
  &.status-inactive {
    background: #fff2f0;
    color: #ff4d4f;
  }
}

.user-details {
  .detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2px;
    font-size: 12px;
  }
  
  .detail-label {
    color: #999;
  }
  
  .detail-value {
    color: #666;
  }
}

.user-dept,
.user-role {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.user-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f5f5f5;
  border-radius: 16px;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:active {
    background: #e8e8e8;
  }
}

.grid-actions {
  margin-top: 8px;
}

.grid-action-btn {
  width: 100%;
  height: 28px;
  border: none;
  background: var(--el-color-primary);
  color: white;
  border-radius: 14px;
  font-size: 12px;
  cursor: pointer;
  
  &:active {
    background: var(--el-color-primary-light-3);
  }
}

.load-more {
  text-align: center;
  padding: 20px;
}

.load-more-btn {
  padding: 12px 24px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  color: #666;
  cursor: pointer;
  
  &:active {
    background: #f5f5f5;
  }
}

// 用户详情弹窗样式
.user-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.user-detail-panel {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-title {
  font-size: 18px;
  font-weight: 500;
}

.close-btn {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
}

.detail-content {
  padding: 20px;
  text-align: center;
}

.detail-avatar {
  margin-bottom: 20px;
  
  img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
  }
}

.detail-info {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  color: #666;
  font-size: 14px;
}

.info-value {
  color: #333;
  font-size: 14px;
}

.detail-actions {
  display: flex;
  gap: 12px;
}

.detail-action-btn {
  flex: 1;
  height: 40px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  
  &.primary {
    background: var(--el-color-primary);
    color: white;
  }
  
  &:not(.primary) {
    background: #f5f5f5;
    color: #666;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
