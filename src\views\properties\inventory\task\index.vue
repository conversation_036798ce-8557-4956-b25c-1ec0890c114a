<!-- cursor测试:1 -->
<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="选择年份" prop="nf" v-if="view && props.type != 'cx'">
            <el-date-picker
              v-model="queryParams.nf"
              type="year"
              placeholder="选择年份"
              value-format="YYYY"
            />
          </el-form-item>
          <el-form-item v-if="view || cx || add" label="资产编号" prop="zcbh">
            <el-input
              v-model="queryParams.zcbh"
              placeholder="资产编号"
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item v-if="view || cx || add" label="资产名称" prop="zcmc">
            <el-input
              v-model="queryParams.zcmc"
              placeholder="资产名称"
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item v-if="add" label="存放地点" prop="cfdd">
            <el-input
              v-model="queryParams.cfdd"
              placeholder="存放地点"
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item v-if="add" label="入账日期" prop="djsj">
            <el-date-picker
              v-model="queryParams.djsj"
              :editable="false"
              class="filter-item"
              type="daterange"
              range-separator="~"
              start-placeholder="开始时间"
              end-placeholder="截止时间"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item v-if="view || cx" label="盘点状态" prop="qczt">
            <DDLXcode v-model="queryParams.qczt" xcode="020231" />
          </el-form-item>
          <el-form-item v-if="view" label="盘点结果" prop="qcjg">
            <DDLXcode v-model="queryParams.qcjg" xcode="020232" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon>
                <Search />
              </template>
              搜索
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon>
                <Refresh />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="盘点任务列表">
        <div>
          <el-button v-if="add" type="success" plain size="small" @click="handleBatchUpdate">
            <template #icon>
              <Plus />
            </template>
            批量盘点
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        stripe
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column v-if="add" type="selection" width="55" align="center" fixed />

        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column key="jhbh" label="计划编号" prop="jhbh" min-width="100" align="center" />
        <el-table-column key="zcbh" label="资产编号" prop="zcbh" min-width="100" align="center" />
        <el-table-column key="zcmc" label="资产名称" prop="zcmc" min-width="120" align="center" />
        <el-table-column key="ggxh" label="规格型号" prop="ggxh" min-width="100" align="center" />
        <el-table-column key="sl" label="数量" prop="sl" min-width="60" align="center" />
        <el-table-column
          key="syrname"
          label="保管员"
          prop="syrname"
          min-width="100"
          align="center"
        />
        <el-table-column
          key="cfdd"
          label="存放地点"
          prop="cfddname"
          min-width="120"
          align="center"
        />
        <el-table-column
          key="sybmname"
          label="所在部门"
          prop="sybmname"
          min-width="100"
          align="center"
        />
        <el-table-column
          key="qcztname"
          label="盘点状态"
          prop="qcztname"
          min-width="100"
          align="center"
        />
        <el-table-column
          key="djsj"
          label="入账日期"
          prop="djsj"
          min-width="100"
          align="center"
          v-if="add"
        />
        <el-table-column
          key="qcjgname"
          label="盘点结果"
          prop="qcjgname"
          min-width="100"
          align="center"
        />
        <el-table-column key="qcqk" label="盘点情况" prop="qcqk" min-width="100" align="center" />
        <el-table-column fixed="right" label="操作" width="180">
          <template #default="scope">
            <el-button
              v-if="add"
              type="primary"
              icon="edit"
              size="small"
              @click="handleOpenDialog(true, scope.row)"
            >
              盘点
            </el-button>
            <el-button
              v-if="view || add || cx"
              type="success"
              icon="Document"
              size="small"
              @click="handleOpenDialog(false, scope.row)"
            >
              查看
            </el-button>

            <el-button
              v-if="view && scope.row.isCallBack == '1'"
              type="danger"
              icon="Remove"
              size="small"
              @click="handleCallBack(scope.row.guid)"
            >
              收回
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 10"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 批量修改 -->
    <el-drawer v-model="batchUpdateVisible" append-to-body size="40%">
      <BatchUpdate
        :ids="removeIds.join(',')"
        :handleQuery="handleQuery"
        :RefreshFatherDrawer="RefreshFatherDrawer"
      />
    </el-drawer>

    <!-- 任务表单弹窗 -->
    <el-drawer v-model="dialog.visible" :title="dialog.title" append-to-body size="80%">
      <el-card>
        <!-- 
        id、guid常规参数
        key用于刷新组件
        editable用于区分是否可编辑
        dcbm调出部门特殊字段
        netcode新增编辑时为空,审核时需要 
        RefreshFatherDrawer关闭抽屉并刷新数据  -->
        <taskView
          :id="itemId"
          :key="itemGuid"
          :guid="itemGuid"
          :editable="itemEditable"
          :dcbm="itemDcbm"
          :netcode="itemNetcode"
          :RefreshFatherDrawer="RefreshFatherDrawer"
        />
      </el-card>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import taskAPI, { taskPageVO, taskPageQuery } from "@/api/properties/task";
import taskView from "./components/TaskView.vue";
import BatchUpdate from "./components/BatchUpdate.vue";
import { getGuid } from "@/utils/guid";
//————————————————————————————————————————————暴露的方法,和请求参数

const props = defineProps({
  guid: {
    type: String,
    required: true,
  },
  dcbm: {
    type: String,
    required: true,
  },
  syr: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    required: true,
  },
});

function RefreshFatherDrawer(close?: boolean) {
  //是否关闭弹窗由参数决定，默认不关闭
  if (close) {
    dialog.visible = false;
  }
  // itemId.value = undefined;
  handleQuery();
}
// 获取路由参数,view/add/check
const route = useRoute();
// 获取路由实例（可导航）
const router = useRouter();
const type = ref(route.query.type?.toString() || "view");
//显示隐藏权限,用于v-if
const add = type.value == "add";
const view = type.value == "view";
const cx = type.value == "cx";
if (props.type == "cx") {
  type.value = "cx";
}

//————————————————————————————————————————————查询相关
const queryFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);
let nf = "";
if (type.value == "view") {
  nf = String(new Date().getFullYear());
}
const queryParams = reactive<taskPageQuery>({
  pageNum: 1,
  pageSize: 10,
  type: type.value,
  nf: nf,
  jhguid: props.guid,
  syr: props.syr,
  sybm: props.dcbm,
});
// 任务表格数据
const pageData = ref<any[]>([]);

/** 查询任务 */
function handleQuery() {
  loading.value = true;
  console.log("刷新列表数据");
  taskAPI
    .getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .catch((error) => {
      console.error("获取任务数据失败:", error);
      ElMessage.error("获取任务数据失败");
      pageData.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置任务查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  queryParams.type = type.value;
  handleQuery();
}
const handleChangeRadio = () => {
  handleResetQuery();
};

//————————————————————————————————————————————操作相关
const removeIds = ref<number[]>([]);
/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: taskPageVO[]) {
  removeIds.value = selection.map((item) => item.id).filter((id) => id !== undefined) as number[];
}

/** 删除任务 */
function handleDelete(id?: string) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      taskAPI
        .deleteByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .catch((error) => {
          console.error("删除任务数据失败:", error);
          ElMessage.error("删除任务数据失败");
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

const handleCallBack = (guid: string) => {
  ElMessageBox.confirm("确认收回吗?收回后可再次提交该单据", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      loading.value = true;
      taskAPI
        .callBack(guid)
        .then(() => {
          ElMessage.success("操作成功");
          handleResetQuery();
        })
        .catch((error) => {
          ElMessage.error("操作失败，失败原因：" + error.message);
        })
        .finally(() => (loading.value = false));
    })
    .catch(() => {
      ElMessage.info("已取消收回");
    });
};

//————————————————————————————————————————————弹窗相关
// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});
/** 打开任务弹窗 */
const itemGuid = ref<string>("");
const itemId = ref<number | undefined>();
const itemDcbm = ref<string>("");
const itemEditable = ref<boolean>(false);
const itemNetcode = ref<string>("");
function handleOpenDialog(editable: boolean, row?: taskPageVO) {
  itemGuid.value = row?.guid || getGuid();
  itemId.value = row?.id;
  //itemNetcode.value = row?.netcode || "";
  itemEditable.value = editable;
  dialog.title = editable ? (row?.id ? "编辑任务单" : "新增任务单") : "查看任务单";
  dialog.visible = true;
  console.log("type.value", type.value);
}

function handleOpen(editable: boolean) {
  router.replace("/pub/adjustMarket?type=ly");
}
//批量更新方法
const batchUpdateVisible = ref(false);
const handleBatchUpdate = () => {
  if (removeIds.value.length === 0) {
    ElMessage.warning("请选择要盘点的资产");
    return;
  }
  batchUpdateVisible.value = true;
};

onMounted(() => {
  handleQuery();
});

watch(
  () => props.dcbm,
  (newValue) => {
    if (newValue) {
      console.log("dcbm", newValue);
      queryParams.sybm = newValue;
      handleQuery();
    }
  }
);

watch(
  () => props.syr,
  (newValue) => {
    if (newValue) {
      console.log("syr", newValue);
      queryParams.syr = newValue;
      handleQuery();
    }
  }
);
</script>

<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}

.flex {
  display: flex;
  flex-direction: row;
}
</style>
