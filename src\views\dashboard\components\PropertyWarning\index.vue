<template>
  <div>
    <el-card>
      <template #header>
        <div class="flex-x-between">
          <div class="flex-y-center">资产预警</div>
          <el-button
            type="primary"
            size="small"
            plain
            @click="router.push('/book/warning?type=view')"
          >
            更多
          </el-button>
        </div>
      </template>

      <ScrollbarLoop :height="props.height" :datacount="pageData.list.length">
        <div
          v-for="(item, index) in pageData.list"
          class="divItem"
          style="cursor: pointer"
          @click="hancleGoToDo(item.SYR, item.SYRNAME, item.SYRBMNAME, item.ZCBMNAME)"
        >
          {{ index + 1 }}
          <span class="red">{{ item.SYRNAME }}</span>
          已调至
          <span class="red">{{ item.SYRBMNAME }}</span>
          ， 但是仍有
          <span class="red">{{ item.ZCSL }}</span>
          件资产留在
          <span class="red">{{ item.ZCBMNAME }}</span>
        </div>
      </ScrollbarLoop>
    </el-card>

    <!-- 弹出框 资产预警清单 -->
    <el-dialog v-model="pageData.dv" title="资产预警列表" width="60%">
      <el-table
        v-loading="pageData.loading"
        :data="pageData.list"
        highlight-current-row
        :border="true"
        height="500px"
      >
        <el-table-column prop="SYRNAME" label="姓名" width="116" />
        <el-table-column prop="SYRBMNAME" label="现部门" width="275" />
        <el-table-column prop="ZCBMNAME" label="原部门" width="275" />
        <el-table-column prop="ZCSL" label="原部门资产(件)" width="150" />
        <el-table-column label="操作" fixed="right" width="100">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="
                hancleGoToDo(
                  scope.row.SYR,
                  scope.row.SYRNAME,
                  scope.row.SYRBMNAME,
                  scope.row.ZCBMNAME
                )
              "
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 列表 -->
    <el-dialog v-model="dataInfo.dv" title="资产预警未调拨列表" width="60%">
      <Title name="人员信息" />
      <el-descriptions v-loading="dataInfo.loading" :column="2" style="margin-left: 100px">
        <el-descriptions-item label="用户工号：">{{ dataInfo.SYR }}</el-descriptions-item>
        <el-descriptions-item label="原部门：">{{ dataInfo.ZCBMNAME }}</el-descriptions-item>
        <el-descriptions-item label="用户姓名：">{{ dataInfo.SYRNAME }}</el-descriptions-item>
        <el-descriptions-item label="现部门：">{{ dataInfo.SYRBMNAME }}</el-descriptions-item>
      </el-descriptions>

      <Title name="资产明细" />
      <el-table
        v-loading="dataInfo.loading"
        :data="dataInfo.list"
        highlight-current-row
        :border="true"
        height="280px"
      >
        <el-table-column type="index" label="序号" width="55" />
        <el-table-column prop="ZCBH" label="资产编号" width="110" />
        <el-table-column prop="ZCMC" label="资产名称" width="200" />
        <el-table-column prop="ZCBMNAME" label="使用部门" width="150" />
        <el-table-column prop="SL" label="数量" width="60" />
        <el-table-column prop="DJ" label="单价(元)" width="80" />
        <el-table-column prop="JE" label="原值(元)" width="90" />
        <el-table-column prop="CFDDNAME" label="存放地点" width="200" />
        <el-table-column label="操作" fixed="right" width="65">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleRowView(scope.row.GUID, scope.row.RKGUID)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-if="dataInfo.total > 0"
        v-model:total="dataInfo.total"
        v-model:page="yjzc_queryParams.pageNum"
        v-model:limit="yjzc_queryParams.pageSize"
        @pagination="YjPageQuery()"
      />
    </el-dialog>
  </div>
  <!-- 查看资产弹窗 -->
  <el-drawer v-model="itemVisible" append-to-body size="70%">
    <CardPanel :key="itemGuid" :guid="itemGuid" :rkguid="itemRkGuid" />
  </el-drawer>
</template>

<script setup lang="ts">
import dashboardAPI from "@/api/properties/dashboard";
import { useRouter } from "vue-router";

// 在 setup 中
const router = useRouter();

//组件参数
const props = defineProps({
  //模块高度，默认200
  height: {
    type: String,
    default: "200",
  },
});

const pageData = reactive({
  list: <any[]>[],
  dv: false,
  loading: false,
});

const dataInfo = reactive({
  list: <any[]>[],
  SYR: "",
  SYRNAME: "",
  SYRBMNAME: "",
  ZCBMNAME: "",
  dv: false,
  loading: false,
  total: 0,
});

/*const hancleGoToDo = (SYR: any, SYRNAME: any, SYRBMNAME: any, ZCBMNAME: any) => {
  dataInfo.dv = true;
  dataInfo.SYR = SYR;
  dataInfo.SYRBMNAME = SYRBMNAME;
  dataInfo.SYRNAME = SYRNAME;
  dataInfo.ZCBMNAME = ZCBMNAME;
  let queryParams = { zcbh: "", zcmc: "", syr: SYR, syrbm: "", zcbm: "" };
  dashboardAPI1.getPropertyWarningInfo(queryParams).then((res: any) => {
    //dataInfo.list.push(...res);
    dataInfo.list = res;
    console.log("propertywarning", res);
  });
};*/
const yjzc_queryParams = reactive<any>({
  zcbh: "",
  zcmc: "",
  syr: "",
  syrbm: "",
  zcbm: "",
  pageNum: 1,
  pageSize: 5,
});

function YjPageQuery() {
  console.log("发送");
  console.log(yjzc_queryParams);
  dashboardAPI
    .getPropertyWarningInfoPage(yjzc_queryParams)
    .then((res: any) => {
      //dataInfo.list.push(...res);
      dataInfo.list = res.list;
      dataInfo.total = res.total;
      console.log("getPropertyWarningInfoPage", res);
    })
    .catch((error) => {
      console.error("获取调拨资产数据失败:", error);
      ElMessage.error("获取未调拨资产数据失败");
      dataInfo.list = [];
      dataInfo.total = 0;
    })
    .finally(() => {
      dataInfo.loading = false;
    });
}

const hancleGoToDo = (SYR: any, SYRNAME: any, SYRBMNAME: any, ZCBMNAME: any) => {
  dataInfo.dv = true;
  dataInfo.SYR = SYR;
  dataInfo.SYRBMNAME = SYRBMNAME;
  dataInfo.SYRNAME = SYRNAME;
  dataInfo.ZCBMNAME = ZCBMNAME;
  yjzc_queryParams.syr = SYR;
  yjzc_queryParams.pageNum = 1;
  YjPageQuery();
};

//弹窗相关参数
const itemVisible = ref(false);
const itemGuid = ref("");
const itemRkGuid = ref("");
const handleRowView = (guid: string, rkguid: string) => {
  if (!guid) {
    ElMessage.warning("资产编号不能为空");
    return;
  }
  itemGuid.value = guid;
  itemVisible.value = true;
  itemRkGuid.value = rkguid;
};

onMounted(() => {
  dashboardAPI.getPropertyWarningList().then((res: any) => {
    //pageData.list.push(...res);
    pageData.list = res;
    console.log("propertywarning", res);
  });
});
</script>
<style lang="scss" scoped>
.divItem {
  margin-bottom: 12px;
  line-height: 30px;
  border-bottom: 1px solid #cccccc;
  font-size: 14px;
}

.red {
  color: red;
  font-weight: bold;
}
</style>
../../api/workflow
