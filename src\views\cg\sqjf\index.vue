<template>
  <div class="app-container">
   <el-card>
    <!-- ********************** 用插槽显示筛选框 ********************** -->
    <template #header>
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
                <el-form-item label="外键，自增" prop="sqid">
                      <el-input
                          v-model="queryParams.sqid"
                          placeholder="外键，自增"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="zxcg_sq.guid" prop="sqguid">
                      <el-input
                          v-model="queryParams.sqguid"
                          placeholder="zxcg_sq.guid"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon><Search /></template>
            搜索
          </el-button>
          <el-button @click="handleResetQuery">
            <template #icon><Refresh /></template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="列表">
        <div>
          <el-button
              v-hasPerm="['cg:zxcgSqjf:add']"
              type="success"
              plain
              size="small"
              @click="formType.view = false; handleEdit()"
          >
            <template #icon><Plus /></template>
            新增
          </el-button>
          <el-button
              v-hasPerm="['cg:zxcgSqjf:delete']"
              type="danger"
              plain
              size="small"
              :disabled="removeIds.length === 0"
              @click="handleDelete()"
          >
            <template #icon><Delete /></template>
            删除
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
          ref="dataTableRef"
          v-loading="loading"
          :data="pageData"
          highlight-current-row
          border
          @selection-change="handleSelectionChange"
          @expand-change="handleExpandChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed />
        <el-table-column label="展开" type="expand" width="55">
          <template #default="props">
            <div style="padding: 0px 70px; background-color: #ffffff">
              <!-- <TransferPropertiesList
                :dcbm="props.row.dcbm"
                :guid="props.row.guid || ''"
              ></TransferPropertiesList> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
                <el-table-column
                    key="jfid"
                    label="主键，自增"
                    prop="jfid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sqid"
                    label="外键，自增"
                    prop="sqid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysxmid"
                    label="预算项目ID"
                    prop="ysxmid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zgbm"
                    label="经费类别(专项、基本、科研、自筹)"
                    prop="zgbm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="jflb"
                    label="主管部门"
                    prop="jflb"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="jfnd"
                    label="经费年度"
                    prop="jfnd"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="jfbh"
                    label="经费代码"
                    prop="jfbh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="jfmc"
                    label="经费名称"
                    prop="jfmc"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cwjfbh"
                    label="财务经费代码"
                    prop="cwjfbh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cwjfmc"
                    label="财务经费名称"
                    prop="cwjfmc"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ishs"
                    label="是否含税(1是，0否)"
                    prop="ishs"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="jhje"
                    label="计划金额"
                    prop="jhje"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysje"
                    label="可用金额"
                    prop="ysje"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sjzc"
                    label="实际支出（元）"
                    prop="sjzc"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="kyje"
                    label="可用金额"
                    prop="kyje"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sqguid"
                    label="zxcg_sq.guid"
                    prop="sqguid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="jhid"
                    label="采购计划ID"
                    prop="jhid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="yszje"
                    label="经费总金额"
                    prop="yszje"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="syje"
                    label="作废"
                    prop="syje"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="jflbname"
                    label="经费类别名称"
                    prop="jflbname"
                    min-width="150"
                    align="center"
                />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button
                v-hasPerm="['cg:zxcgSqjf:edit']"
                type="primary"
                size="small"
                icon="Edit"
                link
                @click="formType.view = false;handleRowEdit(scope.row.id)"
            >
              编辑
            </el-button>
            <el-button
                v-hasPerm="['cg:zxcgSqjf:delete']"
                type="danger"
                size="small"
                icon="Delete"
                link
                @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
            <el-button
              v-if="check"
              type="primary"
              icon="DocumentChecked"
              size="small"
              link
              @click="hancleRowCheck"
            >
              审核
            </el-button>
            <el-button
              v-if="view"
              type="success"
              icon="Document"
              size="small"
              link
              @click="hancleRowView"
            >
              查看
            </el-button>
            <el-button
              v-if="view"
              type="primary"
              icon="Printer"
              size="small"
              link
              @click="formType.view = true;hancleRowPrint"
            >
              打印
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="handleQuery()"
      />
    </el-card>

    <!-- 申请经费表单弹窗 -->   
    <el-drawer 
      v-model="dialog.visible" 
      :title="dialog.title" 
      append-to-body 
      size="75%"
      :before-close="handleCloseDialog"

    >
<el-card>
        <template #header>
          <Title name="XX信息">
            <div>
              <el-button
                v-if="!formType.view"
                type="primary"
                @click="handleSubmit()"
              >
                保存
              </el-button>
              <el-button v-if="!formType.view" type="danger" @click="handleSubmit()">
                提交
              </el-button>
            </div>
          </Title>
        </template>



      <el-form ref="dataFormRef" :model="formData" :rules="rules" label-width="100px"  :inline="true"
          :disabled="formType.view">
                <el-form-item label="主键，自增" prop="jfid">
                      <el-input
                          v-model="formData.jfid"
                          placeholder="主键，自增"
                      />
                </el-form-item>
                <el-form-item label="外键，自增" prop="sqid">
                      <el-input
                          v-model="formData.sqid"
                          placeholder="外键，自增"
                      />
                </el-form-item>
                <el-form-item label="预算项目ID" prop="ysxmid">
                      <el-input
                          v-model="formData.ysxmid"
                          placeholder="预算项目ID"
                      />
                </el-form-item>
                <el-form-item label="经费类别(专项、基本、科研、自筹)" prop="zgbm">
                      <el-input
                          v-model="formData.zgbm"
                          placeholder="经费类别(专项、基本、科研、自筹)"
                      />
                </el-form-item>
                <el-form-item label="主管部门" prop="jflb">
                      <el-input
                          v-model="formData.jflb"
                          placeholder="主管部门"
                      />
                </el-form-item>
                <el-form-item label="经费年度" prop="jfnd">
                      <el-input
                          v-model="formData.jfnd"
                          placeholder="经费年度"
                      />
                </el-form-item>
                <el-form-item label="经费代码" prop="jfbh">
                      <el-input
                          v-model="formData.jfbh"
                          placeholder="经费代码"
                      />
                </el-form-item>
                <el-form-item label="经费名称" prop="jfmc">
                      <el-input
                          v-model="formData.jfmc"
                          placeholder="经费名称"
                      />
                </el-form-item>
                <el-form-item label="财务经费代码" prop="cwjfbh">
                      <el-input
                          v-model="formData.cwjfbh"
                          placeholder="财务经费代码"
                      />
                </el-form-item>
                <el-form-item label="财务经费名称" prop="cwjfmc">
                      <el-input
                          v-model="formData.cwjfmc"
                          placeholder="财务经费名称"
                      />
                </el-form-item>
                <el-form-item label="是否含税(1是，0否)" prop="ishs">
                      <el-input
                          v-model="formData.ishs"
                          placeholder="是否含税(1是，0否)"
                      />
                </el-form-item>
                <el-form-item label="计划金额" prop="jhje">
                      <el-input
                          v-model="formData.jhje"
                          placeholder="计划金额"
                      />
                </el-form-item>
                <el-form-item label="可用金额" prop="ysje">
                      <el-input
                          v-model="formData.ysje"
                          placeholder="可用金额"
                      />
                </el-form-item>
                <el-form-item label="实际支出（元）" prop="sjzc">
                      <el-input
                          v-model="formData.sjzc"
                          placeholder="实际支出（元）"
                      />
                </el-form-item>
                <el-form-item label="可用金额" prop="kyje">
                      <el-input
                          v-model="formData.kyje"
                          placeholder="可用金额"
                      />
                </el-form-item>
                <el-form-item label="zxcg_sq.guid" prop="sqguid">
                      <el-input
                          v-model="formData.sqguid"
                          placeholder="zxcg_sq.guid"
                      />
                </el-form-item>
                <el-form-item label="采购计划ID" prop="jhid">
                      <el-input
                          v-model="formData.jhid"
                          placeholder="采购计划ID"
                      />
                </el-form-item>
                <el-form-item label="经费总金额" prop="yszje">
                      <el-input
                          v-model="formData.yszje"
                          placeholder="经费总金额"
                      />
                </el-form-item>
                <el-form-item label="作废" prop="syje">
                      <el-input
                          v-model="formData.syje"
                          placeholder="作废"
                      />
                </el-form-item>
                <el-form-item label="经费类别名称" prop="jflbname">
                      <el-input
                          v-model="formData.jflbname"
                          placeholder="经费类别名称"
                      />
                </el-form-item>
      </el-form>
      <template #footer></template>
     </el-card>
    </ el-drawer>
  </div>
</template>

<script setup lang="ts">
  defineOptions({
    name: "ZxcgSqjf",
    inheritAttrs: false,
  });
  import { useUserStore } from "@/store";
  import ZxcgSqjfAPI, { ZxcgSqjfPageVO, ZxcgSqjfForm, ZxcgSqjfPageQuery } from "@/api/cg/zxcg-sqjf";

  // 获取路由参数,view/add/check
  const route = useRoute();
  const type = ref(route.query.type?.toString() || "view");
  //显示隐藏权限,用于v-if
  const add = type.value == "add";
  const view = type.value == "view";
  const check = type.value == "check";
  //页面参数
  const queryFormRef = ref(ElForm);
  const loading = ref(false);
  const removeIds = ref<number[]>([]);
  const total = ref(0);

  const queryParams = reactive<ZxcgSqjfPageQuery>({
    pageNum: 1,
    pageSize: 10,
  });

  // 申请经费表格数据
  const pageData = ref<ZxcgSqjfPageVO[]>([]);

  // 弹窗
  const dialog = reactive({
    title: "",
    visible: false,
  });



  /** 查询申请经费 */
  function handleQuery() {
    loading.value = true;
          ZxcgSqjfAPI.getPage(queryParams)
        .then((data) => {
          pageData.value = data.list;
          total.value = data.total;
        })
        .finally(() => {
          loading.value = false;
        });
  }

  /** 重置申请经费查询 */
  function handleResetQuery() {
    queryFormRef.value!.resetFields();
    queryParams.pageNum = 1;
    handleQuery();
  }

  /** 行复选框选中记录选中ID集合 */
  function handleSelectionChange(selection: any) {
    removeIds.value = selection.map((item: any) => item.id);
  }

  /** 打开申请经费弹窗 */
  function handleEdit(id?: number) {
    dialog.visible = true;
    if (id) {
      dialog.title = "修改申请经费";
            ZxcgSqjfAPI.getFormData(id).then((data) => {
        Object.assign(formData, data);
      });
    } else {
      dialog.title = "新增申请经费";
    }
  }

 

  /** 关闭申请经费弹窗 */
  function handleCloseDialog() {
    dialog.visible = false;
    dataFormRef.value.resetFields();
    dataFormRef.value.clearValidate();
    Object.assign(formData, {});

  }

  /** 删除申请经费 */
  function handleDelete(id?: number) {
    const ids = [id || removeIds.value].join(",");
    if (!ids) {
      ElMessage.warning("请勾选删除项");
      return;
    }

    ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(
        () => {
          loading.value = true;
                ZxcgSqjfAPI.deleteByIds(ids)
              .then(() => {
                ElMessage.success("删除成功");
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        },
        () => {
          ElMessage.info("已取消删除");
        }
    );
  }

  onMounted(() => {
    handleQuery();
  });

  //--------------以下是form表单相关
const dataFormRef = ref(ElForm);

  // 申请经费表单数据
  const formData = reactive<ZxcgSqjfForm>({});

const formType = reactive({
  add: false,
  edit: false,
  view: false,
});

  // 申请经费表单校验规则
  const rules = reactive({
                      sqid: [{ required: true, message: "请输入外键，自增", trigger: "blur" }],
                      ysxmid: [{ required: true, message: "请输入预算项目ID", trigger: "blur" }],
                      zgbm: [{ required: true, message: "请输入经费类别(专项、基本、科研、自筹)", trigger: "blur" }],
                      sqguid: [{ required: true, message: "请输入zxcg_sq.guid", trigger: "blur" }],
                      jhid: [{ required: true, message: "请输入采购计划ID", trigger: "blur" }],
                      yszje: [{ required: true, message: "请输入经费总金额", trigger: "blur" }],
                      syje: [{ required: true, message: "请输入作废", trigger: "blur" }],
  });

   /** 提交申请经费表单 */
  function handleSubmit() {
    dataFormRef.value.validate((valid: any) => {
      if (valid) {
        loading.value = true;
        const id = formData.id;
        if (id) {
                ZxcgSqjfAPI.update(id, formData)
              .then(() => {
                ElMessage.success("修改成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        } else {
                ZxcgSqjfAPI.add(formData)
              .then(() => {
                ElMessage.success("新增成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        }
      }
    });
  }
</script>
<style lang="scss" scoped>

</style>
