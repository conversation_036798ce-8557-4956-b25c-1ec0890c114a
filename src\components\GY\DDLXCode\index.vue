<template>
  <!-- 支持搜索 -->
  <el-select
    v-model="_modelValue"
    filterable
    :multiple="props.multiple"
    :placeholder="placeholder"
    :loading="loading"
    :disabled="disabled"
    :clearable="clearable"
    :size="size"
    @filter-change="handleFilterChange"
  >
    <el-option v-for="item in pageData" :key="item.xcode" :label="item.name" :value="item.xcode" />
    <template v-if="error" #empty>
      <el-empty :description="error" />
    </template>
  </el-select>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import SysApi, { XCodeVO } from "@/api/system/sys";
import { useDebounceFn } from "@vueuse/core";

interface Props {
  /** XCode identifier for the dropdown */
  xcode: string;
  /** Selected value(s) */
  modelValue: string | string[];
  /** Enable multiple selection */
  multiple?: boolean;
  /** Placeholder text */
  placeholder?: string;
  /** Disabled state */
  disabled?: boolean;
  /** Allow clearing selection */
  clearable?: boolean;
  /** Component size */
  size?: "large" | "default" | "small";
  /** Page size for data fetching */
  pageSize?: number;
  flag?: string;
  xlen?: number;
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  placeholder: "请选择",
  disabled: false,
  clearable: true,
  size: "default",
  pageSize: 100,
});

const emit = defineEmits<{
  "update:modelValue": [value: string | string[]];
}>();

const pageData = ref<XCodeVO[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);

const _modelValue = computed({
  get: () => props.modelValue,
  set: (val) => emit("update:modelValue", val),
});

// Fetch data with error handling
const fetchData = async (searchQuery?: string) => {
  loading.value = true;
  error.value = null;

  try {
    if (props.xcode) {
      const params = {
        pageNum: 1,
        pageSize: props.pageSize,
        preXCode: props.xcode,
        searchQuery,
        flag: props.flag,
        xlen: props.xlen,
      };

      const res = await SysApi.getXcodeList(params);
      pageData.value = res.list || [];
    } else {
      pageData.value = [];
    }
  } catch (e) {
    error.value = "Failed to load data";
    console.error("Error fetching XCode data:", e);
  } finally {
    loading.value = false;
  }
};

// Debounced search handler
const handleFilterChange = useDebounceFn((value: string) => {
  fetchData(value);
}, 300);

// Initial data load
onMounted(() => {
  fetchData();
});

watch(
  () => props.xcode,
  (newVal) => {
    console.log("我变了");
    // if (newVal && newVal != "")
    {
      fetchData();
    }
  }
);
</script>

<style scoped>
.el-select {
  width: 100%;
}
</style>
