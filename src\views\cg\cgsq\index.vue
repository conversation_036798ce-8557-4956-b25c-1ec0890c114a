<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item label="申请名称" prop="sqmc">
            <el-input
              v-model="queryParams.sqmc"
              placeholder="申请名称"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item label="申请人姓名" prop="sqrname">
            <el-input
              v-model="queryParams.sqrname"
              placeholder="申请人姓名"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item label="申请部门名称" prop="sqbmmc">
            <el-input
              v-model="queryParams.sqbmmc"
              placeholder="申请部门名称"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon><Search /></template>
              搜索
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon><Refresh /></template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="采购申请列表">
        <div>
          <el-button
            type="success"
            plain
            size="small"
            @click="
              formType.view = false;
              handleOpenDialog(true, 'xm');
            "
          >
            <template #icon><Plus /></template>
            新增项目采购申请
          </el-button>
          <el-button
            type="success"
            plain
            size="small"
            @click="
              formType.view = false;
              handleOpenDialog(true, 'lx');
            "
          >
            <template #icon><Plus /></template>
            新增零星采购申请
          </el-button>
          <el-button
            type="success"
            plain
            size="small"
            @click="
              formType.view = false;
              handleOpenDialog(true, 'wx');
            "
          >
            <template #icon><Plus /></template>
            新增零星维修申请
          </el-button>

          <el-button
            type="danger"
            plain
            size="small"
            :disabled="removeIds.length === 0"
            @click="handleDelete()"
          >
            <template #icon><Delete /></template>
            删除
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
        @expand-change="handleExpandChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed />

        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>

        <el-table-column key="sqmc" label="项目名称" prop="sqmc" min-width="300" align="center" />
        <el-table-column key="xmbm" label="经费卡号" prop="xmbm" min-width="120" align="center" />
        <el-table-column
          key="ysje"
          label="预算金额(元)"
          prop="ysje"
          min-width="120"
          align="center"
        />

        <el-table-column
          key="cgml"
          label="采购类别"
          prop="cgml"
          min-width="100"
          align="center"
          :formatter="(row) => (row.cgml === 'A' ? '货物' : row.cgml === 'B' ? '工程' : '服务')"
        />

        <el-table-column
          key="sqrname"
          label="申请人"
          prop="sqrname"
          min-width="110"
          align="center"
        />
        <el-table-column key="sqsj" label="申请时间" prop="sqsj" min-width="160" align="center" />

        <el-table-column
          key="sqbmmc"
          label="申请部门名称"
          prop="sqbmmc"
          min-width="160"
          align="center"
        />

        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              icon="Edit"
              link
              @click="
                formType.view = false;
                handleOpenDialog(true, '', scope.row);
              "
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              icon="Delete"
              link
              @click="handleDelete(scope.row.sqid)"
            >
              删除
            </el-button>
            <el-button
              v-if="check"
              type="primary"
              icon="DocumentChecked"
              size="small"
              link
              @click="hancleRowCheck"
            >
              审核
            </el-button>
            <el-button
              v-if="view"
              type="success"
              icon="Document"
              size="small"
              link
              @click="handleOpenDialog(false, '', scope.row)"
            >
              查看
            </el-button>
            <el-button
              v-if="view"
              type="primary"
              icon="Printer"
              size="small"
              link
              @click="
                formType.view = true;
                hancleRowPrint;
              "
            >
              打印
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 采购申请表单弹窗 -->
    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      append-to-body
      size="80%"
      :before-close="handleCloseDialog"
    >
      <el-card>
        <!--采购申请组件-->
        <SqEdit
          :sqid="itemId"
          :RefreshFatherDrawer="SqRefreshFatherDrawer"
          ref="SqRef"
          :editable="itemEditable"
          :key="itemGuid + '1'"
          :guid="itemGuid"
          :sqcgfl="SqCgfl"
        />
        <el-button
          v-if="!formType.view"
          type="primary"
          @click="handleSaveSq()"
          style="display: none"
        >
          保存
        </el-button>
      </el-card>
    </el-drawer>
  </div>
</template>

<style>
.custom-radio {
  width: 40px; /* 设置宽度 */
  display: inline-block; /* 确保宽度生效   */
}
</style>
<script setup lang="ts">
defineOptions({
  name: "ZxcgSq",
  inheritAttrs: false,
});
import { useTagsViewStore, useUserStore } from "@/store";
import ZxcgSqAPI, { ZxcgSqPageVO, ZxcgSqForm, ZxcgSqPageQuery } from "@/api/cg/zxcg-sq";

import { getGuid } from "@/utils/guid";

import SqEdit from "@/views/cg/cgsq/components/SqEdit.vue";

const userStore = useUserStore();
// 获取路由参数,view/add/check
const route = useRoute();
const type = ref(route.query.type?.toString() || "view");
//显示隐藏权限,用于v-if
const add = type.value == "add";
const view = type.value == "view";
const check = type.value == "check";
//页面参数
const queryFormRef = ref(ElForm);
const loading = ref(false);
const removeIds = ref<number[]>([]);
const total = ref(0);

const itemGuid = ref<string | undefined>();
const itemId = ref<number | undefined>();
const itemEditable = ref<boolean>(false);
const SqCgfl = ref<string | undefined>();
const queryParams = reactive<ZxcgSqPageQuery>({
  pageNum: 1,
  pageSize: 10,
});

const formType = reactive({
  add: false,
  edit: false,
  view: false,
});

// 采购申请表格数据1
const pageData = ref<ZxcgSqPageVO[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

/** 查询采购申请 */
function handleQuery() {
  loading.value = true;
  ZxcgSqAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置采购申请查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}
function SqRefreshFatherDrawer(close?: boolean) {
  //是否关闭弹窗由参数决定，默认不关闭
  if (close) {
    dialog.visible = false;
  }
  handleResetQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  removeIds.value = selection.map((item: any) => item.sqid);
}

/** 删除采购申请 */
function handleDelete(id?: number) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      ZxcgSqAPI.deleteByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

/*采购申请弹窗--------------------------------------------------------*/
//const sqRef = ref<SqEdit | null>(null);

//const sqRef = ref<InstanceType<typeof SqEdit> | null>(null);
const sqRef = ref<any | null>(null);
const handleOpenDialog = async (editable: boolean, sqcgfl: string, row?: ZxcgSqPageVO) => {
  //await dataFormRef.value?.validate();
  loading.value = true;
  itemGuid.value = row?.guid || getGuid();
  itemId.value = row?.sqid;
  itemEditable.value = editable;
  dialog.title = editable ? (row?.sqid ? "修改采购申请" : "新增采购申请") : "查看采购申请";
  dialog.visible = true;
  if (row) {
  } else {
    SqCgfl.value = sqcgfl;
    // console.log("SqCgfl.value", SqCgfl.value);
  }

  loading.value = false;
};
function handleCloseDialog() {
  dialog.visible = false;
  itemId.value = undefined;
  if (sqRef.value) {
    //await
    sqRef?.value.handleClearForm();
  }
}
const handleSaveSq = async () => {
  console.log("sqRef.value:", sqRef.value);
  if (sqRef.value) {
    console.log("我加载了，");
  }
};

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped></style>
