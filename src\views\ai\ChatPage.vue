<template>
  <div class="chat-page">
    <div class="chat-header">
      <el-button type="primary" @click="backToList">
        <el-icon><Back /></el-icon>
        返回列表
      </el-button>
    </div>

    <chat-interface
      :agent-id="agentId"
      @session-created="handleSessionCreated"
      @session-ended="handleSessionEnded"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { Back } from "@element-plus/icons-vue";
import ChatInterface from "./components/ChatInterface.vue";

defineOptions({
  name: "ChatPage",
});

// 路由实例
const route = useRoute();
const router = useRouter();

// 状态变量
const agentId = ref<string>("");
const currentSessionId = ref<string>("");

// 返回智能体列表页面
const backToList = () => {
  // 如果有活跃会话，可以提示用户是否确认离开
  if (currentSessionId.value) {
    // 这里可以添加确认对话框，但为简化示例，直接返回
  }
  router.push("/agent/platform");
};

// 处理会话创建
const handleSessionCreated = (sessionId: string) => {
  currentSessionId.value = sessionId;
};

// 处理会话结束
const handleSessionEnded = () => {
  currentSessionId.value = "";
};

// 组件挂载时获取路由参数
onMounted(() => {
  const queryAgentId = route.query.agentId as string;
  console.log("queryAgentId", queryAgentId);
  if (!queryAgentId) {
    ElMessage.warning("未指定智能体，将返回列表页");
    router.push("/agent/platform");
    return;
  }
  agentId.value = queryAgentId;
});
</script>

<style lang="scss" scoped>
.chat-page {
  padding: 20px;

  .chat-header {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
  }
}
</style>
