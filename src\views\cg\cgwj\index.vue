<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item label="文件编号" prop="wjbh">
            <el-input
              v-model="queryParams.wjbh"
              placeholder="文件编号"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item label="文件名称" prop="wjmc">
            <el-input
              v-model="queryParams.wjmc"
              placeholder="文件名称"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item label="采购形式" prop="cgxs">
            <DDLXcode v-model="queryParams.cgxs" xcode="021502" clearable />
          </el-form-item>
          <el-form-item label="采购方式" prop="cgfs">
            <DDLXcode v-model="queryParams.cgfs" xcode="queryParams.cgxs" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon><Search /></template>
              搜索
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon><Refresh /></template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="列表">
        <div>
          <el-button
            v-if="false"
            type="success"
            plain
            size="small"
            @click="handleOpenDialog(false)"
          >
            <template #icon><Plus /></template>
            新增
          </el-button>
          <el-button
            v-if="add"
            type="danger"
            plain
            size="small"
            :disabled="removeIds.length === 0"
            @click="handleDelete()"
          >
            <template #icon><Delete /></template>
            删除
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column v-if="add" type="selection" width="55" align="center" fixed />
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          key="qrsbh"
          label="确认书编号"
          prop="qrsbh"
          min-width="150"
          align="center"
        />
        <el-table-column key="wjbh" label="文件编号" prop="wjbh" min-width="150" align="center" />
        <el-table-column key="wjmc" label="文件名称" prop="wjmc" min-width="150" align="center" />
        <el-table-column key="lxr" label="联系人" prop="lxr" min-width="150" align="center" />
        <el-table-column key="lxfs" label="联系方式" prop="lxfs" min-width="150" align="center" />
        <el-table-column
          key="cgxs"
          label="采购形式"
          prop="cgxsname"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="cgfs"
          label="采购方式"
          prop="cgfsname"
          min-width="150"
          align="center"
        />
        <el-table-column key="ysjf" label="预算金额" prop="ysjf" min-width="150" align="center" />
        <el-table-column key="djsj" label="登记时间" prop="djsj" min-width="150" align="center">
          <template #default="scope">
            <span>{{ format(scope.row.djsj, "YYYY-MM-DD") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          key="netcode"
          label="数据状态"
          prop="netcodename"
          min-width="100"
          align="center"
        />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button
              v-if="add"
              type="primary"
              size="small"
              icon="Edit"
              link
              @click="handleOpenDialog(true, scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="add"
              type="danger"
              size="small"
              icon="Delete"
              link
              @click="handleDelete(scope.row.guid)"
            >
              删除
            </el-button>
            <el-button
              v-if="check"
              type="primary"
              icon="DocumentChecked"
              size="small"
              link
              @click="handleOpenDialog(false, scope.row)"
            >
              审核
            </el-button>
            <el-button
              type="success"
              icon="Document"
              size="small"
              link
              @click="handleOpenDialog(false, scope.row)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 采购文件表单弹窗 -->
    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      append-to-body
      size="75%"
      :before-close="handleCloseDialog"
    >
      <el-card v-if="dialog.editEnable">
        <el-steps
          style="width: 100%"
          :active="dialog.active"
          finish-status="success"
          title="创建步骤"
          simple
        >
          <el-step title="1.采购实施计划" />
          <el-step title="2.标项创建" />
          <el-step title="3.采购文件编制" />
        </el-steps>
        <wj-info
          v-if="dialog.active == 1"
          :key="dialog.guid"
          :guid="dialog.guid"
          :editable="dialog.editEnable"
          @nowstep="handleNowstep"
        />
        <wjbd-info
          v-if="dialog.active == 2"
          :key="dialog.wjid"
          :wjid="dialog.wjid"
          :editable="dialog.editEnable"
          @nowstep="handleNowstep"
        />
        <wjfj-info
          v-if="dialog.active == 3"
          :key="dialog.guid"
          :guid="dialog.guid"
          :editable="dialog.editEnable"
          @nowstep="handleNowstep"
        />
      </el-card>
      <el-card v-if="!dialog.editEnable">
        <Cgwjview
          :key="itemGuid"
          :wjid="itemId"
          :guid="itemGuid"
          :netcode="itemNetcode"
          :editable="itemEditable"
          :dcbm="itemDcbm"
          :RefreshFatherDrawer="RefreshFatherDrawer"
        />
      </el-card>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "ZxcgCgwj",
  inheritAttrs: false,
});
import { useUserStore } from "@/store";
import ZxcgCgwjAPI, { ZxcgCgwjPageVO, ZxcgCgwjForm, ZxcgCgwjPageQuery } from "@/api/cg/zxcg-cgwj";
import { useRoute } from "vue-router";
import { format } from "@/utils/day";
import Cgwjview from "./components/cgwjview.vue";
import { getGuid } from "@/utils/guid";
import { it } from "node:test";

// 获取路由参数,view/add/check
const route = useRoute();
const type = ref(route.query.type?.toString() || "view");
//显示隐藏权限,用于v-if
const add = type.value == "edit";
const view = type.value == "view";
const check = type.value == "sp";
//页面参数
const queryFormRef = ref(ElForm);
const loading = ref(false);
const removeIds = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<ZxcgCgwjPageQuery>({
  pageNum: 1,
  pageSize: 10,
});
queryParams.pagetype = type.value;

// 采购文件表格数据
const pageData = ref<ZxcgCgwjPageVO[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  guid: "",
  wjid: 0,
  visible: false,
  editEnable: false,
  active: 1,
});

/** 查询采购文件 */
function handleQuery() {
  loading.value = true;
  ZxcgCgwjAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置采购文件查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  removeIds.value = selection.map((item: any) => item.id);
}
function RefreshFatherDrawer(close?: boolean) {
  //是否关闭弹窗由参数决定，默认不关闭
  if (close) {
    dialog.visible = false;
  }
  // itemId.value = undefined;
  handleQuery();
}

/** 打开采购文件弹窗 */
const itemGuid = ref<string>("");
const itemId = ref<number | undefined>();
const itemNetcode = ref<string>("");
const itemDcbm = ref<string>("");
const itemEditable = ref<boolean>(false);
function handleOpenDialog(editEnable: boolean, row?: ZxcgCgwjPageVO) {
  itemGuid.value = row?.guid || getGuid();
  itemId.value = row?.wjid;
  itemNetcode.value = row?.netcode || "";
  itemEditable.value = editEnable;
  dialog.wjid = row?.wjid || 0;
  dialog.visible = true;
  dialog.editEnable = editEnable;
  dialog.active = 1;
  console.log("handleOpenDialog", editEnable, row?.guid);
  if (row?.guid) {
    dialog.title = "修改采购文件";
    dialog.guid = row?.guid;
  } else {
    dialog.title = "新增采购文件";
    dialog.guid = "";
  }
}
const handleNowstep = (newName: number) => {
  dialog.active = newName;
};
/** 关闭采购文件弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  handleQuery();
}

/** 删除采购文件 */
function handleDelete(id?: number) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      ZxcgCgwjAPI.deleteByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped></style>
