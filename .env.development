# 应用端口
VITE_APP_PORT=3000

# 代理前缀
VITE_APP_BASE_API=/dev-api

# 接口地址
VITE_APP_PREVIEW_API=/api/v1/files/preview
#VITE_APP_API_URL=http://*************:8990
#VITE_APP_API_URL=http://**************:8990
#傅鹏郡
#VITE_APP_API_URL=http://**************:8990
#VITE_APP_API_URL=http://***************:8990
#服务器
#VITE_APP_API_URL=http://*************:8991/
#localhost
VITE_APP_API_URL=http://127.0.0.1:8990

# WebSocket 端点（不配置则关闭），线上 ws://api.youlai.tech/ws ，本地 ws://localhost:8989/ws
VITE_APP_WS_ENDPOINT=

# 启用 Mock 服务
VITE_MOCK_DEV_SERVER=false


# 是否启动用户快捷登录功能
VITE_APP_QUICK_LOGIN=true

