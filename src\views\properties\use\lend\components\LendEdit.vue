<!-- cursor测试:1 -->
<template>
  <div>
    <Title name="借用单信息">
      <div v-if="editable">
        <el-button type="primary" @click="handleSave()">保存</el-button>
        <el-button type="danger" @click="handleSubmit()">提交</el-button>
      </div>
    </Title>

    <el-form
      ref="dataFormRef"
      :disabled="!props.editable"
      :inline="true"
      :model="formData"
      :rules="rules"
      label-width="auto"
    >
      <el-form-item label="借用单号" prop="sqdh">
        <el-input v-model="formData.sqdh" placeholder="系统自动生成" disabled />
      </el-form-item>
      <el-form-item label="预计借用天数" prop="by2">
        <el-input v-model="formData.by2" placeholder="预计借用天数" />
      </el-form-item>
      <el-form-item label="申请部门" prop="djbmbm">
        <DDLDeptList v-model="formData.djbmbm" :disabled="true" />
      </el-form-item>
      <el-form-item label="申请人" prop="djr">
        <DDLUserList
          :key="formData.djbmbm"
          v-model="formData.djr"
          :disabled="true"
          :dcode="formData.djbmbm"
        />
      </el-form-item>
      <el-form-item label="申请时间" prop="djsj">
        <el-input v-model="formData.djsj" disabled />
      </el-form-item>
      <el-form-item label="借出部门" prop="sybm">
        <DDLDeptList v-model="formData.sybm" :disabled="true" />
      </el-form-item>
      <el-form-item label="借出人" prop="syr">
        <DDLUserList
          :key="formData.sybm"
          v-model="formData.syr"
          :disabled="true"
          :dcode="formData.sybm"
        />
      </el-form-item>
      <el-form-item label="借出管理员" prop="bmzcgly">
        <DDLUserList
          :key="formData.sybm"
          v-model="formData.bmzcgly"
          :disabled="true"
          :dcode="formData.sybm"
        />
      </el-form-item>

      <el-form-item label="用途说明" prop="ytsm" style="width: 800px">
        <el-input
          v-model="formData.ytsm"
          type="textarea"
          placeholder="用途说明"
          maxlength="200"
          show-word-limit
          :rows="5"
        />
      </el-form-item>

      <el-form-item label="备注" prop="notes" style="width: 800px">
        <el-input
          v-model="formData.notes"
          type="textarea"
          placeholder="备注"
          maxlength="200"
          show-word-limit
          :rows="5"
        />
      </el-form-item>
    </el-form>

    <Title name="借用资产清单">
      <div>
        <el-button
          v-if="editable"
          plain
          type="danger"
          icon="plus"
          @click="handleAddTransferPorperties"
        >
          添加借用资产
        </el-button>
      </div>
    </Title>

    <el-table v-loading="loading" :data="pageData" highlight-current-row :border="true" stripe>
      <el-table-column type="index" label="序号" width="55" />
      <el-table-column label="资产编号" prop="zcbh" width="120" />
      <el-table-column label="资产名称" prop="zcmc" width="200" />
      <el-table-column label="金额(元)" prop="je" width="100" />
      <el-table-column label="数量" prop="sl" min-width="80" />
      <el-table-column label="出借人" prop="syrname" width="200" />
      <el-table-column label="出借部门" prop="sybmname" width="300" />
      <!-- ********************** 操作列 **********************
         按钮：查询View；编辑Edit；删除Delete -->
      <el-table-column label="操作" fixed="right" width="150">
        <template #default="scope">
          <el-button
            type="primary"
            icon="View"
            size="small"
            link
            @click="handleRowView(scope.row.zcGuid, scope.row.rkguid)"
          >
            查看
          </el-button>
          <el-button
            v-if="editable"
            type="danger"
            icon="Delete"
            size="small"
            link
            @click="handleRowDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- ********************** 翻页 ********************** -->
    <pagination
      v-if="total > 10"
      v-model:total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="handlePageQuery"
    />

    <!-- 选择资产弹窗 -->
    <el-drawer v-model="choosedialog.visible" :title="choosedialog.title" append-to-body size="65%">
      <lendChoose :key="keyId" :dcode="formData.sybm" :handleReturnConfirm="handleReturnConfirm" />
    </el-drawer>
    <!-- 查看资产弹窗 -->
    <el-drawer v-model="itemVisible" append-to-body size="70%">
      <CardPanel :key="itemGuid" :guid="itemGuid" :rkguid="itemRkGuid" />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store";
import lendAPI from "@/api/properties/lend";
import lendChoose from "@/views/properties/use/lend/components/LendChoose.vue";
import { ElLoading } from "element-plus";
const keyId = ref(0);
const props = defineProps({
  //查询详情的借用单ID和guid
  id: {
    type: Number,
  },
  guid: {
    type: String,
    required: true,
  },
  //保存成功后需要关闭抽屉并刷新数据
  RefreshFatherDrawer: {
    type: Function,
    required: true,
  },
  //是否可编辑
  editable: {
    type: Boolean,
  },
  dcbm: {
    type: String,
    required: true,
  },
});
//初始化一些用户的信息
const { nickname, dcode } = useUserStore().userInfo;
//表单
const formData = reactive<any>({
  guid: "",
  sqdh: "",
  djsj: new Date().toISOString().split("T")[0],
  djbmbm: dcode,
  sqlx: "2",
  sjzt: "0",
  by2: "",
  bmzcgly: "",
  djr: nickname,
});

const dataFormRef = ref<InstanceType<typeof ElForm>>();

const rules = reactive({
  by2: [{ required: true, message: "预计借用天数不能为空", trigger: "blur" }],
});
/** 初始化借用信息 */
function handleFormQuery() {
  if (props.id) {
    lendAPI
      .getFormData(props.guid)
      .then((data) => {
        Object.assign(formData, data);
      })
      .catch((error) => {
        console.error("获取借用数据失败:", error);
        ElMessage.error("获取借用数据失败");
      });
  } else {
    // 进去前给个新的GUID
    formData.guid = props.guid;
    formData.dcbm = dcode;
    formData.djr = nickname;
    formData.djsj = new Date().toISOString().split("T")[0];
  }
}

const handleSave = async () => {
  dataFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const submitData = { ...formData };
      const id = formData.id;
      const apiCall = id ? lendAPI.update(id, submitData) : lendAPI.add(submitData);
      await apiCall
        .then(async (res) => {
          ElMessage.success("借用单保存成功");
          //保存成功后，重新用id去查询一次数据
          if (!id) {
            handleFormQuery();
          }
          props.RefreshFatherDrawer();
        })
        .catch((error) => {
          ElMessage.error("操作失败，失败原因：" + error.message);
        })
        .finally(() => {});
    }
  });
};
/** 提交领用表单 */
const handleSubmit = async () => {
  dataFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      ElMessageBox.confirm("确定提交审核吗?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        //第一层confirm
        .then(async () => {
          await handleSave().then(async () => {
            //第二层保存完成
            const submitData = { ...formData };
            const dialogloading = ElLoading.service({
              lock: true,
              text: "处理中",
            });
            await lendAPI
              .submit(submitData.guid || "")
              .then(() => {
                //第三层提交完成
                ElMessage.success("借用单提交成功");
                props.RefreshFatherDrawer(true);
              })
              .finally(() => {
                dialogloading.close();
              })
              .catch((error) => {
                ElMessage.error(error.message);
              });
          });
        })
        .catch(() => {});
    }
  });
};

//list相关功能

const loading = ref(false);
const removeIds = ref<number[]>([]);

const total = ref(0);
// 借用表格数据
const pageData = ref<any[]>([]);
//请求参数
const queryParams = reactive<any>({
  pageNum: 1,
  pageSize: 10,
  syguid: props.guid,
});

//删除项
const handleRowDelete = (id?: number) => {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }
  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      lendAPI
        .deleteDetailsByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handlePageQuery();
          keyId.value++;
        })
        .catch((error) => {
          console.error("删除借用资产失败:", error);
          ElMessage.error("删除借用资产失败");
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
};

const handlePageQuery = () => {
  if (!props.id) {
    pageData.value = [];
    total.value = 0;
    return;
  }

  loading.value = true;
  lendAPI
    .getDetailsPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .catch((error) => {
      console.error("获取借用资产列表失败:", error);
      ElMessage.error("获取借用资产列表失败");
      pageData.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
};
//—————————————————————————————————————————————打开弹窗选择资产

// 弹窗显隐
const choosedialog = reactive({
  title: "资产借用明细",
  visible: false,
});

const handleAddTransferPorperties = async () => {
  await dataFormRef.value?.validate();
  const api = handleSave();
  await api.then(() => {
    choosedialog.visible = true;
  });
};

//暴露的方法
const handleReturnConfirm = (zcguid: string[]) => {
  if (!zcguid.length) {
    ElMessage.warning("请选择要借用的资产");
    return;
  }
  const param = { syGuid: props.guid || "", zcGuid: zcguid.join(",") };
  loading.value = true;
  const dialogloading = ElLoading.service({
    lock: true,
    text: "处理中",
  });
  lendAPI
    .addDetail(param)
    .then(() => {
      ElMessage.success("添加成功");
      handlePageQuery();
      choosedialog.visible = false;
      keyId.value++;
    })
    .catch((error) => {
      console.error("添加借用资产失败:", error);
      ElMessage.error("添加借用资产失败");
    })
    .finally(() => {
      loading.value = false;
      dialogloading.close();
    });
};

//------------------------------打开弹窗查看资产信息
const itemVisible = ref(false);
const itemGuid = ref("");
const itemRkGuid = ref("");
const handleRowView = (guid: string, rkguid: string) => {
  itemGuid.value = guid;
  itemRkGuid.value = rkguid;
  itemVisible.value = true;
};

onMounted(() => {
  handleFormQuery();
  handlePageQuery();
});
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}

/* 自定义对话框样式 */
.custom-dialog {
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  background-color: #ffffff;
  .el-dialog__header {
    background-color: #409eff;
    color: #ffffff;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    .el-dialog__title {
      color: #ffffff;
    }
  }
  .el-dialog__body {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.9);
  }
}
</style>
