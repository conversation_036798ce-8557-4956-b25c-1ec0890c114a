<!-- cursor测试:1 -->
<template>
  <div>
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item label="盘点部门" prop="sybm">
            <DDLDeptList v-model="queryParams.sybm" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon>
                <Search />
              </template>
              搜索
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon>
                <Refresh />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="盘点任务列表"></Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        stripe
        border
      >
        <el-table-column v-if="add" type="selection" width="55" align="center" fixed />
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          key="sybmname"
          label="部门名称"
          prop="sybmname"
          min-width="150"
          align="center"
        />
        <el-table-column key="xfsl" label="相符数量" prop="xfsl" min-width="100" align="center" />
        <el-table-column key="xfje" label="相符金额" prop="xfje" min-width="120" align="center" />
        <el-table-column
          key="bxfsl"
          label="不相符数量"
          prop="bxfsl"
          min-width="100"
          align="center"
        />
        <el-table-column
          key="bxfje"
          label="不相符金额"
          prop="bxfje"
          min-width="100"
          align="center"
        />
        <el-table-column key="wpsl" label="未盘点数量" prop="wpsl" min-width="120" align="center" />
        <el-table-column key="wpje" label="未盘点金额" prop="ypsl" min-width="120" align="center" />
        <el-table-column key="sl" label="资产数量小计" prop="sl" min-width="120" align="center" />
        <el-table-column key="je" label="资产金额小计" prop="je" min-width="120" align="center" />
        <el-table-column fixed="right" label="操作" width="120">
          <template #default="scope">
            <el-button
              v-if="view"
              type="success"
              icon="Document"
              size="small"
              @click="handleOpenDialog(false, scope.row)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 10"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>
  </div>

  <!-- 任务表单弹窗 -->
  <el-drawer v-model="dialog.visible" :title="dialog.title" append-to-body size="80%">
    <el-card>
      <!-- 引入 bmtask 组件 -->
      <Persontask
        :id="itemId"
        :key="itemGuid"
        :guid="itemGuid"
        :editable="itemEditable"
        :dcbm="itemDcbm"
        :netcode="itemNetcode"
        :type="itemtype"
        :RefreshFatherDrawer="RefreshFatherDrawer"
      />
    </el-card>
  </el-drawer>
</template>
<script setup lang="ts">
defineOptions({
  name: "task",
});
import { ElMessage, ElForm } from "element-plus";
import taskAPI, { taskPageVO, taskPageQuery } from "@/api/properties/task";
import Persontask from "@/views/properties/inventory/task/index.vue";
import { getGuid } from "@/utils/guid";
//————————————————————————————————————————————暴露的方法,和请求参数

const props = defineProps({
  guid: {
    type: String,
    required: true,
  },
  dcbm: {
    type: String,
    required: true,
  },
});

function RefreshFatherDrawer(close?: boolean) {
  //是否关闭弹窗由参数决定，默认不关闭
  if (close) {
    dialog.visible = false;
  }
  // itemId.value = undefined;
  handleQuery();
}
// 获取路由参数,view/add/check
const route = useRoute();
// 获取路由实例（可导航）
const router = useRouter();
const type = ref(route.query.type?.toString() || "view");
//显示隐藏权限,用于v-if
const add = type.value == "add";
const view = type.value == "view";
const check = type.value == "check";

//————————————————————————————————————————————查询相关
const queryFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);
let nf = "";
if (type.value == "view") {
  nf = String(new Date().getFullYear());
}
const queryParams = reactive<any>({
  pageNum: 1,
  pageSize: 10,
  jhguid: props.guid,
  sybm: props.dcbm,
});
// 计划表格数据
const pageData = ref<any[]>([]);

/** 查询计划 */
function handleQuery() {
  loading.value = true;
  console.log("刷新列表数据");
  taskAPI
    .getAllPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .catch((error) => {
      console.error("获取计划数据失败:", error);
      ElMessage.error("获取计划数据失败");
      pageData.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置计划查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  queryParams.type = type.value;
  handleQuery();
}
const handleChangeRadio = () => {
  handleResetQuery();
};

//————————————————————————————————————————————弹窗相关
// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});
/** 打开计划弹窗 */
const itemGuid = ref<string>("");
const itemId = ref<number | undefined>();
const itemDcbm = ref<string>("");
const itemEditable = ref<boolean>(false);
const itemNetcode = ref<string>("");
const itemtype = ref<string>("");

function handleOpenDialog(editable: boolean, row?: any) {
  itemGuid.value = row?.jhguid || getGuid();
  itemDcbm.value = row?.sybm || "";
  itemId.value = row?.id;
  itemEditable.value = editable;
  itemtype.value = "cx";
  dialog.title = editable ? (row?.id ? "编辑计划单" : "新增计划单") : "盘点任务查看";
  dialog.visible = true;
  console.log("type.value", type.value);
}

function handleOpen(editable: boolean) {
  router.replace("/pub/adjustMarket?type=ly");
}

onMounted(() => {
  handleQuery();
});
</script>

<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}

.flex {
  display: flex;
  flex-direction: row;
}
</style>
<style lang="scss">
.el-table__row {
  .el-checkbox {
    margin-right: 10px;
  }
}
</style>
