import { ref } from 'vue';
import storeAPI from "@/api/properties/store";

// 创建一个ref来存储数据，并使用一个变量来跟踪是否已加载
const czcodes = ref<OptionType[]>([]);

// 定义一个函数来获取数据，包含缓存逻辑
const fetchCzCodes = async () => {
  try {
    // 如果当前值为空，则调用API获取数据
    if (czcodes.value.length === 0) {
      console.log("正在获取CZ代码树...");
      const data = await storeAPI.getCzCodeTree();
      czcodes.value = data;
    }
  } catch (error) {
    console.error("获取CZ代码树失败:", error);
    // 可以在这里添加错误处理逻辑
  }
};

// 在模块加载时立即执行一次数据获取
(async () => {
  try {
    await fetchCzCodes();
  } catch (error) {
    console.error("首次获取CZ代码树失败:", error);
  }
})();

// 导出数据和获取函数
export { czcodes, fetchCzCodes };
