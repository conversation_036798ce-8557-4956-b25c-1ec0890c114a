<template>
  <div style="margin: 10px">
    <!-- <el-drawer v-model="_modelValue" append-to-body size="50%"> -->
    <el-form ref="dataFormRef" :model="formData" label-width="auto" :inline="true">
      <el-form-item label="资产编号：">
        {{ formData.zcbh }}
      </el-form-item>
      <el-form-item label="教育分类：">
        {{ formData.jycodeName }}
      </el-form-item>
      <el-form-item label="财政分类：">
        {{ formData.czcodeName }}
      </el-form-item>
      <el-form-item label="资产名称：">
        {{ formData.zcmc }}
      </el-form-item>
      <el-form-item label="数量：">
        {{ formData.sl }}
      </el-form-item>
      <el-form-item label="计量单位：">
        {{ formData.jldw }}
      </el-form-item>
      <el-form-item label="货币单位：">
        {{ formData.hbdwname }}
      </el-form-item>
      <el-form-item label="单价(元)：">
        {{ formData.dj }}
      </el-form-item>
      <el-form-item label="资产原值(元)：">
        {{ formData.je }}
      </el-form-item>
      <el-form-item label="价值类型：">
        {{ formData.jzlxname }}
      </el-form-item>
      <el-form-item label="购置日期：">
        {{ formData.gzrq }}
      </el-form-item>
      <el-form-item label="取得日期：">
        {{ formData.qdrq }}
      </el-form-item>
      <el-form-item label="部门：">
        {{ formData.sybmname }}
      </el-form-item>
      <el-form-item label="保管员：">
        {{ formData.syrname }}
      </el-form-item>
      <el-form-item label="存放地点：">
        {{ formData.cfddname }}
      </el-form-item>
      <el-form-item label="部门资产管理员：">
        {{ formData.bmzcglyname }}
      </el-form-item>
      <el-form-item label="使用方向：">
        {{ formData.syfxname }}
      </el-form-item>
      <el-form-item label="使用日期：">
        {{ formData.syrq }}
      </el-form-item>
      <el-form-item label="发票编号：">
        {{ formData.fpdjh }}
      </el-form-item>
      <el-form-item label="会计凭证号：">
        {{ formData.kjpzh }}
      </el-form-item>
      <el-form-item label="会计凭证日期：">
        {{ formData.kjpzsj }}
      </el-form-item>
      <el-form-item label="确认书号：">
        {{ formData.querensh }}
      </el-form-item>
      <el-form-item label="经费编号：">
        {{ formData.jfbh }}
      </el-form-item>
      <el-form-item label="经费科目：">
        {{ formData.jfkmname }}
      </el-form-item>
      <el-form-item label="取得方式：">
        {{ formData.qdfsname }}
      </el-form-item>
      <el-form-item label="预计使用年限(月)：">
        {{ formData.yjsynx }}
      </el-form-item>
      <el-form-item label="已使用年限(月)：">
        {{ formData.ljsyys }}
      </el-form-item>
      <!-- <el-form-item label="采购申请人：">
      {{ formData.id }}
    </el-form-item>
    <el-form-item label="采购组织形式：">
      {{ formData.id }}
    </el-form-item>
    <el-form-item label="供应商名称：">
      {{ formData.id }}
    </el-form-item>
    <el-form-item label="品牌">
      {{ formData.pp }}
    </el-form-item>
    <el-form-item label="规格型号：">
      {{ formData.ggxh }}
    </el-form-item>
    <el-form-item label="生产厂家：">
      {{ formData.id }}
    </el-form-item>
    <el-form-item label="合同编号：">
      {{ formData.id }}
    </el-form-item>
    <el-form-item label="是否库存：">
      {{ formData.id }}
    </el-form-item>
    <el-form-item label="备注">
      {{ formData.notes }}
    </el-form-item> -->
    </el-form>
    <!-- </el-drawer> -->
  </div>
</template>

<script setup lang="ts">
import BookApt from "@/api/properties/book";
import { czcodes, fetchCzCodes } from "@/assets/CZCode";
import { jycodes, fetchJyCodes } from "@/assets/JYCode";
const props = defineProps({
  guid: {
    type: String,
    required: true,
  },
});
// const emit = defineEmits(["update:modelValue"]);
// const _modelValue = computed({
//   get: () => {
//     return props.modelValue;
//   },
//   set: (val) => {
//     emit("update:modelValue", val);
//   },
// });
let formData = reactive<any>({});

const handleQuery = () => {
  BookApt.getFormData(props.guid).then((res) => {
    Object.assign(formData, res);
  });
};
watch(
  () => props.guid,
  () => {
    console.log("props.guid", props.guid);
    if (props.guid && props.guid.length > 0) {
      handleQuery();
    }
  }
);
//监听formData.czcode的值，并从codes中找到对应的name
watch(
  () => formData.czcode,
  (newVal) => {
    if (newVal) {
      const codeItem = findInTree(czcodes.value, newVal);
      console.log("codeItem", codeItem);
      if (codeItem) {
        formData.czcodeName = codeItem.label;
      }
    } else {
      formData.czcodeName = '';
    }
  }
);
//监听formData.czcode的值，并从codes中找到对应的name
watch(
  () => formData.jycode,
  (newVal) => {
    if (newVal) {
      const codeItem = findInTree(jycodes.value, newVal);
      console.log("codeItem--jycodeName", codeItem);
      if (codeItem) {
        formData.jycodeName = codeItem.label;
      }
    } else {
      formData.jycodeName = '';
    }
  }
);

// 递归查找树形结构中的节点
function findInTree(tree: any[], value: string): any {
  for (const node of tree) {
    if (node.value === value) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findInTree(node.children, value);
      if (found) {
        return found;
      }
    }
  }
  return null;
}

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}
</style>
