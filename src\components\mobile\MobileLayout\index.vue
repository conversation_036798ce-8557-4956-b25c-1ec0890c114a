<template>
  <div class="mobile-layout">
    <!-- 顶部导航栏 -->
    <div class="mobile-navbar" v-if="showNavBar">
      <div class="navbar-content">
        <div class="navbar-left">
          <div v-if="showBack" class="back-button" @click="handleBack">
            <svg-icon icon-class="arrow-left" size="20px" />
          </div>
        </div>
        <div class="navbar-title">{{ pageTitle }}</div>
        <div class="navbar-right">
          <slot name="navbar-right"></slot>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="mobile-content" :class="{ 'has-navbar': showNavBar, 'has-tabbar': showTabBar }">
      <router-view />
    </div>

    <!-- 底部Tab导航 -->
    <div class="mobile-tabbar" v-if="showTabBar">
      <div class="tabbar-content">
        <div 
          v-for="tab in tabs" 
          :key="tab.key"
          class="tab-item"
          :class="{ active: activeTab === tab.key }"
          @click="handleTabClick(tab)"
        >
          <div class="tab-icon">
            <svg-icon :icon-class="tab.icon" size="22px" />
          </div>
          <div class="tab-title">{{ tab.title }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/store'

defineOptions({
  name: 'MobileLayout'
})

interface TabItem {
  key: string
  title: string
  icon: string
  path: string
}

const props = withDefaults(defineProps<{
  showNavBar?: boolean
  showTabBar?: boolean
  showBack?: boolean
  title?: string
}>(), {
  showNavBar: true,
  showTabBar: true,
  showBack: false,
  title: ''
})

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()

// Tab配置
const tabs: TabItem[] = [
  {
    key: 'dashboard',
    title: '首页',
    icon: 'homepage',
    path: '/mobile/dashboard'
  },
  {
    key: 'profile',
    title: '我的',
    icon: 'user',
    path: '/mobile/profile'
  }
]

// 当前激活的Tab
const activeTab = computed(() => {
  const currentPath = route.path
  const tab = tabs.find(t => currentPath.startsWith(t.path))
  return tab?.key || 'dashboard'
})

// 页面标题
const pageTitle = computed(() => {
  if (props.title) return props.title
  
  // 根据路由获取标题
  const routeTitle = route.meta?.title as string
  if (routeTitle) return routeTitle
  
  // 根据当前Tab获取标题
  const currentTab = tabs.find(t => t.key === activeTab.value)
  return currentTab?.title || '首页'
})

// 处理Tab点击
const handleTabClick = (tab: TabItem) => {
  if (activeTab.value !== tab.key) {
    router.push(tab.path)
  }
}

// 处理返回按钮
const handleBack = () => {
  router.back()
}
</script>

<style lang="scss" scoped>
.mobile-layout {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  background-color: #f5f5f5;
}

.mobile-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 44px;
  background-color: #fff;
  border-bottom: 1px solid #e5e5e5;

  .navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 16px;
  }

  .navbar-left,
  .navbar-right {
    display: flex;
    align-items: center;
    min-width: 60px;
  }

  .navbar-left {
    justify-content: flex-start;
  }

  .navbar-right {
    justify-content: flex-end;
  }

  .back-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f5f5f5;
    }
  }

  .navbar-title {
    flex: 1;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    color: #333;
  }
}

.mobile-content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;

  &.has-navbar {
    padding-top: 44px;
  }

  &.has-tabbar {
    padding-bottom: 50px;
  }
}

.mobile-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 50px;
  background-color: #fff;
  border-top: 1px solid #e5e5e5;

  .tabbar-content {
    display: flex;
    height: 100%;
  }

  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: color 0.2s;

    &.active {
      color: var(--el-color-primary);
    }

    &:not(.active) {
      color: #999;
    }
  }

  .tab-icon {
    margin-bottom: 2px;
  }

  .tab-title {
    font-size: 10px;
    line-height: 1;
  }
}

// 移动端适配
@media screen and (max-width: 992px) {
  .mobile-layout {
    display: flex;
  }
}
</style>
