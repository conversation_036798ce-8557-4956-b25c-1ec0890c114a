<template>
  <div>
    <el-tabs v-model="activeName" class="demo-tabs">
      <el-tab-pane label="基本信息" name="1">
        <CardInfo :key="props.guid" :guid="props.guid" />
      </el-tab-pane>
      <el-tab-pane label="资产照片" name="2">
        <ImageList :key="props.rkguid" :guid="props.rkguid" />
      </el-tab-pane>
      <el-tab-pane label="附件文档" name="3">
        <FileView :key="props.rkguid" :guid="props.rkguid" />
      </el-tab-pane>
      <el-tab-pane label="变动情况" name="4" />
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
//附件面板，集成了基本信息，照片附件，变动等信息
const props = defineProps({
  guid: {
    type: String,
    required: true,
  },
  rkguid: {
    type: String,
    required: true,
  },
});

const activeName = ref("1");

// const handleClick = (tab: TabsPaneContext, event: Event) => {
//   console.log(tab, event);
// };

onMounted(() => {});
</script>
<style lang="scss" scoped></style>
