<template>
  <el-input v-model="filterText" placeholder="请输入关键字" />
  <el-scrollbar height="500px">
    <el-tree
      ref="treeRef"
      class="filter-tree"
      :data="pageData.treeData"
      :props="pageData.defaultProps"
      :default-expand-all="false"
      :filter-node-method="filterNode"
    />
  </el-scrollbar>
  <div class="center">
    <el-button type="primary" @click="handleSubmit">提交</el-button>
  </div>
</template>

<script setup lang="ts">
import SysCgmlAPI, { Cgmlcodes, fetchCgmlcodes } from "@/api/cg/sys-cgml";
//根据guid获取节点信息
const props = defineProps({
  /* type: {
    type: String,
    required: true,
  },*/
  Cglb: {
    type: String,
  },
});
const pageData = reactive({
  treeData: <any[]>[],

  defaultProps: {
    children: "children",
    label: "label",
  },
});

interface Tree {
  [key: string]: any;
}

const filterText = ref("");
const treeRef = ref<InstanceType<typeof ElTree>>();
const emits = defineEmits(["node-click"]);

watch(filterText, (val) => {
  treeRef.value!.filter(val);
});

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.label.includes(value);
};

//选择节点事件
const handleSubmit = () => {
  if (!treeRef.value!.getCurrentNode().children) {
    emits("node-click", treeRef.value!.getCurrentNode().value);
  }
};

onMounted(() => {
  fetchCgmlcodes();

  if (props.Cglb) {
    var tlen = pageData.treeData.length;
    if (tlen > 0) pageData.treeData.splice(0, tlen);
    if (props.Cglb == "A") pageData.treeData.push(Cgmlcodes.value[0]);
    else if (props.Cglb == "B") pageData.treeData.push(Cgmlcodes.value[1]);
    else if (props.Cglb == "C") pageData.treeData.push(Cgmlcodes.value[2]);
  } else pageData.treeData = Cgmlcodes.value;
  console.log("Cgmlcodes:", Cgmlcodes.value[0]);
});
</script>
<style lang="scss" scoped>
.center {
  text-align: center;
}
</style>
