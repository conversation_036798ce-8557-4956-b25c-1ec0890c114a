import request from "@/utils/request";

const ZXCGSQJF_BASE_URL = "/api/v1/zxcgSqjfs";

const ZxcgSqjfAPI = {
  /** 获取申请经费分页数据 */
  getPage(queryParams?: ZxcgSqjfPageQuery) {
    return request<any, PageResult<ZxcgSqjfPageVO[]>>({
      url: `${ZXCGSQJF_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取申请经费表单数据
   *
   * @param id ZxcgSqjfID
   * @returns ZxcgSqjf表单数据
   */
  getFormData(id: number) {
    return request<any, ZxcgSqjfForm>({
      url: `${ZXCGSQJF_BASE_URL}/${id}/form`,
      method: "get",
    });
  },

  /** 添加申请经费*/
  add(ysbm?: string, sqid?: number, ysje?: number) {
    return request({
      url: `${ZXCGSQJF_BASE_URL}`,
      method: "post",
      params: {
        ysbm: ysbm,
        sqid: sqid,
        ysje: ysje,
      },
    });
  },

  /**
   * 更新申请经费
   *
   * @param id ZxcgSqjfID
   * @param data ZxcgSqjf表单数据
   */
  update(id: number, ysje?: number) {
    return request({
      url: `${ZXCGSQJF_BASE_URL}/${id}`,
      method: "put",
      params: {
        ysje: ysje,
      },
    });
  },

  /**
   * 批量删除申请经费，多个以英文逗号(,)分割
   *
   * @param ids 申请经费ID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${ZXCGSQJF_BASE_URL}/${ids}`,
      method: "delete",
    });
  },
};

export default ZxcgSqjfAPI;

/** 申请经费分页查询参数 */
export interface ZxcgSqjfPageQuery extends PageQuery {
  /** 外键，自增 */
  sqid?: number;
  /** zxcg_sq.guid */
  sqguid?: string;
}

/** 申请经费表单对象 */
export interface ZxcgSqjfForm {
  /** 主键，自增 */
  jfid?: number;
  /** 外键，自增 */
  sqid?: number;
  /** 预算项目ID */
  ysxmid?: number;
  /** 经费类别(专项、基本、科研、自筹) */
  zgbm?: string;
  /** 主管部门 */
  jflb?: string;
  /** 经费年度 */
  jfnd?: string;
  /** 经费代码 */
  jfbh?: string;
  /** 经费名称 */
  jfmc?: string;
  /** 财务经费代码 */
  cwjfbh?: string;
  /** 财务经费名称 */
  cwjfmc?: string;
  /** 是否含税(1是，0否) */
  ishs?: string;
  /** 计划金额 */
  jhje?: number;
  /** 可用金额 */
  ysje?: number;
  /** 实际支出（元） */
  sjzc?: number;
  /** 可用金额 */
  kyje?: number;
  /** zxcg_sq.guid */
  sqguid?: string;
  /** 采购计划ID */
  jhid?: number;
  /** 经费总金额 */
  yszje?: number;
  /** 作废 */
  syje?: number;
  /** 经费类别名称 */
  jflbname?: string;
}

/** 申请经费分页对象 */
export interface ZxcgSqjfPageVO {
  /** 主键，自增 */
  jfid?: number;
  /** 外键，自增 */
  sqid?: number;
  /** 预算项目ID */
  ysxmid?: number;
  /** 经费类别(专项、基本、科研、自筹) */
  zgbm?: string;
  /** 主管部门 */
  jflb?: string;
  /** 经费年度 */
  jfnd?: string;
  /** 经费代码 */
  jfbh?: string;
  /** 经费名称 */
  jfmc?: string;
  /** 财务经费代码 */
  cwjfbh?: string;
  /** 财务经费名称 */
  cwjfmc?: string;
  /** 是否含税(1是，0否) */
  ishs?: string;
  /** 计划金额 */
  jhje?: number;
  /** 可用金额 */
  ysje?: number;
  /** 实际支出（元） */
  sjzc?: number;
  /** 可用金额 */
  kyje?: number;
  /** zxcg_sq.guid */
  sqguid?: string;
  /** 采购计划ID */
  jhid?: number;
  /** 经费总金额 */
  yszje?: number;
  /** 作废 */
  syje?: number;
  /** 经费类别名称 */
  jflbname?: string;
}
