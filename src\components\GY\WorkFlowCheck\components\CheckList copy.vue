<template>
  <div class="approval-form approval-card">
    <!-- 审批流程图 -->
    <div class="approval-flow">
      <Title name="审批流程" />
      <div class="custom-steps">
        <el-steps
          v-loading="chartloading"
          :active="activeStepIndex"
          finish-status="success"
          align-center
        >
          <el-step
            v-for="(step, index) in steps"
            :key="index"
            :title="step.netname"
            :description="step.nickname"
            :status="getStepStatus(step.netcode, step.netstate)"
            :icon="getStepIcon(step.netcode, step.netstate)"
          >
            <template #description>
              <div class="step-details" @click="getLcrzData">
                <div v-if="getStepShow(step.netcode)" class="operator">
                  审批人:{{ step.nickname }}
                </div>
                <div v-else class="waiting">审批人:{{ step.nickname }}</div>
                <div class="timestamp">
                  <span v-if="step.time">{{ format(step.time, "YYYY-MM-DD hh:mm:ss") }}</span>
                  <span v-else />
                </div>
              </div>
            </template>
          </el-step>
        </el-steps>
      </div>
      <Title name="审批日志" />
      <!-- 审批历史 -->
      <div v-if="history && history.length > 0" class="approval-history">
        <el-table v-loading="loading" stripe :data="history" highlight-current-row :border="true">
          <el-table-column label="序号" type="index" width="55" align="center" fixed>
            <template #default="scope">
              <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="环节名称" prop="netcodename" width="250" />
          <!-- <el-table-column label="操作角色" prop="zcmc" width="200" /> -->
          <el-table-column label="操作意见" prop="shyj" width="300" />
          <el-table-column label="操作时间" prop="czsj" min-width="170" />
          <el-table-column label="操作人" prop="czrnamenick" width="150" />
          <el-table-column label="操作部门" prop="czrbhname" width="200" />
          <el-table-column label="动作" prop="activename" width="100" />
        </el-table>
        <!-- ********************** 翻页 ********************** -->
        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getLcrzData"
        />

        <!-- <el-timeline v-loading="loading">
          <el-timeline-item
            v-for="(activity, index) in history"
            :key="index"
            :type="getStatusType(activity.active)"
            :timestamp="activity.czsj"
            :hollow="activity.hollow"
          >
            <div class="timeline-content">
              <h4>{{ activity.netcodename }}</h4>
              <div class="activity-details">
                <p>
                  <strong>审核人：</strong>
                  {{ activity.czrnamenick }}
                </p>
                <p>
                  <strong>操作：</strong>
                  {{ activity.activename }}
                </p>
                <p>
                  <strong>审批意见：</strong>
                  {{ activity.shyj }}
                </p>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, PropType } from "vue";
import WorkFlowAPI, { WorkFlowChartVO } from "@/api/system/workflow";
import { ElMessage, ElMessageBox, FormInstance } from "element-plus";
import { format } from "@/utils/day";
defineOptions({
  name: "CheckForm",
});
// 定义活动状态类型
type ActivityStatus = "Forward" | "After" | "Stay" | string;

// 获取状态对应的类型
const getStatusType = (
  status: ActivityStatus
): "success" | "warning" | "info" | "primary" | "danger" => {
  const statusMap: Record<ActivityStatus, "success" | "warning" | "info" | "primary" | "danger"> = {
    Forward: "success",
    After: "danger",
    Stay: "primary",
  };
  return statusMap[status] || "info"; // 默认返回 'info'
};
// 定义组件属性
const props = defineProps({
  // 表单标题
  title: {
    type: String,
    default: "审批表单",
  },
  // 表单数据
  guid: {
    type: String,
    required: true,
  },
  // 表单数据
  netcode: {
    type: String,
    required: true,
  },
});

const loading = ref(false);
const chartloading = ref(false);
const history = ref<any[]>([]);
const steps = ref<WorkFlowChartVO[]>([]);
const total = ref(0);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  guid: props.guid,
});
const getLcrzData = () => {
  loading.value = true; // 设置 loading 为 true
  WorkFlowAPI.getLcrzPage(queryParams)
    .then((res) => {
      console.log(res);
      history.value = res.list;
      total.value = res.total;
    })
    .catch(() => {
      // ElMessage.error("获取审批历史失败");
    })
    .finally(() => {
      loading.value = false; // 设置 loading 为 false
    });
};

const getChart = () => {
  chartloading.value = true; // 设置 chartloading 为 true
  WorkFlowAPI.getChart({
    guid: props.guid,
    netcode: props.netcode,
  })
    .then((res) => {
      steps.value = res;
    })
    .catch(() => {
      // ElMessage.error("获取审批流程失败");
    })
    .finally(() => {
      chartloading.value = false; // 设置 chartloading 为 false
    });
};

// 计算当前激活的步骤索引
const activeStepIndex = computed(() => {
  return steps.value.findIndex((step) => step.netcode === props.netcode);
});

// 获取步骤状态
const getStepStatus = (netcode?: string, netstate?: string): "success" | "finish" | "wait" => {
  if (netcode === props.netcode && netstate === "1") {
    return "success"; // 当前节点
  } else if (netcode === props.netcode) {
    return "finish"; // 当前节点
  } else if (steps.value.findIndex((step) => step.netcode === netcode) < activeStepIndex.value) {
    return "success"; // 已完成节点
  } else {
    return "wait"; // 未完成节点
  }
};

// 获取步骤状态
const getStepShow = (netcode?: string): boolean => {
  if (netcode === props.netcode) {
    return true; // 当前节点
  } else if (steps.value.findIndex((step) => step.netcode === netcode) < activeStepIndex.value) {
    return true; // 已完成节点
  } else {
    return false; // 未完成节点
  }
};

// 获取步骤状态
const getStepIcon = (netcode?: string, netstate?: string) => {
  if (netcode === props.netcode && netstate === "1") {
    return "CircleCheck"; // 当前节点
  } else if (netcode === props.netcode) {
    return "Aim"; // 当前节点
  } else if (steps.value.findIndex((step) => step.netcode === netcode) < activeStepIndex.value) {
    return "CircleCheck"; // 已完成节点
  } else {
    return "Remove"; // 未完成节点
  }
};

// 封装等待函数
const wait = (ms: any) => new Promise((resolve) => setTimeout(resolve, ms));
onMounted(() => {
  loading.value = true;
  chartloading.value = true;
  getLcrzData();
  getChart();
  // console.log("12311111111111111");
});
</script>

<style lang="scss" scoped>
.approval-form {
  width: 100%;
}

.approval-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: 16px;
      font-weight: bold;
    }
  }

  .approval-info {
    margin-bottom: 20px;
  }

  .approval-flow {
    margin: 20px 0;

    .flow-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 15px;
      color: var(--el-text-color-primary);
    }
  }

  .approval-history {
    margin: 20px 0;

    .history-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 15px;
      color: var(--el-text-color-primary);
    }

    .timeline-content {
      h4 {
        margin: 0;
        font-size: 14px;
        color: var(--el-text-color-primary);
      }

      .activity-details {
        margin-top: 5px;

        p {
          margin: 5px 0 0;
          font-size: 13px;
          color: var(--el-text-color-secondary);

          strong {
            color: var(--el-text-color-primary);
          }
        }
      }
    }
  }

  .approval-opinion {
    margin: 20px 0;

    .opinion-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 15px;
      color: var(--el-text-color-primary);
    }
  }

  .approval-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
  }
}

.custom-steps {
  padding: 20px;

  :deep(.el-steps) {
    .el-step__head {
      &.process .el-step__icon {
        background: #409eff;
        color: white;
      }
      &.success .el-step__icon {
        background: #67c23a;
        color: white;
      }
    }
    .el-step__line {
      top: 15px;
      background: #e4e7ed;
    }
  }
}
.step-details {
  display: flex;
  flex-direction: column;
  gap: 8px; // 增加行间距，提升可读性
  //左侧对齐
  .operator {
    font-size: 14px;
    color: var(--el-text-color-primary);
    font-weight: bold; // 强调审批人信息
  }

  .waiting {
    font-size: 14px;
    color: #a8abb2;
  }

  .timestamp {
    font-size: 13px;
    color: var(--el-text-color-secondary);

    span {
      display: inline-block;
      padding: 2px 6px;
      border-radius: 4px;

      &.has-time {
        background-color: #f0f8ff; // 时间存在时的背景色
        color: #409eff; // 链接蓝颜色
      }

      &.no-time {
        background-color: #f5f7fa; // 时间缺失时的背景色
        color: #909399; // 灰色字体
      }
    }
  }
}
</style>
