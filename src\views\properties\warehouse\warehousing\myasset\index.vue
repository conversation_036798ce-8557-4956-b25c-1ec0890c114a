<!-- cursor测试:1 -->
<template>
  <div>
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item label="资产编号" prop="zcbh">
            <el-col :span="11">
              <el-input
                v-model="queryParams.zcbhs"
                placeholder="编号起"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-col>
            <el-col :span="2" class="text-center">
              <span class="text-gray-500">-</span>
            </el-col>
            <el-col :span="11">
              <el-input
                v-model="queryParams.zcbhe"
                placeholder="编号止"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-col>
          </el-form-item>
          <el-form-item label="资产名称" prop="zcmc">
            <el-input
              v-model="queryParams.zcmc"
              placeholder="请输入资产名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="规格型号" prop="ggxh">
            <el-input
              v-model="queryParams.ggxh"
              placeholder="请输入规格型号"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="使用部门" prop="sybm">
            <DDLDeptList v-model="queryParams.sybm" clearable />
          </el-form-item>
          <el-form-item label="是否可入仓" prop="zczt">
            <el-select v-model="queryParams.zczt" clearable :loading="loading" placeholder="请选择">
              <el-option label="可入仓" value="1" />
            </el-select>
          </el-form-item>
          <div v-if="isExpand">
            <el-form-item label="保管员" prop="syr">
              <el-input
                v-model="queryParams.syr"
                placeholder="请输入保管员"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="账面状态" prop="zcgjzt">
              <DDLXcode v-model="queryParams.zcgjzt" xcode="020236" clearable />
            </el-form-item>
            <el-form-item label="购置日期" prop="qdrq">
              <el-date-picker
                v-model="queryParams.qdrq"
                :editable="false"
                class="filter-item"
                type="daterange"
                range-separator="~"
                start-placeholder="开始时间"
                end-placeholder="截止时间"
                value-format="YYYY-MM-DD"
                clearable
              />
            </el-form-item>
            <el-form-item label="供应商名称" prop="gysmc">
              <el-input
                v-model="queryParams.gysmc"
                placeholder="请输入供应商名称"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="使用方向" prop="syfxs">
              <DDLXcode v-model="queryParams.syfxs" xcode="020204" :multiple="true" clearable />
            </el-form-item>
            <el-form-item label="原值（元）" prop="je">
              <el-col :span="11">
                <el-input
                  v-model="queryParams.jes"
                  placeholder="最小金额"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-col>
              <el-col :span="2" class="text-center">
                <span class="text-gray-500">-</span>
              </el-col>
              <el-col :span="11">
                <el-input
                  v-model="queryParams.jee"
                  placeholder="最大金额"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-col>
            </el-form-item>
            <el-form-item label="取得方式" prop="qdfs">
              <DDLXcode v-model="queryParams.qdfs" xcode="020218" clearable />
            </el-form-item>
            <el-form-item label="经费来源" prop="zjly">
              <el-input
                v-model="queryParams.zjly"
                placeholder="请输入经费来源"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="记账日期" prop="sxrq">
              <el-date-picker
                v-model="queryParams.sxrq"
                :editable="false"
                class="filter-item"
                type="daterange"
                range-separator="~"
                start-placeholder="开始时间"
                end-placeholder="截止时间"
                value-format="YYYY-MM-DD"
                clearable
              />
            </el-form-item>
            <div>
              <el-form-item style="width: 600px" label="财政分类" prop="czXCodes">
                <CascaderCzJyCode
                  v-model="queryParams.czXCodes"
                  type="CZ"
                  :multiple="true"
                  clearable
                />
              </el-form-item>
              <el-form-item style="width: 600px" label="存放地点" prop="cfdd">
                <CascaderACode v-model="queryParams.cfdd" clearable />
              </el-form-item>
            </div>
          </div>
          <el-form-item style="width: 400px">
            <el-button type="primary" :loading="loading" @click="handleQuery">
              <template #icon>
                <Search />
              </template>
              搜索
            </el-button>
            <el-button @click="handleExpand">
              <template v-if="isExpand">
                收起
                <el-icon>
                  <ArrowUp />
                </el-icon>
              </template>
              <template v-else>
                高级搜索
                <el-icon>
                  <ArrowDown />
                </el-icon>
              </template>
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon>
                <Refresh />
              </template>
              重置
            </el-button>
            <el-button type="success" @click="handleExport">
              <template #icon>
                <Download />
              </template>
              导出
            </el-button>
          </el-form-item>
        </el-form>
      </template>

      <Title name="我的资产">
        <div>
          <el-button
            plain
            type="primary"
            icon="Pointer"
            :disabled="selectedValues.length === 0"
            @click="handleBatchUpdate"
          >
            入仓申请
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        stripe
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
          fixed
          :selectable="(row: any) => row.locName == ''"
        />
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="资产编号" prop="zcbh" width="120" fixed />
        <el-table-column label="资产名称" prop="zcmc" width="200" fixed />
        <el-table-column label="资产大类" prop="zclxfl" width="200" />
        <el-table-column label="规格型号" prop="ggxh" width="200" />
        <el-table-column label="品牌" prop="pp" width="200" />
        <el-table-column label="使用方向" prop="syfxname" width="200" />
        <el-table-column label="数量" prop="sl" min-width="80" />
        <el-table-column label="单位" prop="jldw" width="200" />
        <el-table-column label="原值(元)" prop="je" width="100" />
        <el-table-column label="保管员" prop="syrname" width="200" />
        <el-table-column label="部门资产管理员" prop="bmzcgly" width="200" />
        <el-table-column label="使用部门" prop="sybmname" width="200" />
        <el-table-column label="存放地点" prop="cfddname" width="200" />
        <el-table-column label="账面状态" prop="zcgjztname" width="200" />
        <el-table-column label="取得方式" prop="qdfsname" width="200" />
        <el-table-column label="购置日期" prop="gzrq" width="200" />
        <el-table-column label="记账日期" prop="zcrzsj" width="200" />
        <el-table-column label="经费来源" prop="zjlyname" width="200" />
        <el-table-column label="供应商名称" prop="gysmc" width="200" />
        <el-table-column label="使用现状" prop="syxzname" width="200" />
        <el-table-column label="流程状态" prop="netcodename" width="200" />

        <el-table-column label="健康值" prop="" width="200" />
        <!-- ********************** 操作列 **********************
         按钮：查询View；编辑Edit；删除Delete -->
        <el-table-column label="操作" fixed="right" width="100">
          <template #default="scope">
            <el-button
              type="success"
              icon="Document"
              size="small"
              @click="handleRowView(scope.row.guid, scope.row.rkguid)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <!-- 查看资产弹窗 -->
    <el-drawer v-model="itemVisible" append-to-body size="70%">
      <CardPanel :key="itemGuid" :guid="itemGuid" :rkguid="itemRkGuid" />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import BookApi, { bookPageQuery, bookPageVO } from "@/api/properties/book";
import gwcAPI from "@/api/properties/gwc";
import CascaderCzJyCode from "@/components/Properties/CascaderCzJyCode/index.vue";
import CascaderACode from "@/components/Properties/CascaderACode/index.vue";
import { useUserStore } from "@/store";
import { ElLoading } from "element-plus";

const loading = ref(false);
const selectedValues = ref<string[]>([]);
const total = ref(0);
// 调拨表格数据
const pageData = ref<bookPageVO[]>([]);
const queryFormRef = ref(ElForm);
// 选中项发生变化
function handleSelectionChange(selection: bookPageVO[]) {
  selectedValues.value = selection.map((item: any) => item.id);
}

//弹窗相关参数
const itemVisible = ref(false);
const itemGuid = ref("");
const itemRkGuid = ref("");
//展开列
const isExpand = ref<boolean>(false);
const handleExpand = () => {
  isExpand.value = !isExpand.value;
};
const handleRowView = (guid: string, rkguid: string) => {
  if (!guid) {
    ElMessage.warning("资产编号不能为空");
    return;
  }
  itemGuid.value = guid;
  itemVisible.value = true;
  itemRkGuid.value = rkguid;
};

const { username, dcode } = useUserStore().userInfo;

const queryParams = reactive<bookPageQuery>({
  pageNum: 1,
  pageSize: 10,
  zcbhs: "",
  zcbhe: "",
  zcmc: "",
  ggxh: "",
  sybm: "",
  syr: username,
  zcgjzt: "",
  qdrq: ["", ""],
  gysmc: "",
  syfxs: [],
  jes: "",
  jee: "",
  qdfs: "",
  zjly: "",
  sxrq: ["", ""],
  czXCodes: [],
  cfdd: "",
  lx: "1",
  zczt: "",
});
const handleQuery = () => {
  loading.value = true;
  BookApi.getBookPage(queryParams)
    .then((res) => {
      pageData.value = res.list;
      total.value = res.total;
    })
    .catch((error) => {
      console.error("获取我的资产数据失败:", error);
      ElMessage.error("获取我的资产数据失败");
      pageData.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
};

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value?.resetFields();
  queryParams.pageNum = 1;
  queryParams.zcbhs = "";
  queryParams.zcbhe = "";
  queryParams.jes = "";
  queryParams.jee = "";
  queryParams.syfxs = [];
  queryParams.qdrq = ["", ""];
  queryParams.sxrq = ["", ""];
  queryParams.czXCodes = [];
  handleQuery();
}

//批量选择明细创建入仓单
const handleBatchUpdate = () => {
  if (selectedValues.value.length === 0) {
    ElMessage.warning("请选择要入仓的资产");
    return;
  }
  handleAddwarehouse();
};

//创建入仓单
const handleAddwarehouse = () => {
  const num = selectedValues.value.length;
  var IDS = selectedValues.value.join(",");
  console.log(IDS);
  ElMessageBox.confirm("已选" + num + "条资产，是否创建入仓？", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: "处理中",
    });
    gwcAPI
      .saveGwcSyb(IDS, "1", "0", "0")
      .then((res) => {
        if (res) {
          //res是新处置单的id
          ElMessage.success("入仓单创建成功，请前往入仓查询进行编辑或查看。");
          handleQuery();
        }
      })
      .finally(() => {
        loading.close();
      });
  });
};

onMounted(() => {
  handleQuery();
});
// 导出用户
function handleExport() {
  BookApi.exportBook(queryParams).then((response: any) => {
    const fileData = response.data;
    const fileName = decodeURI(response.headers["content-disposition"].split(";")[1].split("=")[1]);
    const fileType =
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8";

    const blob = new Blob([fileData], { type: fileType });
    const downloadUrl = window.URL.createObjectURL(blob);

    const downloadLink = document.createElement("a");
    downloadLink.href = downloadUrl;
    downloadLink.download = fileName;

    document.body.appendChild(downloadLink);
    downloadLink.click();

    document.body.removeChild(downloadLink);
    window.URL.revokeObjectURL(downloadUrl);
  });
}
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 320px;
}
</style>
