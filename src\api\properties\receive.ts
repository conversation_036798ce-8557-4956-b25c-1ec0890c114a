import request from "@/utils/request";

const receive_BASE_URL = "/api/v1/receive";

const receiveAPI = {
  /** 获取领用分页数据 */
  getPage(queryParams?: receivePageQuery) {
    return request<any, PageResult<receivePageVO[]>>({
      url: `${receive_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取领用表单数据
   *
   * @param id receiveID
   * @returns receive表单数据
   */
  getFormData(guid: string) {
    return request<any, receiveForm>({
      url: `${receive_BASE_URL}/form/${guid}`,
      method: "get",
    });
  },

  /** 添加领用*/ // 添加/api/v1/lend/add/{guid}
  add(guid: string) {
    return request({
      url: `${receive_BASE_URL}/add/${guid}`,
      method: "post",
    });
  },

  /** 添加领用*/
  addreceive(data: receiveForm): Promise<boolean> {
    return request<any, boolean>({
      url: `${receive_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新领用
   *
   * @param id receiveID
   * @param data receive表单数据
   */
  update(id: number, data: receiveForm): Promise<boolean> {
    return request<any, boolean>({
      url: `${receive_BASE_URL}/update/${id}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 提交领用
   *
   * @param id receiveID
   * @param data receive表单数据
   */
  submit(guid: string, data?: any) {
    return request({
      url: `${receive_BASE_URL}/submit/${guid}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 批量删除领用，多个以英文逗号(,)分割
   *
   * @param ids 领用ID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${receive_BASE_URL}/delete/${ids}`,
      method: "get",
    });
  },

  //———————————————————————————————— 明细相关接口————————————————————————————
  /** 添加领用明细*/
  addDetail(data: { syGuid: string; zcGuid: string }) {
    return request({
      url: `${receive_BASE_URL}/details/add`,
      method: "post",
      data: data,
    });
  },

  // 领用单明细查询
  getDetailsPage(queryParams?: receiveDetailsPageQuery) {
    return request<any, PageResult<receiveDetailPageVO[]>>({
      url: `${receive_BASE_URL}/details/page`,
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 批量删除领用单明细，多个以英文逗号(,)分割
   *
   * @param ids 领用单明细ID字符串，多个以英文逗号(,)分割
   */
  deleteDetailsByIds(ids: string) {
    return request({
      url: `${receive_BASE_URL}/details/delete/${ids}`,
      method: "get",
    });
  },

  //回收/api/v1/receives/callback/{guid}
  callBack(guid: string) {
    return request({
      url: `${receive_BASE_URL}/callback/${guid}`,
      method: "get",
    });
  },

  //回收/api/v1/receives/node/list
  getNodeList() {
    return request({
      url: `${receive_BASE_URL}/node/list`,
      method: "get",
    });
  },
};

export default receiveAPI;

/** 领用分页查询参数 */
export interface receivePageQuery extends PageQuery {
  /** 领用单号 */
  sqdh?: string;
  /** 申请部门 */
  sqbm?: string;
  /** 数据编码 */
  netcode?: string;
  /** 数据状态 */
  sjzt?: string;
  /** 登记时间 */
  sqrq?: string[];
  //查询类型，是新增页审核页还是查询页
  type?: string;
  //查询代办/已办
  checkstatus?: number;
}

/** 领用表单对象 */
export interface receiveForm {
  /** ID */
  id?: number;
  /** Guid */
  guid?: string;
  /** 领用单号 */
  sqdh?: string;
  sybm?: string;
  djbmbm?: string;
  djsj?: string;
  sxsj?: string;
  bmzcgly?: string;
  syr?: string;
  bgy?: string;
  djr?: string;
  notes?: string;
  ytsm?: string;
  /** 数据编码 */
  netcode?: string;
  /** 数据状态 */
  sjzt?: string;
  sqlx?: string;
  by1?: string;
  by2?: string;
  by3?: string;
  by4?: string;
  by5?: string;
  by6?: string;
}

/** 领用分页对象 */
export interface receivePageVO {
  /** ID */
  id?: number;
  /** Guid */
  guid?: string;
  /** 领用单号 */
  sqdh?: string;
  /** 领用部门 */
  sybmname?: string;
  nodemc?: string;
  bgyname?: string;
  djsj?: Date;
  /** 数据编码 */
  netcode?: string;
  /** 数据状态 */
  sjzt?: string;
}

/** 领用列表查询参数 */
export interface receiveDetailsPageQuery extends PageQuery {
  /** 领用单guid */
  syguid: string;
}

/** 领用分页对象 */
export interface receiveDetailPageVO {
  bgr?: string;
  syGuid?: string;
  cfdd?: string;
  zcGuid?: string;
  dj?: string;
  ggxh?: string;
  guid?: string;
  id?: string;
  je?: string;
  jldw?: string;
  pp?: string;
  sl?: string;
  sybm?: string;
  syr?: string;
  zcbh?: string;
  zcmc?: string;
  /** 领用部门 */
  sybmname?: string;
  syrname?: string;
  rkguid?: string;
}
