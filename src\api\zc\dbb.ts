import request from "@/utils/request";

const DBB_BASE_URL = "/api/v1/dbbs";

const DbbAPI = {
  /** 获取transfer分页数据 */
  getPage(queryParams?: DbbPageQuery) {
    return request<any, PageResult<DbbPageVO[]>>({
      url: `${DBB_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取transfer表单数据
   *
   * @param id DbbID
   * @returns Dbb表单数据
   */
  getFormData(id: number) {
    return request<any, DbbForm>({
      url: `${DBB_BASE_URL}/${id}/form`,
      method: "get",
    });
  },

  /** 添加transfer*/
  add(data: DbbForm) {
    return request({
      url: `${DBB_BASE_URL}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新transfer
   *
   * @param id DbbID
   * @param data Dbb表单数据
   */
  update(id: number, data: DbbForm) {
    return request({
      url: `${DBB_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 批量删除transfer，多个以英文逗号(,)分割
   *
   * @param ids transferID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${DBB_BASE_URL}/${ids}`,
      method: "delete",
    });
  },
};

export default DbbAPI;

/** transfer分页查询参数 */
export interface DbbPageQuery extends PageQuery {
  /** 调拨单号 */
  dbdh?: string;
  /** 调出部门 */
  dcbm?: string;
  /** 调入部门 */
  drbm?: string;
  /** 调入人 */
  drr?: string;
}

/** transfer表单对象 */
export interface DbbForm {
  /** ID */
  id?: string;
  /** Guid */
  guid?: string;
  /** 调拨单号 */
  dbdh?: string;
  /** 调出部门 */
  dcbm?: string;
  /** 调出部门管理员 */
  dcbmgly?: string;
  /** 调入部门 */
  drbm?: string;
  /** 调入人 */
  drr?: string;
  /** 调入部门管理员 */
  drbmgly?: string;
  /** 调拨理由 */
  dbly?: string;
  /** 流程编码 */
  betcode?: string;
  /** 数据状态 */
  sjzt?: string;
  /** 登记部门 */
  djbm?: string;
  /** 登记人 */
  djr?: string;
  /** 登记时间 */
  djsj?: string;
  /** 备注 */
  notes?: string;
  /** 终止理由 */
  by1?: string;
  /** 审结时间 */
  sjsj?: string;
  /** 调拨类型1正常调拨，2台账修改，3保管员变更 */
  dblx?: string;
}

/** transfer分页对象 */
export interface DbbPageVO {
  /** ID */
  id?: string;
  /** Guid */
  guid?: string;
  /** 调拨单号 */
  dbdh?: string;
  /** 调出部门 */
  dcbm?: string;
  /** 调出部门管理员 */
  dcbmgly?: string;
  /** 调入部门 */
  drbm?: string;
  /** 调入人 */
  drr?: string;
  /** 调入部门管理员 */
  drbmgly?: string;
  /** 调拨理由 */
  dbly?: string;
  /** 登记部门 */
  djbm?: string;
  /** 登记人 */
  djr?: string;
  /** 登记时间 */
  djsj?: string;
  /** 备注 */
  notes?: string;
}
