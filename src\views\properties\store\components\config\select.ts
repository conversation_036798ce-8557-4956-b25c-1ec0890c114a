import User<PERSON><PERSON> from "@/api/system/user";
import Dept<PERSON><PERSON> from "@/api/system/dept";
import type { ISelectConfig } from "@/components/TableSelect/index.vue";
import { ref } from "vue";

const deptOptions = ref<OptionType[]>([]);

// 加载部门数据
DeptAPI.getOptions().then((data) => {
  deptOptions.value = data;
});

const selectConfig: ISelectConfig = {
  pk: "id",
  width: "800px",
  placeholder: "请选择用户",
  //始终显示在顶部
  popover: { placement: "top-start" },
  formItems: [
    {
      type: "input",
      label: "关键字",
      prop: "keywords",
      attrs: {
        placeholder: "用户名/昵称/手机号",
        clearable: true,
        style: {
          width: "200px",
        },
      },
    },
    {
      type: "tree-select",
      label: "部门",
      prop: "deptId",
      attrs: {
        placeholder: "请选择",
        data: deptOptions,
        filterable: true,
        "check-strictly": true,
        "render-after-expand": false,
        clearable: true,
        style: {
          width: "200px",
        },
      },
    },
    {
      type: "select",
      label: "状态",
      prop: "status",
      attrs: {
        placeholder: "全部",
        clearable: true,
        style: {
          width: "100px",
        },
      },
      options: [
        { label: "启用", value: 1 },
        { label: "禁用", value: 0 },
      ],
    },
  ],
  indexAction: function (params) {
    if ("createAt" in params) {
      const createAt = params.createAt as string[];
      if (createAt?.length > 1) {
        params.startTime = createAt[0];
        params.endTime = createAt[1];
      }
      delete params.createAt;
    }
    return UserAPI.getPage(params);
  },
  tableColumns: [
    { type: "selection", width: 50, align: "center" },
    { label: "编号", align: "center", prop: "id", width: 100 },
    { label: "用户名", align: "center", prop: "username" },
    { label: "用户昵称", align: "center", prop: "nickname", width: 200 },
    { label: "部门", align: "center", prop: "deptName", width: 200 },
  ],
};

export default selectConfig;
