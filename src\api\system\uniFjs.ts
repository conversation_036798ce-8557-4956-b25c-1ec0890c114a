import request from "@/utils/request";

const UniFjs_BASE_URL = "/api/v1/uniFjs";

const UniFjsAPI = {
  /**
   * 附件查询
   *
   */
  getFjList(params: fjListQuery) {
    return request<any, PageResult<fjVo[]>>({
      url: `${UniFjs_BASE_URL}/get-unifjList`,
      method: "get",
      params: params,
    });
  },

  /**
   * 附件查询分页
   *
   */
  getFjPage(params: fjListPageQuery) {
    return request<any, PageResult<fjVo[]>>({
      url: `${UniFjs_BASE_URL}/page`,
      method: "get",
      params: params,
    });
  },
  /**
   * 删除附件
   *
   */
  deleteByIds(id: string) {
    return request({
      url: `${UniFjs_BASE_URL}/${id}`,
      method: "delete",
    });
  },
};

export interface fjListPageQuery extends PageQuery {
  guid: string;
  fjcode: string;
}

export interface fjListQuery {
  guid: string;
  fjcode: string;
}

export interface fjVo {
  id: 0;
  createTime: "";
  updateTime: "";
  guid: "";
  frmcode: "";
  fjcode: "";
  fjname: "";
  fjmc: "";
  filesize: "";
  filekide: "";
  url: "";
  djsj: "";
  djr: "";
  md5: "";
  notes: "";
  baseurl: "";
}

export default UniFjsAPI;
