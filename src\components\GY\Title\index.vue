<template>
  <div class="flex-x-between mb-10px" style="margin-top: 10px">
    <div>
      <div style="font-weight: bold; font-size: 15px">{{ props.name }}</div>
      <div style="border-bottom: 3px solid #409eff; width: 100px; height: 10px" />
    </div>
    <slot />
  </div>
</template>
<script setup>
const props = defineProps({
  name: {
    type: String,
    required: true,
  },
});
</script>
<style lang="scss" scoped></style>
