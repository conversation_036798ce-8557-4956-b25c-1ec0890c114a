import request from "@/utils/request";

const BOOK_BASE_URL = "/api/v1/book";

const PropertyCardsAPI = {
  /**
   * 获取资产分页列表
   *
   * @param data 查询参数
   */
  getBookPage(data?: bookPageQuery) {
    return request<any, PageResult<bookPageVO[]>>({
      url: `${BOOK_BASE_URL}/get-book`,
      method: "post",
      data: data,
    });
  },
  getCommonBookPage(param: any) {
    return request<any, PageResult<bookChoosePageVO[]>>({
      url: `${BOOK_BASE_URL}/choose/details-page`,
      method: "get",
      params: param,
    });
  },
  /**
   * 卡片名称
   *
   * @param guid 卡片guid
   * @returns 卡片表单数据
   */
  getFormData(guid?: string) {
    return request<any, bookPageVO>({
      url: `${BOOK_BASE_URL}/${guid}/get-form`,
      method: "get",
    });
  },

  /**
   * 导出资产台账
   *
   * @param data 查询参数
   * @returns Blob 数据
   */
  exportBook(queryParams?: bookPageQuery) {
    return request({
      url: `${BOOK_BASE_URL}/export`,
      method: "get",
      params: queryParams,
      responseType: "blob",
    });
  },
};

export default PropertyCardsAPI;

/**
 * 资产卡片列表查询对象
 */
export interface bookPageQuery extends PageQuery {
  zcmc?: string;
  zcbh?: string;
  zcbhs?: string;
  zcbhe?: string;
  zcbhIntervals?: [
    {
      zcbhs?: string;
      zcbhe?: string;
    },
  ];
  sybm?: string;
  syr?: string;
  nickname?: string;
  zcgjzt?: string;
  ggxh?: string;
  kjpzsj?: string;
  qdrqs?: string;
  qdrqe?: string;
  cfdd?: string;
  jes?: string;
  jee?: string;
  czCombXCode?: string;
  jyCombXCode?: string;
  czXCodes?: string[];
  gysmc?: string;
  syfxs?: string[];
  notes?: string;
  qdfs?: string;
  zjly?: string;
  manager?: true;
  expire?: true;
  qdrq?: [string, string];
  sxrq?: [string, string];
  lx?: string;
  zczt?: string;
}
export interface bookPageVO {
  id?: number;
  createTime?: string;
  updateTime?: string;
  guid?: string;
  cgGuid?: string;
  zcrkdh?: string;
  zcmc?: string;
  czcode?: string;
  zclxbh?: string;
  tpj?: string;
  jldw?: string;
  sl?: number;
  dj?: number;
  je?: number;
  hbdw?: string;
  qdfs?: string;
  zcdjfs?: string;
  yjsynx?: number;
  jzlx?: string;
  qdrq?: string;
  kjpzh?: string;
  zcrzsj?: string;
  gzrq?: string;
  djr?: string;
  djbm?: string;
  djsj?: string;
  fpdjh?: string;
  ysrq?: string;
  zcrksj?: string;
  netcode?: string;
  sjzt?: string;
  pp?: string;
  ggxh?: string;
  syr?: string;
  syfx?: string;
  sybm?: string;
  cfdd?: string;
  zyk?: string;
  bxdqrq?: string;
  ysdh?: string;
  zjly?: string;
  jfbh?: string;
  jfmc?: string;
  jfnd?: number;
  bmzcgly?: string;
  notes?: string;
  gysmc?: string;
  kjpzsj?: string;
  jfkm?: string;
  querensh?: string;
  jycode?: string;
  by1?: string;
  by2?: string;
  syrq?: string;
  by3?: string;
  gb?: string;
  listtype?: string;
  syrname?: string;
  sybmname?: string;
  cfddname?: string;
  czname?: string;
}

export interface bookChoosePageVO {
  id?: number;
  guid?: string;
  zcbh?: string;
  zcmc?: string;
  sl?: number;
  je?: number;
  sybmname?: string;
  syrname?: string;
  locName?: string;
}
