<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-card class="search-card" :body-style="{ padding: '24px' }">
      <el-form :model="searchForm" inline class="search-form">
        <div class="form-item-group">
          <el-form-item label="捐款人">
            <el-input v-model="searchForm.donor" placeholder="请输入捐款人姓名" clearable />
          </el-form-item>
          <el-form-item label="用途">
            <el-input v-model="searchForm.purpose" placeholder="请输入捐款用途" clearable />
          </el-form-item>
          <el-form-item label="金额区间">
            <el-input-number
              v-model="searchForm.minAmount"
              :min="0"
              placeholder="最低金额"
              style="width: 120px"
            />
            <span class="separator">-</span>
            <el-input-number
              v-model="searchForm.maxAmount"
              :min="0"
              placeholder="最高金额"
              style="width: 120px"
            />
          </el-form-item>
        </div>
        <div class="form-item-group">
          <el-form-item class="search-buttons">
            <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="resetForm">重置</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-card>

    <!-- 捐款记录表格 -->
    <el-card class="table-card" :body-style="{ padding: '20px' }">
      <div class="table-container">
        <el-table :data="currentPageData" border style="width: 100%" :loading="loading">
          <el-table-column prop="donor" label="捐款人" width="120" />
          <el-table-column prop="amount" label="金额" width="120">
            <template #default="{ row }">¥{{ row.amount.toFixed(2) }}</template>
          </el-table-column>
          <el-table-column prop="purpose" label="用途" min-width="180" />
          <el-table-column prop="donationDate" label="捐款时间" width="180" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="page.current"
            v-model:page-size="page.size"
            :total="page.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增捐款' : '编辑捐款'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="donation-form"
      >
        <el-form-item label="捐款人" prop="donor">
          <el-input v-model="formData.donor" />
        </el-form-item>
        <el-form-item label="金额" prop="amount">
          <el-input-number v-model="formData.amount" :min="0" :precision="2" style="width: 200px" />
        </el-form-item>
        <el-form-item label="用途" prop="purpose">
          <el-input v-model="formData.purpose" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="捐款时间" prop="donationDate">
          <el-date-picker
            v-model="formData.donationDate"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择捐款时间"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, computed } from "vue";
import { Search, Refresh } from "@element-plus/icons-vue";
// 删除未使用的图标导入
// import { Edit, Delete } from "@element-plus/icons-vue";
import type { ElForm } from "element-plus";
// 状态枚举
const DonationStatus = {
  Pending: 0,
  Completed: 1,
  Cancelled: 2,
};

// 搜索表单
const searchForm = reactive({
  donor: "",
  purpose: "",
  minAmount: undefined as undefined | number,
  maxAmount: undefined as undefined | number,
});

// 分页配置
const page = reactive({
  current: 1,
  size: 10,
  total: 0,
});

// 表单数据
const formData = reactive({
  donor: "",
  amount: 0,
  purpose: "",
  donationDate: "",
  status: DonationStatus.Pending,
});

// 表单校验规则
const formRules = {
  donor: [{ required: true, message: "请输入捐款人姓名", trigger: "blur" }],
  amount: [{ required: true, message: "请输入捐款金额", trigger: "blur" }],
  purpose: [{ required: true, message: "请输入捐款用途", trigger: "blur" }],
  donationDate: [{ required: true, message: "请选择捐款时间", trigger: "change" }],
};

// 状态管理
const loading = ref(false);
// 删除对话框相关响应式变量
const dialogVisible = ref(false);
const dialogType = ref<"add" | "edit">("add");
const formRef = ref<InstanceType<typeof ElForm>>();

// 删除表单数据和校验规则
// const formData = reactive({...});
// const formRules = {...};

// 删除提交表单方法
// const submitForm = () => {...};

// 模拟数据
const mockData = reactive([
  {
    donor: "张三",
    amount: 500,
    purpose: "景区维护",
    donationDate: "2024-03-01 10:00:00",
    status: DonationStatus.Completed,
  },
  {
    donor: "李四",
    amount: 1000,
    purpose: "设备采购",
    donationDate: "2024-03-05 14:30:00",
    status: DonationStatus.Pending,
  },
  // ...更多测试数据...
]);
const resetForm = () => {};

const handleSearch = () => {};
// 计算当前页数据
const currentPageData = computed(() => {
  // 数据过滤逻辑
  const filteredData = mockData.filter((item) => {
    const matchesDonor = !searchForm.donor || item.donor.includes(searchForm.donor);
    const matchesPurpose = !searchForm.purpose || item.purpose.includes(searchForm.purpose);
    const matchesAmount =
      (!searchForm.minAmount || item.amount >= searchForm.minAmount) &&
      (!searchForm.maxAmount || item.amount <= searchForm.maxAmount);

    return matchesDonor && matchesPurpose && matchesAmount;
  });

  page.total = filteredData.length;
  return filteredData.slice((page.current - 1) * page.size, page.current * page.size);
});

const submitForm = () => {};

// 状态显示方法
const getStatusType = (status: number) => {
  switch (status) {
    case DonationStatus.Completed:
      return "success";
    case DonationStatus.Cancelled:
      return "danger";
    default:
      return "warning";
  }
};

const getStatusText = (status: number) => {
  switch (status) {
    case DonationStatus.Completed:
      return "已完成";
    case DonationStatus.Cancelled:
      return "已取消";
    default:
      return "待处理";
  }
};

// 其他方法（搜索、重置、分页等）需要根据实际业务逻辑补充完整

// 保留分页处理函数
const handleSizeChange = (val: number) => {
  page.size = val;
};

const handleCurrentChange = (val: number) => {
  page.current = val;
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);

  .search-card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .search-form {
      .form-item-group {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: flex-start;

        .separator {
          margin: 0 8px;
          color: #909399;
        }

        :deep(.el-form-item) {
          margin: 0;

          .el-input,
          .el-select {
            width: 240px;
          }
        }

        .search-buttons {
          margin-left: auto;

          .el-button {
            margin: 0;

            &:not(:first-child) {
              margin-left: 8px;
            }
          }
        }
      }
    }
  }

  .table-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .table-container {
      :deep(.el-table) {
        .price-info {
          .current-price {
            color: #f56c6c;
            font-weight: bold;
          }

          .original-price {
            margin-left: 8px;
            color: #909399;
            text-decoration: line-through;
            font-size: 12px;
          }
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      padding: 10px 20px;
      text-align: right;
    }
  }
}
</style>
