<template>
  <div class="asset-warehouse-container">
    <!-- 头部区域：标题、导航 -->
    <div class="header-wrapper">
      <div class="header">
        <div class="header-left">
          <img :src="gykjLogo" alt="中国美术学院 logo" class="gykj-logo" />
          <h1 class="title">资产公物仓平台</h1>
        </div>
        <div class="header-right">
          <div class="navbar__right">
            <span class="platform-link" @click="router.push('/warehouse')">
              <el-icon class="platform-icon"><HomeFilled /></el-icon>
              <span>首页</span>
            </span>

            <span class="platform-link" @click="router.push('/warehouse/analysis/Warehousebook')">
              <el-icon class="platform-icon"><Box /></el-icon>
              <span>资产入仓</span>
            </span>

            <span class="platform-link" @click="router.push('/warehouse/withdraw/add')">
              <el-icon class="platform-icon"><TakeawayBox /></el-icon>
              <span>资产领用</span>
              <el-badge
                v-if="assetCount.receive > 0"
                :value="assetCount.receive"
                :max="99"
                class="receive-badge"
              />
            </span>

            <span class="platform-link" @click="router.push('/dashboard')">
              <el-icon class="platform-icon"><Setting /></el-icon>
              <span>后台管理</span>
            </span>

            <!-- 用户头像（个人中心、注销登录等） -->
            <div class="user-profile-container">
              <UserProfile v-if="isLogin" />
              <span
                style="font-size: 14px; color: #888888; cursor: pointer"
                v-else
                @click="router.push('/login')"
              >
                登录
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="main-content">
      <!-- 筛选、搜索区域 -->
      <el-card class="filter-card">
        <div class="filter-section">
          <el-radio-group v-model="queryParams.currentType" size="large" @change="handleTypeChange">
            <el-radio-button label="all">全部 ({{ assetCount.all }})</el-radio-button>
            <el-radio-button label="computer">
              计算机设备({{ assetCount.computer }})
            </el-radio-button>
            <el-radio-button label="printer">打印设备({{ assetCount.printer }})</el-radio-button>
            <el-radio-button label="audio">视频、音频设备({{ assetCount.audio }})</el-radio-button>
            <el-radio-button label="furniture">
              家具、用具({{ assetCount.furniture }})
            </el-radio-button>
            <el-radio-button label="lifeAppliance">
              生活用电器({{ assetCount.lifeAppliance }})
            </el-radio-button>
            <el-radio-button label="airCondition">
              制冷空调设备({{ assetCount.airCondition }})
            </el-radio-button>
            <el-radio-button label="other">其他({{ assetCount.other }})</el-radio-button>
          </el-radio-group>
        </div>

        <div class="filter-section">
          <el-radio-group v-model="queryParams.assetType" size="large">
            <el-radio-button label="direct">直接入仓资产({{ assetCount.direct }})</el-radio-button>
            <el-radio-button label="public">处置公示资产({{ assetCount.public }})</el-radio-button>
          </el-radio-group>

          <div class="search-section">
            <el-input
              v-model="queryParams.searchKeyword"
              placeholder="请输入资产名称、编号或规格型号"
              class="search-input"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
              <template #append>
                <el-button @click="handleSearch">搜索</el-button>
              </template>
            </el-input>

            <div class="sort-section">
              <el-select
                v-model="queryParams.sortField"
                placeholder="排序字段"
                size="default"
                style="width: 120px"
                @change="handleSort"
              >
                <el-option label="入仓时间" value="rcsj" />
                <el-option label="购置日期" value="qdrq" />
                <el-option label="资产原值" value="je" />
              </el-select>
              <el-select
                v-model="queryParams.sortDirection"
                placeholder="排序方式"
                size="default"
                style="width: 100px"
                @change="handleSort"
              >
                <el-option label="正序" value="asc" />
                <el-option label="倒序" value="desc" />
              </el-select>
            </div>

            <el-button
              type="primary"
              @click="
                queryParams.searchKeyword = '';
                showAdvancedFilter = !showAdvancedFilter;
              "
            >
              <el-icon><Filter /></el-icon>
              高级筛选
            </el-button>
          </div>
        </div>

        <!-- 高级筛选区域 -->
        <el-collapse-transition>
          <div v-show="showAdvancedFilter" class="advanced-filter">
            <el-form :model="queryParams" label-width="100px" size="default">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="资产编号">
                    <el-input v-model="queryParams.zcbh" placeholder="请输入资产编号" clearable />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="资产名称">
                    <el-input v-model="queryParams.zcmc" placeholder="请输入资产名称" clearable />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item style="width: 400px" label="资产分类" prop="czXCodes">
                    <CascaderCzJyCode
                      width="400px"
                      v-model="queryParams.zclxbh"
                      type="CZ"
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="使用部门">
                    <DDLDeptList v-model="queryParams.sybm" clearable />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="品牌">
                    <el-input v-model="queryParams.brand" placeholder="请输入品牌" clearable />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="规格型号">
                    <el-input v-model="queryParams.spec" placeholder="请输入规格型号" clearable />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="资产原值">
                    <el-input-number
                      v-model="queryParams.minValue"
                      :min="0"
                      :step="1000"
                      placeholder="最小值"
                      style="width: 45%"
                    />
                    <span class="range-separator">至</span>
                    <el-input-number
                      v-model="queryParams.maxValue"
                      :min="0"
                      :step="1000"
                      placeholder="最大值"
                      style="width: 45%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="购置日期">
                    <el-date-picker
                      v-model="queryParams.purchaseTimeRange"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      style="width: 100%"
                      value-format="YYYY-MM-DD"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-button type="primary" @click="handleSearch">搜索</el-button>
                </el-col>
              </el-row>

              <!-- <div class="filter-buttons">
                <el-button @click="resetAdvancedFilter">重置</el-button>
                <el-button type="primary" @click="applyAdvancedFilter">应用筛选</el-button>
                <el-button type="info" @click="restoreAllData">恢复全部数据</el-button>
              </div> -->
            </el-form>
          </div>
        </el-collapse-transition>
      </el-card>

      <!-- 资产列表区域 -->
      <div class="asset-list-container" v-loading="loading">
        <el-empty v-if="assetList.length === 0" description="暂无符合条件的资产" />
        <div v-else class="asset-list">
          <el-card
            v-for="(asset, index) in assetList"
            :key="index"
            class="asset-card"
            shadow="hover"
            @click="viewAssetDetail(asset)"
            style="cursor: pointer"
          >
            <template #header>
              <div class="card-header">
                {{ asset.assetName }}
              </div>
            </template>
            <div class="asset-info">
              <div class="asset-img">
                <el-image
                  :src="asset.image || defaultImage"
                  fit="cover"
                  :initial-index="0"
                  alt="资产图片"
                >
                  <template #error>
                    <div class="placeholder-img">
                      <el-icon><Picture /></el-icon>
                      <span>暂无图片</span>
                    </div>
                  </template>
                </el-image>
                <!-- <div class="use-months">{{ asset.useMonths }}月</div> -->
              </div>
              <div class="asset-details">
                <div class="asset-specs">
                  <p>
                    <el-icon><LocationFilled /></el-icon>
                    编码：{{ asset.assetCode }}
                  </p>
                  <p>
                    <el-icon><Goods /></el-icon>
                    品牌：{{ asset.brand }}
                  </p>
                  <p>
                    <el-icon><Document /></el-icon>
                    规格：{{ asset.spec }}
                  </p>
                  <p>
                    <el-icon><Calendar /></el-icon>
                    购置时间：{{ asset.purchaseTime }}
                  </p>
                  <p>
                    <el-icon><OfficeBuilding /></el-icon>
                    所在部门：{{ asset.department }}
                  </p>
                  <p>
                    <el-icon><User /></el-icon>
                    使用人：{{ asset.user }}
                    <el-tag size="small" type="info" effect="plain" class="ml-5">
                      {{ asset.useMonths }}月
                    </el-tag>
                  </p>
                </div>
                <div class="asset-actions">
                  <el-button type="primary" size="small" @click.stop="handleReceive(asset)">
                    申请领用
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 分页 -->
        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="fetchAssetList()"
        />
      </div>

      <!-- 流程图展示区域 -->
      <el-card class="process-flow-card">
        <template #header>
          <div class="process-header">
            <h3>公物仓导图</h3>
            <el-tag effect="plain" type="info">流程指引</el-tag>
          </div>
        </template>
        <el-image :src="lc4Image" alt="公物仓流程图" fit="contain" class="process-image"></el-image>
      </el-card>
    </div>
    <!-- 资产详情弹窗 -->
    <el-drawer v-model="dialogVisible" title="资产详情" append-to-body size="85%">
      <el-tabs v-model="activeName" type="card" class="tabs">
        <el-tab-pane label="卡片信息" name="details">
          <div v-if="currentAsset" class="asset-detail-dialog">
            <div class="asset-detail-header">
              <h2>{{ currentAsset.assetName }}</h2>
              <el-tag :type="currentAsset.status || 'info'" effect="light">
                {{ getAssetStatusLabel(currentAsset.status) }}
              </el-tag>
            </div>

            <div class="asset-detail-content">
              <div class="asset-detail-image">
                <el-image
                  :src="currentAsset.image || defaultImage"
                  fit="cover"
                  :preview-src-list="currentAsset.image ? [currentAsset.image] : []"
                  style="width: 100%; height: 300px; border-radius: 8px"
                  show-progress
                  :z-index="9999"
                >
                  <template #error>
                    <div class="placeholder-img detail-placeholder">
                      <el-icon><Picture /></el-icon>
                      <span>暂无图片</span>
                    </div>
                  </template>
                </el-image>
              </div>

              <div class="asset-detail-info">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="资产编号">
                    {{ currentAsset.assetCode }}
                  </el-descriptions-item>
                  <el-descriptions-item label="资产类型">
                    {{ getAssetTypeTag(currentAsset).label }}
                  </el-descriptions-item>
                  <el-descriptions-item label="品牌">{{ currentAsset.brand }}</el-descriptions-item>
                  <el-descriptions-item label="规格型号">
                    {{ currentAsset.spec }}
                  </el-descriptions-item>
                  <el-descriptions-item label="购置日期">
                    {{ currentAsset.purchaseTime }}
                  </el-descriptions-item>
                  <el-descriptions-item label="入仓时间">
                    {{ currentAsset.entryTime || "未知" }}
                  </el-descriptions-item>
                  <el-descriptions-item label="资产原值">
                    {{ currentAsset.originalValue ? `¥${currentAsset.originalValue}` : "未知" }}
                  </el-descriptions-item>
                  <el-descriptions-item label="使用时长">
                    {{ currentAsset.useMonths }}个月
                  </el-descriptions-item>
                  <el-descriptions-item label="所在部门" :span="2">
                    {{ currentAsset.department }}
                  </el-descriptions-item>
                  <el-descriptions-item label="使用人" :span="2">
                    {{ currentAsset.user }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>

            <div class="asset-detail-footer">
              <el-button @click="dialogVisible = false">关闭</el-button>
              <el-button type="primary" @click="handleReceive(currentAsset)">申请领用</el-button>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="卡片信息" name="card">
          <CardInfo :key="cardGuid" :guid="cardGuid" />
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
    <!-- 底部版权信息 -->
    <div class="footer-copyright">
      <div class="footer-container">
        <img :src="gykjLogo" alt="XXXXXXXXXX" class="gykj-logo" />
        <div class="divider"></div>
        <div class="copyright-content">
          <div class="copyright-text">
            <el-icon><Location /></el-icon>
            版权所有：XXXXXXXXXXXXX
          </div>
          <div class="copyright-text">
            <el-icon><MapLocation /></el-icon>
            地址：XXXXXXXXXXXXXXXX
          </div>
          <div class="copyright-text">
            <el-icon><Service /></el-icon>
            技术支持：浙江工越信息科技有限公司
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import gwcAPI, { GwcHallQuery } from "@/api/properties/gwc";
import CascaderCzJyCode from "@/components/Properties/CascaderCzJyCode/index.vue";
import UserProfile from "@/layout/components/NavBar/components/UserProfile.vue";
import Card from "@/views/properties/store/components/Card.vue";
import {
  HomeFilled,
  Box,
  TakeawayBox,
  Setting,
  User,
  SwitchButton,
  Search,
  Filter,
  Picture,
  Goods,
  Document,
  Calendar,
  OfficeBuilding,
  LocationFilled,
  Location,
  MapLocation,
  Service,
} from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import lc4Image from "@/assets/lc4.png";
import router from "@/router";
import { getToken } from "@/utils/auth";
import gykjLogo from "@/assets/GYKJlogo.png";

// 默认图片
const defaultImage = "";
// 替代图片加载失败标志
const showFallbackError = ref(false);
const activeName = ref("details");
// 资产列表数据
const assetList = ref<any[]>([]);

const queryParams = reactive<any>({
  pageNum: 1,
  pageSize: 15,
  currentType: "all",
  searchKeyword: "",
  assetType: "direct",
  sortField: "qdrq",
  sortDirection: "asc",
});

const total = ref(0);
const cardGuid = ref("");
const assetCount = reactive({
  all: 0,
  computer: 0,
  printer: 0,
  audio: 0,
  furniture: 0,
  lifeAppliance: 0,
  airCondition: 0,
  other: 0,
  direct: 0,
  public: 0,
  receive: 0,
});
// 计算属性：显示当前筛选条件下的资产总数
const assetCountText = computed(() => {
  return total.value > 0 ? `(${total.value})` : "";
});

// 加载状态
const loading = ref(false);

// 资产类型映射表
const assetTypeMap: Record<string, string> = {
  "01": "computer", // 计算机设备
  "02": "printer", // 打印设备
  "03": "audio", // 视频、音频设备
  "04": "furniture", // 家具、用具
  "05": "lifeAppliance", // 生活用电器
  "06": "airCondition", // 制冷空调设备
  "99": "other", // 其他
};

// 资产状态映射表
const assetStatusMap: Record<string, string> = {
  "01": "new", // 全新
  "02": "good", // 良好
  "03": "used", // 已使用
  "04": "broken", // 损坏
};

/**
 * 从API获取资产列表数据
 */
const fetchAssetList = async () => {
  loading.value = true;
  try {
    const data = await gwcAPI.getPage(queryParams);

    if (data.list) {
      // 转换API返回的数据为前端需要的格式
      assetList.value = data.list.map((item) => ({
        id: item.id,
        assetCode: item.zcbh || "",
        assetName: item.zcmc || "",
        brand: item.pp || "",
        spec: item.ggxh || "",
        purchaseTime: formatDate(item.qdrq),
        department: item.sybm || "",
        user: item.syr || "",
        useMonths: calculateMonths(item.ysyqs),
        image:
          `${import.meta.env.VITE_APP_API_URL}${import.meta.env.VITE_APP_PREVIEW_API}${item.tpj}` ||
          "",
        type: assetTypeMap[item.zclxbh] || "other",
        status: assetStatusMap[item.gwczt] || "used",
        entryTime: formatDate(item.rcsj),
        originalValue: item.je || 0, // 使用API返回的资产原值
        guid: item.guid,
      }));
      total.value = data.total || 0;
    }
  } catch (error) {
    console.error("获取资产列表失败:", error);
    ElMessage.error("获取资产列表失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

const fetchAssetCount = async () => {
  try {
    const data = await gwcAPI.getAssetCount();
    assetCount.all = data.all || 0;
    assetCount.computer = data.computer || 0;
    assetCount.printer = data.printer || 0;
    assetCount.audio = data.audio || 0;
    assetCount.furniture = data.furniture || 0;
    assetCount.lifeAppliance = data.lifeAppliance || 0;
    assetCount.airCondition = data.airCondition || 0;
    assetCount.direct = data.direct || 0;
    assetCount.other = data.other || 0;
    assetCount.public = data.public || 0;
  } catch (error) {
    console.error("获取资产数量失败:", error);
    ElMessage.error("获取资产数量失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

/**
 * 格式化日期
 */
const formatDate = (dateStr?: string) => {
  if (!dateStr) return "";
  try {
    const date = new Date(dateStr);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
  } catch (e) {
    return dateStr;
  }
};

/**
 * 计算使用月数
 */
const calculateMonths = (days?: number) => {
  if (!days) return "0";
  // 将天数转换为月数（近似值）
  return Math.floor(days / 30).toString();
};

/**
 * 获取资产类型编码
 */
const getAssetTypeCode = (type: string): string => {
  const codeMap: Record<string, string> = {
    computer: "01",
    printer: "02",
    audio: "03",
    furniture: "04",
    lifeAppliance: "05",
    airCondition: "06",
    other: "99",
  };
  return codeMap[type] || "";
};

// 菜单和筛选相关
const activeMenu = ref("1");
const searchKeyword = ref("");
const showAdvancedFilter = ref(false);

// 获取资产类型标签
const getAssetTypeTag = (asset: any) => {
  const typeMap = {
    computer: { type: "primary", label: "计算机设备" },
    printer: { type: "success", label: "打印设备" },
    audio: { type: "warning", label: "视频音频设备" },
    furniture: { type: "info", label: "家具用具" },
    lifeAppliance: { type: "danger", label: "生活用电器" },
    airCondition: { type: "", label: "制冷空调设备" },
    other: { type: "info", label: "其他设备" },
  };

  return typeMap[asset.type as keyof typeof typeMap] || typeMap.other;
};

// 获取资产状态标签文本
const getAssetStatusLabel = (status?: string) => {
  const statusMap: Record<string, string> = {
    new: "全新",
    good: "良好",
    used: "已使用",
    broken: "损坏",
  };
  return statusMap[status || ""] || "未知";
};

// 菜单选择事件
const handleMenuSelect = (index: string) => {
  console.log("选中菜单：", index);
  // 可根据实际需求跳转路由或执行其他逻辑
};

// 搜索事件
const handleSearch = () => {
  // 重置页码并重新加载数据
  queryParams.pageNum = 1;
  fetchAssetList();
};
// 搜索事件
const handleSort = () => {
  // 重置页码并重新加载数据
  queryParams.pageNum = 1;
  fetchAssetList();
};
// 搜索事件
const handleTypeChange = () => {
  // 重置页码并重新加载数据
  queryParams.pageNum = 1;
  fetchAssetList();
};
// 资产领用事件
const handleReceive = (asset: any) => {
  const isLogin = !!getToken();
  //判断用户是否登录
  if (!isLogin) {
    router.push({ path: "/login" });
    return;
  }
  ElMessageBox.confirm(
    `确定要申请领用 ${asset.assetName}（编号：${asset.assetCode}）吗？`,
    "申请确认",
    {
      confirmButtonText: "确定申请",
      cancelButtonText: "取消",
      type: "info",
    }
  )
    .then(() => {
      // 从API提交领用申请
      gwcAPI.saveGwcSyb(asset.id, "3", "1", "0").then(() => {
        // 刷新资产列表数据
        fetchAssetList();
        // 从API获取资产列表数据
        fetchAssetList();

        gwcAPI.getAssetCountByReceive().then((res) => {
          // 获取资产数量
          assetCount.receive = res;
        });
        // 从API提交领用申请
        ElMessageBox.confirm(`该资产已成功加入资产领用表，是否提交？`, "申请确认", {
          confirmButtonText: "是，马上提交",
          cancelButtonText: "否，继续选择资产",
          type: "info",
        }).then(() => {
          // 从API提交领用申请
          router.push({ path: "/warehouse/withdraw/add" });
        });
      });
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 资产详情弹窗相关
const dialogVisible = ref(false);
const currentAsset = ref<any | null>(null);

// 查看资产详情
const viewAssetDetail = (asset: any) => {
  cardGuid.value = asset.guid;

  // 设置当前选中的资产
  currentAsset.value = asset;
  // 显示弹窗
  dialogVisible.value = true;
};

watch(
  () => [searchKeyword.value, queryParams.currentType, queryParams.assetType],
  () => {
    fetchAssetList();
  }
);
const isLogin = ref(false);
// 页面加载时初始化数据
onMounted(() => {
  //从API获取资产数据数量
  fetchAssetCount();
  isLogin.value = !!getToken();
  // 从API获取资产列表数据
  fetchAssetList();
  //判断用户是否登录
  if (isLogin.value) {
    //开始调用接口
    gwcAPI.getAssetCountByReceive().then((res) => {
      console.log(res);
      // 获取资产数量
      assetCount.receive = res;
    });
  }
});
</script>

<style lang="scss" scoped>
// 导航栏优化样式
.navbar__right {
  display: flex;
  align-items: center;
  height: 64px;

  .platform-link {
    position: relative;
    display: inline-flex;
    align-items: center;
    min-width: 40px;
    height: 64px;
    padding: 0 16px;
    color: var(--el-text-color);
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0;

    &:hover {
      background-color: rgba(64, 158, 255, 0.1);
      color: var(--el-color-primary);
      transform: translateY(-1px);

      .platform-icon {
        transform: scale(1.1);
      }
    }

    // 选中状态样式
    &.active {
      color: var(--el-color-primary);
      background-color: rgba(64, 158, 255, 0.08);
      font-weight: 600;

      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background-color: var(--el-color-primary);
      }
    }

    .platform-icon {
      margin-right: 6px;
      font-size: 16px;
      transition: transform 0.2s ease;
    }

    // 徽章样式
    .receive-badge {
      position: absolute;
      top: 8px;
      right: 8px;
      z-index: 1;

      :deep(.el-badge__content) {
        font-size: 10px;
        padding: 0 4px;
        height: 16px;
        line-height: 16px;
        border-radius: 8px;
      }
    }
  }

  // 用户头像区域与导航项分隔
  .user-profile-container {
    margin-left: 12px;
    padding-left: 12px;
    border-left: 1px solid var(--el-border-color-light);
    height: 64px;
    display: flex;
    align-items: center;
  }
}

// 响应式调整
@media (max-width: 1200px) {
  .header {
    max-width: 100%;
    padding: 0 16px;
  }

  .main-content {
    max-width: 100%;
    padding: 20px 16px 40px;
  }
}

@media (max-width: 992px) {
  .navbar__right {
    .platform-link {
      padding: 0 12px;
      font-size: 13px;

      .platform-icon {
        font-size: 15px;
      }
    }
  }

  .title {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 0 12px;
  }

  .main-content {
    padding: 16px 12px 32px;
  }

  .navbar__right {
    .platform-link span {
      display: none; // 小屏幕隐藏文字，只保留图标
    }

    .platform-link {
      padding: 0 8px;
      min-width: 32px;

      .platform-icon {
        margin-right: 0;
        font-size: 16px;
      }
    }

    .user-profile-container {
      margin-left: 8px;
      padding-left: 8px;
    }
  }

  .gykj-logo {
    height: 32px;
    margin-right: 12px;
  }

  .title {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .header-left {
    .title {
      display: none; // 超小屏幕隐藏标题
    }
  }

  .navbar__right {
    .platform-link {
      padding: 0 6px;
      min-width: 28px;
    }
  }
}

.asset-warehouse-container {
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
}

// 头部样式
.header-wrapper {
  position: sticky;
  top: 0;
  z-index: 999;
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
  margin-bottom: 0;
}

.header {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
}

.gykj-logo {
  height: 36px;
  margin-right: 16px;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
  letter-spacing: 0.5px;
}

.header-right {
  display: flex;
  align-items: center;
}

.el-menu-demo {
  border-bottom: none;
}

// 主内容区域
.main-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px 20px 40px;
  min-height: calc(100vh - 64px);
}

// 筛选卡片
.filter-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
  box-shadow: var(--el-box-shadow-light);
  background-color: var(--el-bg-color);
  .filter-section {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;

    &:last-child {
      margin-bottom: 0;
    }

    .filter-label {
      font-weight: bold;
      margin-bottom: 10px;
      color: #606266;
    }

    .search-section {
      display: flex;
      align-items: center;
      margin-left: auto;

      .search-input {
        width: 300px;
        margin-right: 15px;
      }
    }
  }

  .search-section {
    display: flex;
    align-items: center;
    gap: 15px;

    .search-input {
      flex: 1;
      max-width: 500px;
    }
  }

  .advanced-filter {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px dashed #dcdfe6;

    .filter-buttons {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
      gap: 10px;
    }
  }
}

// 资产列表
.asset-list-container {
  background-color: transparent;
}

.asset-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-bottom: 25px;
}

.asset-card {
  height: auto;
  border-radius: 8px;
  transition: all 0.3s;
  position: relative;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);

    &::after {
      content: "点击查看详情";
      position: absolute;
      bottom: 10px;
      right: 10px;
      background-color: rgba(64, 158, 255, 0.8);
      color: white;
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      z-index: 1;
    }
  }

  .card-header {
    display: flex;
    align-items: center;
    padding: 5px 10px;
  }

  :deep(.el-card__header) {
    padding: 10px;
  }

  :deep(.el-card__body) {
    padding: 10px;
  }

  .asset-info {
    display: flex;
    padding: 2px 0;
    height: 150px; /* 固定高度 */
    overflow: hidden;
  }

  .asset-img {
    width: 130px;
    height: 130px;
    margin-right: 15px;
    border-radius: 4px;
    overflow: hidden;
    position: relative;

    .el-image {
      width: 100%;
      height: 100%;
    }

    .use-months {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: rgba(0, 0, 0, 0.6);
      color: white;
      padding: 2px 5px;
      font-size: 12px;
      text-align: center;
    }
  }

  .placeholder-img {
    width: 100%;
    height: 100%;
    background-color: #f5f7fa;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;

    .el-icon {
      font-size: 24px;
      margin-bottom: 5px;
    }
  }

  .asset-details {
    flex: 1;
    display: flex;
    flex-direction: column;

    .asset-name {
      font-size: 14px;
      font-weight: bold;
      margin: 0 0 3px;
      color: #303133;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .asset-specs {
      flex: 1;
      overflow: hidden;

      p {
        margin: 1px 0;
        color: #606266;
        font-size: 12px;
        display: flex;
        align-items: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        .el-icon {
          margin-right: 3px;
          color: #909399;
          flex-shrink: 0;
        }
      }
    }

    .asset-actions {
      margin-top: 4px;
      display: flex;
      gap: 5px;
    }
  }
}

// 分页
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  margin-bottom: 30px;
}

// 流程图卡片
.process-flow-card {
  margin-top: 30px;
  margin-bottom: 40px;
  border-radius: 8px;

  .process-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .process-content {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
    width: 100%;
    border-radius: 8px;
    padding: 20px 0;

    .process-image {
      width: 100%;
      min-height: 400px;
      max-height: 700px; /* 增加最大高度 */
      border-radius: 8px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }

    /* 响应式样式 */
    @media (max-width: 768px) {
      .process-image {
        min-height: 300px;
      }
    }

    @media (max-width: 480px) {
      .process-image {
        min-height: 200px;
      }
    }

    .process-description {
      color: #606266;
      margin-bottom: 20px;
      font-size: 14px;
      line-height: 1.6;
    }

    .process-image-container {
      display: flex;
      justify-content: center;
      margin: 20px 0;

      .process-image {
        max-width: 100%;
        max-height: 500px;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      .image-placeholder,
      .image-error {
        width: 100%;
        min-height: 400px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        color: #909399;
        border-radius: 8px;
        border: 1px dashed #dcdfe6;

        .fallback-image {
          width: 100%;
          max-height: 600px;
          object-fit: contain;
          border-radius: 8px;
        }

        .fallback-error {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background-color: rgba(248, 249, 250, 0.9);
        }

        .el-icon {
          font-size: 48px;
          margin-bottom: 15px;
        }

        span {
          font-size: 16px;
        }

        /* 响应式样式 */
        @media (max-width: 768px) {
          min-height: 300px;
        }

        @media (max-width: 480px) {
          min-height: 200px;

          .el-icon {
            font-size: 36px;
            margin-bottom: 10px;
          }

          span {
            font-size: 14px;
          }
        }
      }
    }

    .process-legend {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      gap: 20px;
      margin-top: 20px;

      .legend-item {
        display: flex;
        align-items: center;

        .legend-color {
          width: 16px;
          height: 16px;
          border-radius: 4px;
          margin-right: 8px;

          &.start {
            background-color: #67c23a;
          }

          &.process {
            background-color: #409eff;
          }

          &.decision {
            background-color: #e6a23c;
          }

          &.end {
            background-color: #f56c6c;
          }
        }

        span {
          font-size: 12px;
          color: #606266;
        }
      }
    }
  }
}

// 工具类
.ml-5 {
  margin-left: 5px;
}

.ml-10 {
  margin-left: 10px;
}

// 资产详情弹窗样式
.asset-detail-dialog {
  padding: 10px;

  .asset-detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-size: 22px;
      color: #303133;
    }
  }

  .asset-detail-content {
    display: flex;
    flex-direction: column;
    gap: 20px;

    @media (min-width: 768px) {
      flex-direction: row;
    }

    .asset-detail-image {
      flex: 1;
      min-width: 300px;

      .detail-placeholder {
        height: 300px;
        border-radius: 8px;
      }
    }

    .asset-detail-info {
      flex: 2;
    }
  }

  .asset-detail-footer {
    margin-top: 30px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}

// 弹窗内的描述列表样式
:deep(.el-descriptions) {
  .el-descriptions__label {
    font-weight: bold;
  }
}

// 底部版权信息样式
.footer-copyright {
  background-color: #0078d4;
  color: white;
  padding: 15px 0;
  margin-top: 30px;
  width: 100%;
  height: 200px;

  .footer-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .gykj-logo {
    height: 30px;
    margin-left: 20px;
    margin-right: 20px;
  }

  .divider {
    height: 100px;
    width: 1px;
    background-color: white;
    margin: 0 20px;
  }

  .copyright-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .copyright-text {
      margin: 5px 0;
      font-size: 14px;
      display: flex;
      align-items: center;

      .el-icon {
        margin-right: 8px;
      }
    }
  }
}
</style>
