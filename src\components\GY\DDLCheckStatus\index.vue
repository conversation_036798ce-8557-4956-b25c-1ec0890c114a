<template>
  <!-- 支持搜索 -->
  <el-select v-model="_modelValue" clearable :loading="loading" placeholder="请选择">
    <el-option label="待审核" value="ds" />
    <el-option label="审结" value="sj" />
    <el-option label="终止" value="zz" />
  </el-select>
</template>

<script setup lang="ts">
const loading = ref(false);
//组件参数
const props = defineProps({
  modelValue: {
    type: String,
    required: true,
  },
});

//子传父
const emit = defineEmits(["update:modelValue"]);

const _modelValue = computed({
  get: () => {
    return props.modelValue;
  },
  set: (val) => {
    emit("update:modelValue", val);
  },
});
</script>
