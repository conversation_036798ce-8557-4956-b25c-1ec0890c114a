<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item label="处置类型" prop="czlx">
            <DDLYcode v-model="queryParams.czlx" ycode="0102" />
          </el-form-item>
          <el-form-item label="资产编号" prop="zcbh">
            <el-input v-model="queryParams.zcbh" placeholder="资产编号" />
          </el-form-item>
          <el-form-item label="资产名称" prop="zcmc">
            <el-input v-model="queryParams.zcmc" placeholder="资产名称" />
          </el-form-item>
          <el-form-item label="使用部门" prop="sybm">
            <DDLDeptList v-model="queryParams.sybm" />
          </el-form-item>
          <el-form-item label="保管员" prop="syr">
            <DDLUserList
              :key="queryParams.sybm"
              v-model="queryParams.syr"
              :dcode="queryParams.sybm"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon><Search /></template>
              搜索
            </el-button>
            <!-- <el-button @click="handleResetQuery">
              <template #icon><Refresh /></template>
              重置
            </el-button> -->
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="已处置资产列表" />
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        stripe
        border
      >
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="资产编号" prop="zcbh" width="130" align="center" fixed />
        <el-table-column label="资产名称" prop="zcmc" width="200" align="center" fixed />
        <el-table-column label="处置类型" prop="syxzname" width="150" align="center" />
        <el-table-column label="规格类型" prop="ggxh" width="150" align="center" />
        <el-table-column label="原值(元)" prop="je" width="100" align="center" />
        <el-table-column label="数量" prop="sl" width="55" align="center" />
        <el-table-column label="核销日期" prop="hxsj" width="140" align="center" />
        <el-table-column label="保管员" prop="syrname" width="100" align="center" />
        <el-table-column label="使用部门" prop="sybmname" width="200" align="center" />
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :page-size="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "dispose",
  inheritAttrs: false,
});

import { useRouter } from "vue-router";
import disposeAPI, { disposedPageQuery } from "@/api/properties/dispose";

//页面参数
const queryFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);

//请求列表数据
const pageData = ref<any[]>([]);
const queryParams = reactive<disposedPageQuery>({
  pageNum: 1,
  pageSize: 20,
});
const handleQuery = () => {
  loading.value = true;
  console.log(queryParams);
  disposeAPI
    .getDisposedPage(queryParams)
    .then((res) => {
      pageData.value = res.list;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
};

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.el-form {
  width: auto;
}
</style>
