<template>
  <div class="dept-tree-item">
    <div
      class="dept-node"
      :class="{
        'has-children': hasChildren,
        expanded: dept.expanded,
        highlighted: isHighlighted,
      }"
      :style="{ paddingLeft: `${level * 20 + 16}px` }"
      @click="handleNodeClick"
    >
      <!-- 展开/收起图标 -->
      <div class="expand-icon" @click.stop="handleExpandClick">
        <svg-icon
          v-if="hasChildren"
          :icon-class="dept.expanded ? 'arrow-down' : 'arrow-right'"
          size="12px"
        />
      </div>

      <!-- 部门图标 -->
      <div class="dept-icon">
        <svg-icon icon-class="department" size="16px" />
      </div>

      <!-- 部门信息 -->
      <div class="dept-info">
        <div class="dept-name" v-html="highlightKeyword(dept.name)"></div>
        <div class="dept-meta">
          <span class="dept-leader">{{ dept.leader || "无负责人" }}</span>
          <span class="dept-count">{{ dept.userCount || 0 }}人</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="dept-actions">
        <button v-if="dept.phone" class="action-btn" @click.stop="handleContact">
          <svg-icon icon-class="phone" size="12px" />
        </button>
        <button class="action-btn" @click.stop="handleSelect">
          <svg-icon icon-class="info" size="12px" />
        </button>
      </div>
    </div>

    <!-- 子部门 -->
    <div v-if="hasChildren && dept.expanded" class="dept-children">
      <DeptTreeItem
        v-for="child in dept.children"
        :key="child.id"
        :dept="child"
        :level="level + 1"
        :search-keyword="searchKeyword"
        @select="$emit('select', $event)"
        @expand="$emit('expand', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { ElMessage } from "element-plus";

defineOptions({
  name: "DeptTreeItem",
});

interface Props {
  dept: any;
  level?: number;
  searchKeyword?: string;
}

interface Emits {
  (e: "select", dept: any): void;
  (e: "expand", deptId: number): void;
}

const props = withDefaults(defineProps<Props>(), {
  level: 0,
  searchKeyword: "",
});

const emit = defineEmits<Emits>();

// 是否有子部门
const hasChildren = computed(() => {
  return props.dept.children && props.dept.children.length > 0;
});

// 是否高亮显示（匹配搜索关键词）
const isHighlighted = computed(() => {
  if (!props.searchKeyword) return false;
  return (
    props.dept.name.toLowerCase().includes(props.searchKeyword.toLowerCase()) ||
    (props.dept.leader &&
      props.dept.leader.toLowerCase().includes(props.searchKeyword.toLowerCase()))
  );
});

// 处理节点点击
const handleNodeClick = () => {
  if (hasChildren.value) {
    handleExpandClick();
  } else {
    handleSelect();
  }
};

// 处理展开/收起
const handleExpandClick = () => {
  if (hasChildren.value) {
    emit("expand", props.dept.id);
  }
};

// 处理选择
const handleSelect = () => {
  emit("select", props.dept);
};

// 处理联系
const handleContact = () => {
  if (props.dept.phone) {
    window.location.href = `tel:${props.dept.phone}`;
  } else {
    ElMessage.warning("该部门未设置联系电话");
  }
};

// 高亮关键词
const highlightKeyword = (text: string) => {
  if (!props.searchKeyword) return text;
  const regex = new RegExp(`(${props.searchKeyword})`, "gi");
  return text.replace(regex, "<mark>$1</mark>");
};
</script>

<style lang="scss" scoped>
.dept-tree-item {
  background: white;
  margin-bottom: 1px;
}

.dept-node {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-left: 3px solid transparent;

  &:hover {
    background: #f8f9fa;
  }

  &:active {
    background: #f0f0f0;
  }

  &.highlighted {
    border-left-color: var(--el-color-primary);
    background: #f0f7ff;
  }
}

.expand-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  color: #999;
  cursor: pointer;

  &:hover {
    color: #666;
  }
}

.dept-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f7ff;
  border-radius: 50%;
  margin-right: 12px;
  color: var(--el-color-primary);
}

.dept-info {
  flex: 1;
  min-width: 0;
}

.dept-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;

  :deep(mark) {
    background: #fff2e6;
    color: #fa8c16;
    padding: 0 2px;
    border-radius: 2px;
  }
}

.dept-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #999;
}

.dept-leader {
  &::before {
    content: "👤 ";
  }
}

.dept-count {
  &::before {
    content: "👥 ";
  }
}

.dept-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s;

  .dept-node:hover & {
    opacity: 1;
  }
}

.action-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: #f5f5f5;
  border-radius: 50%;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;

  &:hover {
    background: #e8e8e8;
    color: #333;
  }

  &:active {
    transform: scale(0.95);
  }
}

.dept-children {
  background: #fafafa;
}

// 不同层级的样式
.dept-node {
  // 一级部门
  &[style*="padding-left: 16px"] {
    .dept-icon {
      background: #e6f7ff;
      color: #1890ff;
    }
  }

  // 二级部门
  &[style*="padding-left: 36px"] {
    .dept-icon {
      background: #f6ffed;
      color: #52c41a;
    }
  }

  // 三级部门
  &[style*="padding-left: 56px"] {
    .dept-icon {
      background: #fff7e6;
      color: #fa8c16;
    }
  }

  // 四级及以上部门
  &[style*="padding-left: 76px"],
  &[style*="padding-left: 96px"],
  &[style*="padding-left: 116px"] {
    .dept-icon {
      background: #fff2f0;
      color: #ff4d4f;
    }
  }
}
</style>
