<template>
  <div class="mobile-login">
    <!-- 登录头部 -->
    <div class="login-header">
      <div class="logo-section">
        <img :src="logo" alt="logo" class="logo" />
        <h1 class="title">{{ defaultSettings.title }}</h1>
      </div>
    </div>

    <!-- 登录表单 -->
    <div class="login-form">
      <div class="form-container">
        <!-- 用户名 -->
        <div class="form-item">
          <div class="input-wrapper">
            <svg-icon icon-class="user" class="input-icon" />
            <input
              v-model="loginData.username"
              type="text"
              placeholder="请输入用户名"
              class="form-input"
              @keyup.enter="handleLoginSubmit"
            />
          </div>
        </div>

        <!-- 密码 -->
        <div class="form-item">
          <div class="input-wrapper">
            <svg-icon icon-class="password" class="input-icon" />
            <input
              v-model="loginData.password"
              :type="showPassword ? 'text' : 'password'"
              placeholder="请输入密码"
              class="form-input"
              @keyup.enter="handleLoginSubmit"
            />
            <div class="password-toggle" @click="showPassword = !showPassword">
              <svg-icon :icon-class="showPassword ? 'eye' : 'eye-close'" />
            </div>
          </div>
        </div>

        <!-- 验证码 -->
        <div class="form-item">
          <div class="input-wrapper captcha-wrapper">
            <svg-icon icon-class="captcha" class="input-icon" />
            <input
              v-model="loginData.captchaCode"
              type="text"
              placeholder="请输入验证码"
              class="form-input captcha-input"
              @keyup.enter="handleLoginSubmit"
            />
            <div class="captcha-image" @click="getCaptcha">
              <img v-if="captchaBase64" :src="captchaBase64" alt="验证码" />
              <div v-else class="captcha-placeholder">点击获取</div>
            </div>
          </div>
        </div>

        <!-- 记住密码 -->
        <div class="form-item checkbox-item">
          <label class="checkbox-wrapper">
            <input v-model="rememberMe" type="checkbox" class="checkbox" />
            <span class="checkbox-text">记住密码</span>
          </label>
          <div class="forgot-password">
            <span @click="handleForgotPassword">忘记密码？</span>
          </div>
        </div>

        <!-- 登录按钮 -->
        <div class="form-item">
          <button
            :disabled="loading"
            class="login-button"
            :class="{ loading }"
            @click="handleLoginSubmit"
          >
            <span v-if="loading" class="loading-spinner"></span>
            {{ loading ? '登录中...' : '登录' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="login-footer">
      <div class="version-info">
        版本 {{ defaultSettings.version }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

import AuthAPI, { type LoginData } from '@/api/auth'
import defaultSettings from '@/settings'
import { useUserStore, useDictStore } from '@/store'

const router = useRouter()
const userStore = useUserStore()
const dictStore = useDictStore()

const logo = ref(new URL("../../../assets/logo.png", import.meta.url).href)
const loading = ref(false)
const showPassword = ref(false)
const rememberMe = ref(false)
const captchaBase64 = ref('')

const loginData = reactive<LoginData>({
  username: '',
  password: '',
  captchaKey: '',
  captchaCode: '',
})

// 获取验证码
const getCaptcha = async () => {
  try {
    const data = await AuthAPI.getCaptcha()
    loginData.captchaKey = data.captchaKey
    captchaBase64.value = data.captchaBase64
  } catch (error) {
    ElMessage.error('获取验证码失败')
  }
}

// 登录提交
const handleLoginSubmit = async () => {
  // 简单验证
  if (!loginData.username) {
    ElMessage.error('请输入用户名')
    return
  }
  if (!loginData.password) {
    ElMessage.error('请输入密码')
    return
  }
  if (!loginData.captchaCode) {
    ElMessage.error('请输入验证码')
    return
  }

  loading.value = true
  
  try {
    await userStore.login(loginData)
    await userStore.getUserInfo()
    await dictStore.loadDictionaries()
    
    ElMessage.success('登录成功')
    // 跳转到移动端首页
    router.push('/mobile/dashboard')
  } catch (error) {
    getCaptcha() // 重新获取验证码
  } finally {
    loading.value = false
  }
}

// 忘记密码
const handleForgotPassword = () => {
  ElMessage.info('请联系管理员重置密码')
}

onMounted(() => {
  getCaptcha()
})
</script>

<style lang="scss" scoped>
.mobile-login {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px 40px;

  .logo-section {
    text-align: center;
    color: white;

    .logo {
      width: 80px;
      height: 80px;
      margin-bottom: 16px;
    }

    .title {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
    }
  }
}

.login-form {
  flex: 1;
  padding: 0 20px;

  .form-container {
    background: white;
    border-radius: 12px;
    padding: 32px 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
}

.form-item {
  margin-bottom: 20px;

  &.checkbox-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
  }
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f8f9fa;
  transition: border-color 0.2s;

  &:focus-within {
    border-color: var(--el-color-primary);
    background: white;
  }

  &.captcha-wrapper {
    .form-input {
      flex: 1;
    }
  }
}

.input-icon {
  margin: 0 12px;
  color: #999;
}

.form-input {
  flex: 1;
  height: 48px;
  border: none;
  background: transparent;
  font-size: 16px;
  outline: none;

  &::placeholder {
    color: #999;
  }

  &.captcha-input {
    border-right: 1px solid #e0e0e0;
  }
}

.password-toggle {
  padding: 0 12px;
  cursor: pointer;
  color: #999;
}

.captcha-image {
  width: 100px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 0 7px 7px 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0 7px 7px 0;
  }

  .captcha-placeholder {
    font-size: 12px;
    color: #999;
  }
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;

  .checkbox {
    margin-right: 8px;
  }

  .checkbox-text {
    font-size: 14px;
    color: #666;
  }
}

.forgot-password {
  span {
    font-size: 14px;
    color: var(--el-color-primary);
    cursor: pointer;
  }
}

.login-button {
  width: 100%;
  height: 48px;
  background: var(--el-color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled) {
    background: var(--el-color-primary-light-3);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.loading {
    background: var(--el-color-primary-light-3);
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.login-footer {
  padding: 20px;
  text-align: center;

  .version-info {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
  }
}
</style>
