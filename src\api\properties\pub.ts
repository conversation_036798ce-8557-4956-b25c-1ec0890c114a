import request from "@/utils/request";

const PUB_BASE_URL = "/api/v1/pub";

const pubAPI = {
  /** 获取公共资源库分页数据/api/v1/pub/adjustMarket/page */
  getAdjustPage(data: pubAdjustQuery) {
    return request<any, PageResult<any[]>>({
      url: `${PUB_BASE_URL}/adjustMarket/page`,
      method: "post",
      data: data,
    });
  },

  // 上下架管理的列表页面/api/v1/pub/public/page
  getPutOnPage(data: pubPutOnQuery) {
    return request<any, PageResult<any[]>>({
      url: `${PUB_BASE_URL}/public/page`,
      method: "post",
      data: data,
    });
  },

  //待处置公示/api/v1/pub/pubDispose/page
  getPubDispose(data: pubPubDisposeQuery) {
    return request<any, PageResult<any[]>>({
      url: `${PUB_BASE_URL}/pubDispose/page`,
      method: "post",
      data: data,
    });
  },

  //强行释放接口/api/v1/pub/adjustMarket/release
  release(syguid: string, zcguid: string) {
    return request({
      url: `${PUB_BASE_URL}/adjustMarket/release`,
      method: "post",
      params: {
        syguid: syguid,
        zcguid: zcguid,
      },
    });
  },

  //上架/api/v1/pub/putOn/putaway/{guids}
  putOn(data: { type: string }, guids: string) {
    return request({
      url: `${PUB_BASE_URL}/putOn/putaway/${guids}`,
      method: "post",
      params: data,
    });
  },
  //下架/api/v1/pub/putOn/remove/{guids}
  putDown(guids: string) {
    return request({
      url: `${PUB_BASE_URL}/putOn/remove/${guids}`,
      method: "post",
    });
  },
};

export interface pubAdjustQuery extends PageQuery {
  code?: string;
  type?: string;
  sf?: string;
}

export interface pubPutOnQuery extends PageQuery {
  zcbh?: string;
  zcmc?: string;
  zczt?: string;
  zcxz?: string;
}

export interface pubPubDisposeQuery extends PageQuery {
  czbh?: string;
  czlx?: string;
  djbm?: string;
  djsj?: [];
}

export default pubAPI;
