import request from "@/utils/request";

const TRANSFER_BASE_URL = "/api/v1/transfers";

const transferAPI = {
  /** 获取调拨分页数据 */
  getPage(queryParams?: transferPageQuery) {
    return request<any, PageResult<transferPageVO[]>>({
      url: `${TRANSFER_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取调拨表单数据
   *
   * @param id transferID
   * @returns transfer表单数据
   */
  getFormData(id: number) {
    return request<any, transferForm>({
      url: `${TRANSFER_BASE_URL}/form/${id}`,
      method: "get",
    });
  },

  /** 添加调拨*/
  add(data: transferForm): Promise<boolean> {
    return request<any, boolean>({
      url: `${TRANSFER_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新调拨
   *
   * @param id transferID
   * @param data transfer表单数据
   */
  update(id: number, data: transferForm): Promise<boolean> {
    return request<any, boolean>({
      url: `${TRANSFER_BASE_URL}/update/${id}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 提交调拨
   *
   * @param id transferID
   * @param data transfer表单数据
   */
  submit(guid: string,data?: any) {
    return request({
      url: `${TRANSFER_BASE_URL}/submit/${guid}`,
      method: "post",
      data: data,
    });
  },

    /**
   * 调拨单成功回调接口
   *
   * @param id transferID
   * @param data transfer表单数据
   */
    successSubmit(guid: string) {
      return request({
        url: `${TRANSFER_BASE_URL}/submit/success/${guid}`,
        method: "post",
      });
    },

  /**
   * 批量删除调拨，多个以英文逗号(,)分割
   *
   * @param ids 调拨ID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${TRANSFER_BASE_URL}/delete/${ids}`,
      method: "get",
    });
  },

  //———————————————————————————————— 明细相关接口————————————————————————————
  /** 添加调拨明细*/
  addDetail(data: { dbguid: string; zcguid: string }) {
    return request({
      url: `${TRANSFER_BASE_URL}/details/add`,
      method: "post",
      data: data,
    });
  },

  // 调拨单明细查询
  getDetailsPage(queryParams?: transferDetailsPageQuery) {
    return request<any, PageResult<transferDetailPageVO[]>>({
      url: `${TRANSFER_BASE_URL}/details/page`,
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 批量删除调拨单明细，多个以英文逗号(,)分割
   *
   * @param ids 调拨单明细ID字符串，多个以英文逗号(,)分割
   */
  deleteDetailsByIds(ids: string) {
    return request({
      url: `${TRANSFER_BASE_URL}/details/delete/${ids}`,
      method: "get",
    });
  },

  //回收/api/v1/transfers/callback/{guid}
  callBack(guid: string) {
    return request({
      url: `${TRANSFER_BASE_URL}/callback/${guid}`,
      method: "get",
    });
  },

  //回收/api/v1/transfers/node/list
  getNodeList() {
    return request({
      url: `${TRANSFER_BASE_URL}/node/list`,
      method: "get",
    });
  },
};

export default transferAPI;

/** 调拨分页查询参数 */
export interface transferPageQuery extends PageQuery {
  /** 调拨单号 */
  dbdh?: string;
  /** 调出部门 */
  dcbm?: string;
  /** 调入部门 */
  drbm?: string;
  /** 数据编码 */
  netcode?: string;
  /** 数据状态 */
  sjzt?: string;
  /** 登记时间 */
  djsj?: string[];
  //查询类型，是新增页审核页还是查询页
  type?: string;
  //查询代办/已办
  checkstatus?: number;
}

/** 调拨表单对象 */
export interface transferForm {
  /** ID */
  id?: number;
  /** Guid */
  guid?: string;
  /** 调拨单号 */
  dbdh?: string;
  /** 调出部门 */
  dcbm?: string;
  /** 调出部门管理员 */
  dcbmgly?: string;
  /** 调入部门 */
  drbm?: string;
  /** 调入人 */
  drr?: string;
  /** 调入部门管理员 */
  drbmgly?: string;
  /** 调拨理由 */
  dbly?: string;
  /** 数据编码 */
  netcode?: string;
  /** 数据状态 */
  sjzt?: string;
  /** 登记部门 */
  djbm?: string;
  /** 登记人 */
  djr?: string;
  /** 登记时间 */
  djsj?: string;
  /** 备注 */
  notes?: string;
  /** 终止理由 */
  by1?: string;
  /** 审结时间 */
  sjsj?: string;
  /** 调拨类型1正常调拨，2台账修改，3保管员变更 */
  dblx?: string;
}

/** 调拨分页对象 */
export interface transferPageVO {
  /** ID */
  id?: number;
  /** Guid */
  guid?: string;
  /** 调拨单号 */
  dbdh?: string;
  /** 调出部门 */
  dcbm?: string;
  /** 调出部门管理员 */
  dcbmgly?: string;
  /** 调入部门 */
  drbm?: string;
  /** 调入人 */
  drr?: string;
  /** 调入部门管理员 */
  drbmgly?: string;
  /** 调拨理由 */
  dbly?: string;
  /** 数据编码 */
  netcode?: string;
  /** 数据状态 */
  sjzt?: string;
  /** 登记部门 */
  djbm?: string;
  /** 登记人 */
  djr?: string;
  /** 登记时间 */
  djsj?: Date;
  /** 备注 */
  notes?: string;
  /** 终止理由 */
  by1?: string;
  /** 审结时间 */
  sjsj?: Date;
  /** 调拨类型1正常调拨，2台账修改，3保管员变更 */
  dblx?: string;
}

/** 调拨列表查询参数 */
export interface transferDetailsPageQuery extends PageQuery {
  /** 调拨单guid */
  dbguid: string;
}

/** 调拨分页对象 */
export interface transferDetailPageVO {
  bgr?: string;
  cdguid?: string;
  cfdd?: string;
  dbguid?: string;
  dj?: string;
  ggxh?: string;
  guid?: string;
  id?: string;
  je?: string;
  jldw?: string;
  pp?: string;
  sl?: string;
  sybm?: string;
  syr?: string;
  zcbh?: string;
  zcmc?: string;
}

