<template>
  <div>
    <el-card>
      <template #header>
        <div class="flex-x-between">
          <div class="flex-y-center">调剂市场</div>
          <el-button type="primary" size="small" plain @click="router.push('/pub/adjustMarket')">
            更多
          </el-button>
        </div>
      </template>

      <ScrollbarLoop height="300" :datacount="pageData.length">
        <el-table :data="pageData" style="width: 100%" :show-header="false">
          <el-table-column prop="zcmc" label="资产名称" />
          <el-table-column prop="zcbh" label="资产编号" width="150" />
          <el-table-column label="操作" fixed="right" width="80">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                @click="handleRowView(scope.row.guid, scope.row.zcguid)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </ScrollbarLoop>
    </el-card>

    <!-- 弹出框 -->
    <el-dialog v-model="dialogVisible" title="调剂资产清单" width="60%" height="60%">
      <el-table v-loading="loading" :data="pageData" highlight-current-row :border="true">
        <el-table-column prop="name" label="name" />
        <el-table-column prop="data" label="data" width="100" />
        <el-table-column label="操作" fixed="right" width="150">
          <template #default="scope">
            <el-button type="primary" size="small" @click="hancleGoToDo()">办理</el-button>
            <el-button type="primary" size="small" @click="hancleGoToDo()">清理</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
  <!-- 查看资产弹窗 -->
  <el-drawer v-model="itemVisible" append-to-body size="70%">
    <CardPanel :key="itemGuid" :guid="itemGuid" :rkguid="itemRkGuid" />
  </el-drawer>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store";
import { getToken } from "@/utils/auth";

import pubAPI, { pubAdjustQuery } from "@/api/properties/pub";
import { useRouter } from "vue-router";

// 在 setup 中
const router = useRouter();

const userStore = useUserStore();

const dialogVisible = ref(false);
const loading = ref(false);

//组件参数
const props = defineProps({
  //模块高度，默认200
  height: {
    type: String,
    default: "300",
  },
});
//弹窗相关参数
const itemVisible = ref(false);
const itemGuid = ref("");
const itemRkGuid = ref("");
const handleRowView = (guid: string, rkguid: string) => {
  if (!guid) {
    ElMessage.warning("资产编号不能为空");
    return;
  }
  itemGuid.value = guid;
  itemVisible.value = true;
  itemRkGuid.value = rkguid;
};

/*const pagedata = reactive({
  tableData: [
    { name: "1", data: "2" },
    { name: "3", data: "4" },
    { name: "5", data: "6" },
    { name: "5", data: "6" },
    { name: "5", data: "6" },
    { name: "5", data: "6" },
    { name: "5", data: "6" },
    { name: "5", data: "6" },
  ],
});*/
const pageData = ref<any[]>([]);
const queryParams = reactive<pubAdjustQuery>({
  pageNum: 1,
  pageSize: 12,
  code: "",
  type: "",
  sf: "1",
});
//待办跳转
const hancleGoToDo = () => {};

onMounted(() => {
  console.log(userStore.userInfo.username);
  console.log(getToken());

  pubAPI
    .getAdjustPage(queryParams)
    .then((res) => {
      pageData.value = res.list;
      console.log("pageData", pageData);
      //total.value = res.total;
    })
    .catch((error) => {
      console.error("数据获取失败:", error);
      ElMessage.error("数据获取失败");
      pageData.value = [];
      //total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
});
</script>
<style lang="scss" scoped></style>
../../api/workflow
