import request from "@/utils/request";

const Accept_BASE_URL = "/api/v1/store";

const warningAPI = {
  /** 获取资产预警分页数据 */
  getPage(queryParams?: warningPageQuery) {
    return request<any, PageResult<warningPageVO[]>>({
      url: `${Accept_BASE_URL}/page-warning`,
      method: "get",
      params: queryParams,
    });
  },
}

export default warningAPI;

/** 资产预警单分页查询参数 */
export interface warningPageQuery extends PageQuery {
  /** 资产名称或编号 */
  zcmc?: string;
  syr?: string;
  zcbh?: string;
  zcbm?: string;
  sybm?: string;
}


/** 变动单分页对象 */
export interface warningPageVO {
  /** 资产编号 */
  zcbh?: string;
  zcmc?: string;
  ogly?: string;
  syrname?: string;
  syrbmname?: string;
  zcbmname?: string;  
  ngly?: string;
}


