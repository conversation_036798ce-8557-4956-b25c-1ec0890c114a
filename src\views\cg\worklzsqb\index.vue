<template>
  <div class="app-container">
   <el-card>
    <!-- ********************** 用插槽显示筛选框 ********************** -->
    <template #header>
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
                <el-form-item label="项目编号" prop="xmbh">
                      <el-input
                          v-model="queryParams.xmbh"
                          placeholder="项目编号"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="项目名称" prop="xmmc">
                      <el-input
                          v-model="queryParams.xmmc"
                          placeholder="项目名称"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="项目负责人" prop="xmfzr">
                      <el-input
                          v-model="queryParams.xmfzr"
                          placeholder="项目负责人"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="部门名称" prop="djbmname">
                      <el-input
                          v-model="queryParams.djbmname"
                          placeholder="部门名称"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon><Search /></template>
            搜索
          </el-button>
          <el-button @click="handleResetQuery">
            <template #icon><Refresh /></template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="列表">
        <div>
          <el-button
              v-hasPerm="['cg:workLzSqb:add']"
              type="success"
              plain
              size="small"
              @click="formType.view = false; handleEdit()"
          >
            <template #icon><Plus /></template>
            新增
          </el-button>
          <el-button
              v-hasPerm="['cg:workLzSqb:delete']"
              type="danger"
              plain
              size="small"
              :disabled="removeIds.length === 0"
              @click="handleDelete()"
          >
            <template #icon><Delete /></template>
            删除
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
          ref="dataTableRef"
          v-loading="loading"
          :data="pageData"
          highlight-current-row
          border
          @selection-change="handleSelectionChange"
          @expand-change="handleExpandChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed />
        <el-table-column label="展开" type="expand" width="55">
          <template #default="props">
            <div style="padding: 0px 70px; background-color: #ffffff">
              <!-- <TransferPropertiesList
                :dcbm="props.row.dcbm"
                :guid="props.row.guid || ''"
              ></TransferPropertiesList> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
                <el-table-column
                    key="id"
                    label="id"
                    prop="id"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="guid"
                    label="guid"
                    prop="guid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="psguid"
                    label="评审guid"
                    prop="psguid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="psbh"
                    label="原为评审编号,现改为调整理由"
                    prop="psbh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="pstm"
                    label="默认=项目名称，可修改"
                    prop="pstm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="pslx"
                    label="集合类型=论证类别"
                    prop="pslx"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="psbm"
                    label="评审组别"
                    prop="psbm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cgml"
                    label="采购目录"
                    prop="cgml"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zgbm"
                    label="主管部门"
                    prop="zgbm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="nextspr"
                    label="下一审批人"
                    prop="nextspr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="szbm"
                    label="使用部门"
                    prop="szbm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xmguid"
                    label="公管处部门编码"
                    prop="xmguid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xmbh"
                    label="项目编号"
                    prop="xmbh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xmmc"
                    label="项目名称"
                    prop="xmmc"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xmfzr"
                    label="项目负责人"
                    prop="xmfzr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xmfzrname"
                    label="项目负责人"
                    prop="xmfzrname"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysje"
                    label="$fieldConfig.fieldComment"
                    prop="ysje"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="jhpsrq"
                    label="计划评审日期"
                    prop="jhpsrq"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="djsj"
                    label="登记时间"
                    prop="djsj"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="djr"
                    label="登记人"
                    prop="djr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="djrname"
                    label="登记人"
                    prop="djrname"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="djbm"
                    label="组织机构，集合类型"
                    prop="djbm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="djbmname"
                    label="部门名称"
                    prop="djbmname"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="netcode"
                    label="节点PK：流程编码+2位，支持多节点"
                    prop="netcode"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sjzt"
                    label="配置当前节点初始数据状态"
                    prop="sjzt"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="psfqr"
                    label="评审发起人"
                    prop="psfqr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="notes"
                    label="备注"
                    prop="notes"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by1"
                    label="明细总金额"
                    prop="by1"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by2"
                    label="涉辐、高能耗"
                    prop="by2"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by3"
                    label="分管校领导审批人"
                    prop="by3"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by4"
                    label="是否已经经过专家评审  0:否 1：是 "
                    prop="by4"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by5"
                    label="是否直接论证结束   0:否  1:是"
                    prop="by5"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xmnr"
                    label="项目内容"
                    prop="xmnr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xmdyqk"
                    label="项目调研情况"
                    prop="xmdyqk"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="nexter"
                    label="$fieldConfig.fieldComment"
                    prop="nexter"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="nextbm"
                    label="$fieldConfig.fieldComment"
                    prop="nextbm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sfxs"
                    label="0：线下  1：线上"
                    prop="sfxs"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="jhid"
                    label="采购计划id"
                    prop="jhid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zgbmspr"
                    label="业务归口部门审核人"
                    prop="zgbmspr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xldspr"
                    label="分管校领导"
                    prop="xldspr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="dylzid"
                    label="开发平台大仪论证ID"
                    prop="dylzid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="psnr"
                    label="论证初稿"
                    prop="psnr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="pszg"
                    label="论证终稿"
                    prop="pszg"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="pszz"
                    label="专家组长"
                    prop="pszz"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ewmguid"
                    label="二维码Guid用于防止扫码重复登录"
                    prop="ewmguid"
                    min-width="150"
                    align="center"
                />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button
                v-hasPerm="['cg:workLzSqb:edit']"
                type="primary"
                size="small"
                icon="Edit"
                link
                @click="formType.view = false;handleRowEdit(scope.row.id)"
            >
              编辑
            </el-button>
            <el-button
                v-hasPerm="['cg:workLzSqb:delete']"
                type="danger"
                size="small"
                icon="Delete"
                link
                @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
            <el-button
              v-if="check"
              type="primary"
              icon="DocumentChecked"
              size="small"
              link
              @click="hancleRowCheck"
            >
              审核
            </el-button>
            <el-button
              v-if="view"
              type="success"
              icon="Document"
              size="small"
              link
              @click="hancleRowView"
            >
              查看
            </el-button>
            <el-button
              v-if="view"
              type="primary"
              icon="Printer"
              size="small"
              link
              @click="formType.view = true;hancleRowPrint"
            >
              打印
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="handleQuery()"
      />
    </el-card>

    <!-- 采购论证表单弹窗 -->   
    <el-drawer 
      v-model="dialog.visible" 
      :title="dialog.title" 
      append-to-body 
      size="75%"
      :before-close="handleCloseDialog"

    >
<el-card>
        <template #header>
          <Title name="XX信息">
            <div>
              <el-button
                v-if="!formType.view"
                type="primary"
                @click="handleSubmit()"
              >
                保存
              </el-button>
              <el-button v-if="!formType.view" type="danger" @click="handleSubmit()">
                提交
              </el-button>
            </div>
          </Title>
        </template>



      <el-form ref="dataFormRef" :model="formData" :rules="rules" label-width="100px"  :inline="true"
          :disabled="formType.view">
                <el-form-item label="id" prop="id">
                      <el-input
                          v-model="formData.id"
                          placeholder="id"
                      />
                </el-form-item>
                <el-form-item label="guid" prop="guid">
                      <el-input
                          v-model="formData.guid"
                          placeholder="guid"
                      />
                </el-form-item>
                <el-form-item label="评审guid" prop="psguid">
                      <el-input
                          v-model="formData.psguid"
                          placeholder="评审guid"
                      />
                </el-form-item>
                <el-form-item label="原为评审编号,现改为调整理由" prop="psbh">
                      <el-input
                          v-model="formData.psbh"
                          placeholder="原为评审编号,现改为调整理由"
                      />
                </el-form-item>
                <el-form-item label="默认=项目名称，可修改" prop="pstm">
                      <el-input
                          v-model="formData.pstm"
                          placeholder="默认=项目名称，可修改"
                      />
                </el-form-item>
                <el-form-item label="集合类型=论证类别" prop="pslx">
                      <el-input
                          v-model="formData.pslx"
                          placeholder="集合类型=论证类别"
                      />
                </el-form-item>
                <el-form-item label="评审组别" prop="psbm">
                      <el-input
                          v-model="formData.psbm"
                          placeholder="评审组别"
                      />
                </el-form-item>
                <el-form-item label="采购目录" prop="cgml">
                      <el-input
                          v-model="formData.cgml"
                          placeholder="采购目录"
                      />
                </el-form-item>
                <el-form-item label="主管部门" prop="zgbm">
                      <el-input
                          v-model="formData.zgbm"
                          placeholder="主管部门"
                      />
                </el-form-item>
                <el-form-item label="下一审批人" prop="nextspr">
                      <el-input
                          v-model="formData.nextspr"
                          placeholder="下一审批人"
                      />
                </el-form-item>
                <el-form-item label="使用部门" prop="szbm">
                      <el-input
                          v-model="formData.szbm"
                          placeholder="使用部门"
                      />
                </el-form-item>
                <el-form-item label="公管处部门编码" prop="xmguid">
                      <el-input
                          v-model="formData.xmguid"
                          placeholder="公管处部门编码"
                      />
                </el-form-item>
                <el-form-item label="项目编号" prop="xmbh">
                      <el-input
                          v-model="formData.xmbh"
                          placeholder="项目编号"
                      />
                </el-form-item>
                <el-form-item label="项目名称" prop="xmmc">
                      <el-input
                          v-model="formData.xmmc"
                          placeholder="项目名称"
                      />
                </el-form-item>
                <el-form-item label="项目负责人" prop="xmfzr">
                      <el-input
                          v-model="formData.xmfzr"
                          placeholder="项目负责人"
                      />
                </el-form-item>
                <el-form-item label="项目负责人" prop="xmfzrname">
                      <el-input
                          v-model="formData.xmfzrname"
                          placeholder="项目负责人"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="ysje">
                      <el-input
                          v-model="formData.ysje"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="计划评审日期" prop="jhpsrq">
                      <el-input
                          v-model="formData.jhpsrq"
                          placeholder="计划评审日期"
                      />
                </el-form-item>
                <el-form-item label="登记时间" prop="djsj">
                      <el-input
                          v-model="formData.djsj"
                          placeholder="登记时间"
                      />
                </el-form-item>
                <el-form-item label="登记人" prop="djr">
                      <el-input
                          v-model="formData.djr"
                          placeholder="登记人"
                      />
                </el-form-item>
                <el-form-item label="登记人" prop="djrname">
                      <el-input
                          v-model="formData.djrname"
                          placeholder="登记人"
                      />
                </el-form-item>
                <el-form-item label="组织机构，集合类型" prop="djbm">
                      <el-input
                          v-model="formData.djbm"
                          placeholder="组织机构，集合类型"
                      />
                </el-form-item>
                <el-form-item label="部门名称" prop="djbmname">
                      <el-input
                          v-model="formData.djbmname"
                          placeholder="部门名称"
                      />
                </el-form-item>
                <el-form-item label="节点PK：流程编码+2位，支持多节点" prop="netcode">
                      <el-input
                          v-model="formData.netcode"
                          placeholder="节点PK：流程编码+2位，支持多节点"
                      />
                </el-form-item>
                <el-form-item label="配置当前节点初始数据状态" prop="sjzt">
                      <el-input
                          v-model="formData.sjzt"
                          placeholder="配置当前节点初始数据状态"
                      />
                </el-form-item>
                <el-form-item label="评审发起人" prop="psfqr">
                      <el-input
                          v-model="formData.psfqr"
                          placeholder="评审发起人"
                      />
                </el-form-item>
                <el-form-item label="备注" prop="notes">
                      <el-input
                          v-model="formData.notes"
                          placeholder="备注"
                      />
                </el-form-item>
                <el-form-item label="明细总金额" prop="by1">
                      <el-input
                          v-model="formData.by1"
                          placeholder="明细总金额"
                      />
                </el-form-item>
                <el-form-item label="涉辐、高能耗" prop="by2">
                      <el-input
                          v-model="formData.by2"
                          placeholder="涉辐、高能耗"
                      />
                </el-form-item>
                <el-form-item label="分管校领导审批人" prop="by3">
                      <el-input
                          v-model="formData.by3"
                          placeholder="分管校领导审批人"
                      />
                </el-form-item>
                <el-form-item label="是否已经经过专家评审  0:否 1：是 " prop="by4">
                      <el-input
                          v-model="formData.by4"
                          placeholder="是否已经经过专家评审  0:否 1：是 "
                      />
                </el-form-item>
                <el-form-item label="是否直接论证结束   0:否  1:是" prop="by5">
                      <el-input
                          v-model="formData.by5"
                          placeholder="是否直接论证结束   0:否  1:是"
                      />
                </el-form-item>
                <el-form-item label="项目内容" prop="xmnr">
                      <el-input
                          v-model="formData.xmnr"
                          placeholder="项目内容"
                      />
                </el-form-item>
                <el-form-item label="项目调研情况" prop="xmdyqk">
                      <el-input
                          v-model="formData.xmdyqk"
                          placeholder="项目调研情况"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="nexter">
                      <el-input
                          v-model="formData.nexter"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="nextbm">
                      <el-input
                          v-model="formData.nextbm"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="0：线下  1：线上" prop="sfxs">
                      <el-input
                          v-model="formData.sfxs"
                          placeholder="0：线下  1：线上"
                      />
                </el-form-item>
                <el-form-item label="采购计划id" prop="jhid">
                      <el-input
                          v-model="formData.jhid"
                          placeholder="采购计划id"
                      />
                </el-form-item>
                <el-form-item label="业务归口部门审核人" prop="zgbmspr">
                      <el-input
                          v-model="formData.zgbmspr"
                          placeholder="业务归口部门审核人"
                      />
                </el-form-item>
                <el-form-item label="分管校领导" prop="xldspr">
                      <el-input
                          v-model="formData.xldspr"
                          placeholder="分管校领导"
                      />
                </el-form-item>
                <el-form-item label="开发平台大仪论证ID" prop="dylzid">
                      <el-input
                          v-model="formData.dylzid"
                          placeholder="开发平台大仪论证ID"
                      />
                </el-form-item>
                <el-form-item label="论证初稿" prop="psnr">
                      <el-input
                          v-model="formData.psnr"
                          placeholder="论证初稿"
                      />
                </el-form-item>
                <el-form-item label="论证终稿" prop="pszg">
                      <el-input
                          v-model="formData.pszg"
                          placeholder="论证终稿"
                      />
                </el-form-item>
                <el-form-item label="专家组长" prop="pszz">
                      <el-input
                          v-model="formData.pszz"
                          placeholder="专家组长"
                      />
                </el-form-item>
                <el-form-item label="二维码Guid用于防止扫码重复登录" prop="ewmguid">
                      <el-input
                          v-model="formData.ewmguid"
                          placeholder="二维码Guid用于防止扫码重复登录"
                      />
                </el-form-item>
      </el-form>
      <template #footer></template>
     </el-card>
    </ el-drawer>
  </div>
</template>

<script setup lang="ts">
  defineOptions({
    name: "WorkLzSqb",
    inheritAttrs: false,
  });
  import { useUserStore } from "@/store";
  import WorkLzSqbAPI, { WorkLzSqbPageVO, WorkLzSqbForm, WorkLzSqbPageQuery } from "@/api/cg/work-lz-sqb";

  // 获取路由参数,view/add/check
  const route = useRoute();
  const type = ref(route.query.type?.toString() || "view");
  //显示隐藏权限,用于v-if
  const add = type.value == "add";
  const view = type.value == "view";
  const check = type.value == "check";
  //页面参数
  const queryFormRef = ref(ElForm);
  const loading = ref(false);
  const removeIds = ref<number[]>([]);
  const total = ref(0);

  const queryParams = reactive<WorkLzSqbPageQuery>({
    pageNum: 1,
    pageSize: 10,
  });

  // 采购论证表格数据
  const pageData = ref<WorkLzSqbPageVO[]>([]);

  // 弹窗
  const dialog = reactive({
    title: "",
    visible: false,
  });



  /** 查询采购论证 */
  function handleQuery() {
    loading.value = true;
          WorkLzSqbAPI.getPage(queryParams)
        .then((data) => {
          pageData.value = data.list;
          total.value = data.total;
        })
        .finally(() => {
          loading.value = false;
        });
  }

  /** 重置采购论证查询 */
  function handleResetQuery() {
    queryFormRef.value!.resetFields();
    queryParams.pageNum = 1;
    handleQuery();
  }

  /** 行复选框选中记录选中ID集合 */
  function handleSelectionChange(selection: any) {
    removeIds.value = selection.map((item: any) => item.id);
  }

  /** 打开采购论证弹窗 */
  function handleEdit(id?: number) {
    dialog.visible = true;
    if (id) {
      dialog.title = "修改采购论证";
            WorkLzSqbAPI.getFormData(id).then((data) => {
        Object.assign(formData, data);
      });
    } else {
      dialog.title = "新增采购论证";
    }
  }

 

  /** 关闭采购论证弹窗 */
  function handleCloseDialog() {
    dialog.visible = false;
    dataFormRef.value.resetFields();
    dataFormRef.value.clearValidate();
    Object.assign(formData, {});

  }

  /** 删除采购论证 */
  function handleDelete(id?: number) {
    const ids = [id || removeIds.value].join(",");
    if (!ids) {
      ElMessage.warning("请勾选删除项");
      return;
    }

    ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(
        () => {
          loading.value = true;
                WorkLzSqbAPI.deleteByIds(ids)
              .then(() => {
                ElMessage.success("删除成功");
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        },
        () => {
          ElMessage.info("已取消删除");
        }
    );
  }

  onMounted(() => {
    handleQuery();
  });

  //--------------以下是form表单相关
const dataFormRef = ref(ElForm);

  // 采购论证表单数据
  const formData = reactive<WorkLzSqbForm>({});

const formType = reactive({
  add: false,
  edit: false,
  view: false,
});

  // 采购论证表单校验规则
  const rules = reactive({
  });

   /** 提交采购论证表单 */
  function handleSubmit() {
    dataFormRef.value.validate((valid: any) => {
      if (valid) {
        loading.value = true;
        const id = formData.id;
        if (id) {
                WorkLzSqbAPI.update(id, formData)
              .then(() => {
                ElMessage.success("修改成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        } else {
                WorkLzSqbAPI.add(formData)
              .then(() => {
                ElMessage.success("新增成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        }
      }
    });
  }
</script>
<style lang="scss" scoped>

</style>
