<!-- cursor测试:1 -->
<template>
  <div>
    <Title name="基本信息">
      <div v-if="editable">
        <el-button type="primary" @click="handleSaveSq()">保存</el-button>
        <el-button type="danger" @click="handleSubmitSq()">提交</el-button>
      </div>
    </Title>

    <el-form
      ref="dataFormRef"
      :model="formData"
      :rules="sqrules"
      label-width="160px"
      :inline="true"
      :disabled="!editable"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="是否申报确认书" prop="xnqrs">
            <el-radio-group v-model="formData.xnqrs">
              <el-radio value="0">否</el-radio>
              <el-radio value="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="任务名称" prop="rwmc">
            <el-input v-model="formData.rwmc" placeholder="任务名称" />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="formData.sjzt && formData.sjzt.endsWith('3')">
          <el-form-item label="确认书号" prop="qrsbh">
            <DDLDeptList v-model="formData.qrsbh" />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="采购形式" prop="cgxs">
            <DDLXcode v-model="formData.cgxs" xcode="021502" xlen="8" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="采购方式" prop="cgfs">
            <DDLXcode v-model="formData.cgfs" :xcode="formData.cgxs" xlen="10" />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="委托单位" prop="wtdwname">
            <div class="flex">
              <el-input v-model="formData.wtdwname" :disabled="true" placeholder="请选择委托单位" />
              <el-button type="primary" @click="handleAddWtdwPorperties" v-if="editable">
                选择
              </el-button>
              <el-input v-model="formData.wtdw" placeholder="委托单位" style="display: none" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="采购文件编制完成时间" prop="yjcgsj">
            <el-date-picker
              v-model="formData.yjcgsj"
              format="YYYY-MM-DD"
              type="date"
              placeholder="请选择日期"
            />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="外贸代理机构" prop="wtdwname">
            <div class="flex">
              <el-input
                v-model="formData.wmwtdwname"
                :disabled="true"
                placeholder="请选择外贸代理机构"
              />
              <el-button type="primary" @click="handleAddWtdwPorperties" v-if="editable">
                选择
              </el-button>
              <el-input
                v-model="formData.wmwtdw"
                placeholder="外贸代理机构"
                style="display: none"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="预算经费（元）" prop="ysje">
            <el-input v-model="formData.ysje" placeholder="预算经费" :disabled="true" />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="任务描述" prop="rwms" style="width: 1000px">
            <el-input
              v-model="formData.rwms"
              placeholder="任务描述"
              type="textarea"
              maxlength="200"
              show-word-limit
              :rows="3"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="操作意见" prop="czyj" style="width: 1000px">
            <el-input
              v-model="formData.czyj"
              placeholder="操作意见"
              type="textarea"
              maxlength="200"
              show-word-limit
              :rows="3"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <!--预算经费开始-->
          <Title name="申报建议书">
            <div>
              <el-button
                v-if="editable"
                type="primary"
                plain
                icon="plus"
                @click="handleOpenDialogYs(true)"
              >
                新增
              </el-button>
            </div>
          </Title>
          <el-table
            v-loading="loading"
            :data="jyspageData"
            highlight-current-row
            :border="true"
            height="200px"
          >
            <el-table-column type="index" label="序号" width="55" />
            <el-table-column prop="jfbh" label="建议书名称" width="130" />
            <el-table-column prop="jfmc" label="组织形式" width="130" />
            <el-table-column prop="jhje" label="采购方式" width="120" />
            <el-table-column prop="fzrname" label="金额(元)" width="155" />
            <el-table-column prop="jflbname" label="数量" width="150" />
            <el-table-column prop="ysje" label="是否上报省财" width="120" />
            <el-table-column prop="ysje" label="采购目录" width="120" />
            <el-table-column prop="ysje" label="支付方式" width="120" />
            <el-table-column prop="ysje" label="备案方式" width="120" />
            <el-table-column prop="ysje" label="适合法律条款" width="120" />
            <el-table-column prop="ysje" label="是否面向中小企业" width="120" />
            <el-table-column prop="ysje" label="中小企业落实方式" width="120" />
            <el-table-column prop="ysje" label="中小企业预留比例" width="120" />
            <el-table-column prop="ysje" label="是否临时确认书" width="120" />
            <el-table-column prop="ysje" label="是否进口" width="120" />

            <el-table-column label="操作" fixed="right" width="150" v-if="editable">
              <template #default="scope">
                <el-button
                  type="primary"
                  size="small"
                  icon="Edit"
                  link
                  @click="
                    formType.view = false;
                    handleOpenDialogYs(true, scope.row);
                  "
                >
                  关联明细
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  icon="Delete"
                  link
                  @click="handleYsDelete(scope.row.jfid)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- ********************** 翻页 ********************** -->
          <pagination
            v-if="jystotal > 10"
            v-model:total="jystotal"
            v-model:page="jysqueryParams.pageNum"
            v-model:limit="jysqueryParams.pageSize"
            @pagination="jyshandlePageQuery"
          />

          <!--预算数据-->

          <!--预算经费结束-->
        </el-col>
      </el-row>
    </el-form>
    <div>
      <!--申请明细开始-->
      <Title name="采购申请明细">
        <div>
          <el-button
            v-if="editable"
            type="primary"
            plain
            icon="plus"
            @click="handleOpenDialogMx(true)"
          >
            修改采购目录
          </el-button>
        </div>
      </Title>
      <el-table
        v-loading="loading"
        :data="mxpageData"
        highlight-current-row
        :border="true"
        height="260px"
      >
        <el-table-column type="index" label="序号" width="55" />
        <el-table-column prop="mxbh" label="明细编号" width="155" />
        <el-table-column
          prop="wpmc"
          :label="
            formData.cgml === 'A' ? '物品名称' : formData.cgml === 'B' ? '工程名称' : '服务名称'
          "
          :width="formData.cgml === 'A' ? '300' : '720'"
        />
        <el-table-column v-if="formData.cgml === 'A'" prop="gg" label="规格型号" width="200" />
        <el-table-column v-if="formData.cgml === 'A'" prop="sl" label="数量" width="60" />
        <el-table-column v-if="formData.cgml === 'A'" prop="dwname" label="计量单位" width="85" />
        <el-table-column v-if="formData.cgml === 'A'" prop="price" label="单价(元)" width="85" />
        <el-table-column prop="ysje" label="预算金额(元)" width="120" />
        <el-table-column label="操作" fixed="right" width="130">
          <template #default="scope">
            <el-button
              v-hasPerm="'sys:user:password:reset'"
              type="primary"
              icon="View"
              size="small"
              link
              @click="handleOpenDialogMx(false, scope.row)"
              v-if="editable == false"
            >
              查看
            </el-button>

            <el-button
              type="primary"
              size="small"
              icon="Edit"
              link
              @click="
                formType.view = false;
                handleOpenDialogMx(true, scope.row);
              "
              v-if="editable"
            >
              修改采购目录
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="mxtotal > 10"
        v-model:total="mxtotal"
        v-model:page="mxqueryParams.pageNum"
        v-model:limit="mxqueryParams.pageSize"
        @pagination="mxhandlePageQuery"
      />

      <!--申请明细结束-->
    </div>
    <!-- 附件上传 -->
    <div v-if="editable">
      <Title name="附件上传" />
      <FileUpload :guid="props.guid" :code="formData.fjcode" :key="formData.guid" />
    </div>
    <!-- 附件查看 -->
    <div v-else>
      <Title name="附件查看" />
      <FileView :guid="props.guid" />
    </div>
  </div>

  <!-- 选择代理机构弹窗 -->
  <el-drawer v-model="choosedialog.visible" :title="choosedialog.title" append-to-body size="65%">
    <CgyxChoose
      :key="keyId"
      :ysxmbh="formData.ysxmbhs"
      :handleReturnConfirm="handleReturnConfirm"
    />
  </el-drawer>

  <!--明细添加弹窗-->
  <el-dialog
    v-model="choosedialog_mx.visible"
    width="40%"
    :title="choosedialog_mx.title"
    :before-close="handleCloseMxDialog"
  >
    <MxEdit
      :sqid="formData.sqid"
      :RefreshFatherDrawer="MxRefreshFatherDrawer"
      ref="mxRef"
      :id="itemMxId"
      :editable="itemMxEditable"
      :key="itemMxGuid + '1'"
      :parentguid="formData.guid"
      :guid="itemMxGuid"
      :cglb="formData.cgml"
    />
  </el-dialog>
  <!--明细添加弹窗11-->
</template>

<script setup lang="ts">
defineOptions({
  name: "RwEdit",
});
import { ElLoading } from "element-plus";
import { useUserStore } from "@/store";
import ZxcgRwAPI, { ZxcgCgrwPageVO, ZxcgCgrwForm, ZxcgCgrwPageQuery } from "@/api/cg/zxcg-cgrw";
import ZxcgJysAPI, {
  ZxcgCgjysPageVO,
  ZxcgCgjysForm,
  ZxcgCgjysPageQuery,
} from "@/api/cg/zxcg-cgjys";
import { getGuid } from "@/utils/guid";
import ZxcgSqjfAPI, { ZxcgSqjfForm, ZxcgSqjfPageQuery, ZxcgSqjfPageVO } from "@/api/cg/zxcg-sqjf";
import ZxcgMxAPI, { ZxcgMxForm, ZxcgMxPageVO, ZxcgMxPageQuery } from "@/api/cg/zxcg-mx";
import { formatLocalDateTime } from "@/utils/day";

import SqjfEdit from "@/views/cg/sqjf/components/sqjf-Edit.vue";
import MxEdit from "@/views/cg/mx/components/Mx-Edit.vue";
import DDLYcodein from "@/views/cg/ycodein/components/index.vue";
import { number } from "echarts";
//————————————————————————————————————————————暴露的方法,和请求参数
//组件参数
const props = defineProps({
  mxids: {
    type: Array as PropType<number[]>,
    default: () => [],
  },
  rwid: {
    type: Number,
  },
  guid: {
    type: String,
  },
  //保存成功后需要关闭抽屉并刷新数据
  RefreshFatherDrawer: {
    type: Function,
    required: true,
  },
  //是否可编辑
  editable: {
    type: Boolean,
  },
});

//——————————————————————————————————————————————————form查询相关
const loading = ref(false);
const userStore = useUserStore();
/** 打开采购申请弹窗 */

function handleSqEdit(id?: number) {}

/** 关闭采购申请弹窗 */
function handleClearForm() {
  //dialog.visible = false;
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  formData.sqid = undefined;
  //Object.assign(formData, {});
}

//—————————————————————————————————————————————意向选择弹窗
const keyId = ref(0);
// 弹窗显隐
const choosedialog = reactive({
  title: "采购意向",
  visible: false,
});
const handleAddCgyxPorperties = async () => {
  choosedialog.visible = true;
};
//暴露的方法
const handleReturnConfirm = (yxguid: string, cgxmmc: string) => {
  if (yxguid == null || yxguid == "") {
    ElMessage.warning("请选择要采购意向");
    return;
  }

  //formData.yxmc = cgxmmc;
  //formData.sqyxguid = yxguid;
  choosedialog.visible = false;
};

//-----------------------------------------预算选择弹窗
const jyspageData = ref<ZxcgCgjysPageVO[]>([]); //经费列表
const jystotal = ref(0);
//请求参数
const jysqueryParams = reactive<ZxcgCgjysPageQuery>({
  pageNum: 1,
  pageSize: 10,
  rwid: 0,
});
//列表查询
const jyshandlePageQuery = () => {
  if (formData.sqid) jysqueryParams.rwid = formData.rwid;
  else formData.sqid = 0;
  if (jysqueryParams.rwid == 0) {
    jyspageData.value = [];
    jystotal.value = 0;
    return;
  }
  loading.value = true;
  /*formData.xmmc = "";
  formData.xmbm = "";
  formData.jflb = "";
  formData.ysnd = "";
  ZxcgSqjfAPI.getPage(jfqueryParams)
    .then((data) => {
      console.log("数据：", data);
      jfpageData.value = data.list;
      jftotal.value = data.total;
      if (data.total > 0) {
        formData.xmmc = data.list[0].jfmc;
        formData.xmbm = data.list[0].jfbh;
        formData.jflb = data.list[0].jflb;
        formData.ysnd = data.list[0].jfnd;
        formData.xmfzr = "";
        formData.fzrbm = "";
      }
     
    })
    .catch((error) => {
      console.error("获取经费卡和申请预算列表失败:", error);
      ElMessage.error("获取经费卡和申请预算列表失败");
      jfpageData.value = [];
      jftotal.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });*/
};

//--------------------------------申请明细---
const itemMxGuid = ref<string | undefined>();
const itemMxId = ref<number | undefined>();
const itemMxEditable = ref<boolean>(false);
const mxpageData = ref<ZxcgMxPageVO[]>([]); //明细列表
const mxtotal = ref(0);
//请求参数
const mxqueryParams = reactive<ZxcgMxPageQuery>({
  pageNum: 1,
  pageSize: 10,
  sqid: 0,
});
//列表查询
const mxhandlePageQuery = () => {
  if (formData.rwid) mxqueryParams.rwid0 = formData.rwid;
  else mxqueryParams.mxids = props.mxids.join(",");
  loading.value = true;
  ZxcgMxAPI.getWaitRwcjPage(mxqueryParams)
    .then((data) => {
      console.log("明细数据：", data);
      mxpageData.value = data.list;
      mxtotal.value = data.total;
    })
    .catch((error) => {
      console.error("获取采购任务明细列表失败:", error);
      ElMessage.error("获取采购任务明细列表失败");
      mxpageData.value = [];
      mxtotal.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
};

//const dataFormRef_Mx = ref(ElForm);
const choosedialog_mx = reactive({
  title: "采购申请明细",
  visible: false,
});
//const mxForm = reactive<ZxcgMxForm>({});
const handleOpenDialogMx = async (editable: boolean, row?: ZxcgMxPageVO) => {
  //await dataFormRef.value?.validate();
  if (editable) {
    var validate = true;
    if (formData.rwmc) {
    } else {
      validate = false;
      ElMessage.error("任务名称不能为空");
      return;
    }

    if (validate) {
      const api = handleSaveSq();
      await api.then(() => {
        itemMxGuid.value = row?.guid || getGuid();
        itemMxId.value = row?.mxid;
        itemMxEditable.value = editable;
        choosedialog_mx.title = editable
          ? row?.mxid
            ? "修改采购申请明细"
            : "新增采购申请明细"
          : "查看采购申请明细";
        choosedialog_mx.visible = true;
      });
    }
  } else {
    itemMxGuid.value = row?.guid || getGuid();
    itemMxId.value = row?.mxid;
    itemMxEditable.value = editable;
    choosedialog_mx.title = "查看采购申请明细";
    choosedialog_mx.visible = true;
  }
};

//新增明细

const mxRef = ref<any | null>(null);
function MxRefreshFatherDrawer(close?: boolean) {
  //是否关闭弹窗由参数决定，默认不关闭
  if (close) {
    choosedialog_mx.visible = false;
  }
  mxhandlePageQuery();
}
// 关闭弹窗
function handleCloseMxDialog() {
  choosedialog_mx.visible = false;
  itemMxId.value = undefined;
  if (mxRef.value) {
    //await
    mxRef?.value.handleClearForm();
  }

  //formData.id = undefined;
  // formData.status = 1;
}

//-----------------------------申请明细--
//任务创建初始化
function handleRwAdd(mxids: number[]) {
  mxhandlePageQuery();
}

/** 初始化任务信息 */
function handleFormQuery(id?: number) {
  handleSqEdit(id);
}

//——————————————————————————————————————————————————form操作相关

const handleSaveRw = async () => {
  /*if (formData.sqmc) {
    if (formData.zgbmlist) formData.zgbm = formData.zgbmlist.join(",");
    else formData.zgbm = "";
    console.log("formData.zgbm", formData.zgbm);
    //处理显隐数据逻辑
    if (formData.isjk == "1") {
    } else {
      //去掉进口论证的论证
      formData.by7 = "";
    }
    if (formData.isdyly == "1") {
    } else {
      //去掉单一来源论证
      formData.dylzid = "";
    }
    //formData.by2
    if (formData.by2 == "0") {
    } else {
      //去掉不面向中小企业的理由
      formData.by9 = "";
    }
    if (formData.cgml == "A") {
    } else {
      //去掉新创分类
      formData.sfxcfl = "";
    }
    if (formData.sfxcfl == "1") {
    } else {
      //去掉是否新创
      formData.sfxc = "";
    }

    if (formData.by14 == "1") {
    } else {
      //去掉可行性分析
      formData.kxxfx = "";
      //去掉三重一大
      formData.sfszyd = "0";
    }

    if (formData.by16) {
    } else {
      formData.by16 = "0";
    }
    if (is_fgw) {
    } else {
      formData.sffgw = "0";
    }

    if (formData.sqcgfl == "xm") {
      formData.td = "0";
    } else if (formData.sqcgfl == "lx") {
      formData.td = "1";
    } else {
      formData.td = "";
    }
    if (formData.cgml == "B") {
    } else {
      formData.zbgcxm = "0";
    }
    if (formData.by14 == "1" && formData.cgml == "A") {
    } else {
      formData.sfzcy = "0";
    }

    if (formData.yjdnzfsj) formData.yjdnzfsj = formatLocalDateTime(new Date(formData.yjdnzfsj));
    // formData.xmmc="";
    console.log("formData.yjdnzfsj", formData.yjdnzfsj);
    const submitData = { ...formData };

    const id = formData.sqid;

    const apiCall = id ? ZxcgSqAPI.update(id, submitData) : ZxcgSqAPI.add(submitData);
    await apiCall
      .then(async (res) => {
        if (!res) {
          ElMessage.error("申请单保存失败");
        } else {
          ElMessage.success("申请单保存成功");
          //保存成功后，重新用id去查询一次数据
          formData.sqid = Number(res);
          // handleClearForm();
          handleFormQuery(formData.sqid);
          console.log("sqid:,", formData.sqid);
          keyId.value++;
          props.RefreshFatherDrawer();
        }
      })
      .catch((error) => {
        ElMessage.error("操作失败，失败原因：" + error.message);
      })
      .finally(() => {});
  } else {
    ElMessage.success("采购项目名称不能为空");
  }*/
};
//--------------以下是form表单相关
const dataFormRef = ref(ElForm);

// 采购申请表单数据
const formData = reactive<ZxcgCgrwForm>({});
/*const zgbmData = reactive({
  list: [] as string[],
});*/

const formType = reactive({
  add: false,
  edit: false,
  view: false,
});

// 采购申请表单校验规则
//const rules = reactive({});
/*
const sqrules = reactive({
  sqmc: [{ required: true, message: "采购项目名称不能为空", trigger: "blur" }],
  bmfzr: [{ required: true, message: "部门审核人不能为空", trigger: "blur" }],
  by13: [{ required: true, message: "分管校领导不能为空", trigger: "blur" }],
  gzpdxq: [{ required: true, message: "派单校区不能为空", trigger: "blur" }],
  cgml: [{ required: true, message: "采购目录不能为空", trigger: "blur" }],
  fwlx: [{ required: true, message: "服务类别不能为空", trigger: "blur" }],
  zgbmlist: [{ required: true, message: "业务归口管理部门不能为空", trigger: "blur" }],
  yxmc: [{ required: true, message: "采购意向不能为空", trigger: "blur" }],
  yjdnzfsj: [{ required: true, message: "预计当年支付时间不能为空", trigger: "blur" }],
  yjdnzfje: [
    { required: true, message: "预计当年支付金额不能为空", trigger: "blur" },
    {
      pattern: /^(?=.*[1-9])\d*(?:\.\d{1,2})?$/,
      message: "必须为大于0的数字",
      trigger: "blur",
    },
  ],
  isjk: [{ required: true, message: "是否进口不能为空", trigger: "blur" }],
  //by7: [{ required: true, message: "选取进口专家论证意见或在采购申请附件中上传", trigger: "blur" }],
  isdyly: [{ required: true, message: "是否单一来源", trigger: "blur" }],
  //dylzid: [{ required: true, message: "选取单一来源专家论证意见或在采购申请附件中上传", trigger: "blur" }],
  by2: [{ required: true, message: "是否面向中小企业不能为空", trigger: "blur" }],
  by9: [{ required: true, message: "不面向中小企业的理由不能为空", trigger: "blur" }],
  by17: [{ required: true, message: "是否学科办经费不能为空", trigger: "blur" }],
  sffgw: [{ required: true, message: "是否发改委项目不能为空", trigger: "blur" }],
  iskysb: [{ required: true, message: "是否科研仪器设备不能为空", trigger: "blur" }],
  sfxcfl: [{ required: true, message: "是否信创不能为空", trigger: "blur" }],
  sfxc: [{ required: true, message: "是否信创不能为空", trigger: "blur" }],
  zbgcxm: [{ required: true, message: "是否必须招标的工程项目不能为空", trigger: "blur" }],
  sfszyd: [{ required: true, message: "是否本单位三重一大事项不能为空", trigger: "blur" }],
  sfzcy: [{ required: true, message: "是否政采云采购不能为空", trigger: "blur" }],
  kxxfx: [{ required: true, message: "采购需求不能为空", trigger: "blur" }],
});
*/
/** 提交采购申请表单 */
function handleSubmitSq() {
  dataFormRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      /*  handleSaveSq().then(async () => {
        //第二层保存完成
        const submitData = { ...formData };
        const dialogloading = ElLoading.service({
          lock: true,
          text: "处理中",
        });

        //工作流提交
        await ZxcgSqAPI.submit(submitData.guid || "")
          .then(() => {
            //第三层提交完成
            ElMessage.success("采购申请提交成功");
            props.RefreshFatherDrawer(true);
          })
          .finally(() => {
            dialogloading.close();
            loading.value = false;
          })
          .catch((error) => {
            ElMessage.error(error.message);
          });
      });*/
    }
  });
}

onMounted(() => {
  // if (props.editable) handleSqAdd(props.sqcgfl, props.sqid);
  // else handleFormQuery(props.sqid);
  if (props.rwid) {
    //已经存在任务数据
  } else {
    if (props.mxids) {
      //待创建任务
      handleRwAdd(props.mxids);
    } else {
      ElMessage.error("参数错误！");
    }
  }
});
watch(
  () => formData.cgxs,
  (newVal) => {
    formData.cgfs = "";
  }
);
// 明确暴露给父组件的方法
defineExpose({
  handleClearForm,
});
</script>
<style lang="scss" scoped>
/* 自定义对话框样式 */
.custom-dialog {
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  background-color: #ffffff;

  .el-dialog__header {
    background-color: #409eff;
    color: #ffffff;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;

    .el-dialog__title {
      color: #ffffff;
    }
  }

  .el-dialog__body {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.9);
  }
}
</style>
