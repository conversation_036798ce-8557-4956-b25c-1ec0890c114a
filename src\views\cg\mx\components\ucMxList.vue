<template>
  <!-- ********************** 页面批量操作按钮 ********************** -->
  <Title name="采购明细">
    <div>
      <el-button
        v-if="!view"
        type="success"
        plain
        size="small"
        @click="
          formType.view = false;
          handleEdit();
        "
      >
        <template #icon><Plus /></template>
        新增
      </el-button>
      <el-button
        v-if="!view"
        type="danger"
        plain
        size="small"
        :disabled="removeIds.length === 0"
        @click="handleDelete()"
      >
        <template #icon><Delete /></template>
        删除
      </el-button>
    </div>
  </Title>
  <!-- ********************** 列表内容 ********************** -->
  <el-table
    ref="dataTableRef"
    v-loading="loading"
    :data="pageData"
    highlight-current-row
    border
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" width="55" align="center" fixed />
    <el-table-column label="序号" type="index" width="55" align="center" fixed>
      <template #default="scope">
        <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
      </template>
    </el-table-column>
    <el-table-column key="mxbh" label="明细编号" prop="mxbh" min-width="150" align="center" />
    <el-table-column key="wpmc" label="物品名称" prop="wpmc" min-width="150" align="center" />
    <el-table-column key="cgml" label="采购目录" prop="cgmlname" min-width="150" align="center" />
    <el-table-column key="gg" label="规格" prop="gg" min-width="150" align="center" />
    <el-table-column key="dw" label="单位" prop="dwname" min-width="150" align="center" />
    <el-table-column
      v-if="cgmllx == 'A'"
      key="sl"
      label="数量"
      prop="sl"
      min-width="150"
      align="center"
    />
    <el-table-column
      v-if="cgmllx == 'A'"
      key="price"
      label="单价（元）"
      prop="price"
      min-width="150"
      align="center"
    />
    <el-table-column key="ysje" label="金额（元）" prop="ysje" min-width="150" align="center" />
    <el-table-column fixed="right" label="操作" width="220">
      <template #default="scope">
        <el-button
          v-if="!view"
          type="primary"
          size="small"
          icon="Edit"
          link
          @click="
            formType.view = false;
            handleEdit(scope.row.mxid);
          "
        >
          编辑
        </el-button>
        <el-button
          v-if="!view"
          type="danger"
          size="small"
          icon="Delete"
          link
          @click="handleDelete(scope.row.mxid)"
        >
          删除
        </el-button>
        <el-button
          v-if="view"
          type="success"
          icon="Document"
          size="small"
          link
          @click="
            formType.view = true;
            handleEdit(scope.row.mxid);
          "
        >
          查看
        </el-button>
      </template>
    </el-table-column>
  </el-table>
  <!-- ********************** 翻页 ********************** -->
  <pagination
    v-if="total > 0"
    v-model:total="total"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    @pagination="handleQuery()"
  />
  <!-- 采购申请明细表单弹窗 -->
  <el-drawer
    v-model="dialog.visible"
    :title="dialog.title"
    append-to-body
    size="75%"
    :before-close="handleCloseDialog"
  >
    <el-card>
      <template #header>
        <Title name="明细信息">
          <div>
            <el-button v-if="!formType.view" type="primary" @click="handleSubmit()">保存</el-button>
            <el-button type="success" @click="handleBack()">返回</el-button>
          </div>
        </Title>
      </template>

      <el-form
        ref="dataFormRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        :inline="true"
        :disabled="formType.view"
      >
        <el-form-item label="明细编号" prop="mxbh">
          <el-input v-model="formData.mxbh" />
        </el-form-item>
        <el-form-item label="采购目录" prop="cgml">
          <el-input v-model="formData.cgml" />
        </el-form-item>
        <el-form-item label="物品名称" prop="wpmc">
          <el-input v-model="formData.wpmc" />
        </el-form-item>
        <el-form-item label="规格" prop="gg">
          <el-input v-model="formData.gg" />
        </el-form-item>
        <el-form-item label="技术指标要求" prop="xxcs">
          <el-input v-model="formData.xxcs" />
        </el-form-item>
        <el-form-item label="数量" prop="sl">
          <el-input v-model="formData.sl" />
        </el-form-item>
        <el-form-item label="计量单位" prop="dw">
          <el-input v-model="formData.dw" />
        </el-form-item>
        <el-form-item label="单价（元）" prop="price">
          <el-input v-model="formData.price" />
        </el-form-item>
        <el-form-item label="金额（元）" prop="ysje">
          <el-input v-model="formData.ysje" />
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input v-model="formData.notes" />
        </el-form-item>
      </el-form>
      <template #footer />
    </el-card>
  </el-drawer>
</template>

<script setup lang="ts">
import ZxcgMxAPI, { ZxcgMxPageVO, ZxcgMxForm, ZxcgMxPageQuery } from "@/api/cg/zxcg-mx";

const props = defineProps({
  sqid: {
    type: Number,
    required: false,
  },
  rwid: {
    type: Number,
    required: false,
  },
  wjid: {
    type: Number,
    required: false,
  },
  bdid: {
    type: Number,
    required: false,
  },
  //是否可编辑
  editable: {
    type: Boolean,
    required: true,
  },
});

//显示隐藏权限,用于v-if
const view = props.editable == false;
const cgmllx = ref("A");
//页面参数
const queryFormRef = ref(ElForm);
const loading = ref(false);
const removeIds = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<ZxcgMxPageQuery>({
  pageNum: 1,
  pageSize: 10,
});

queryParams.sqid = props.sqid;
queryParams.wjid = props.wjid;
queryParams.bdid = props.bdid;
queryParams.rwid = props.rwid;

watch(
  [() => props.sqid, () => props.wjid, () => props.bdid, () => props.rwid],
  () => {
    queryParams.sqid = props.sqid;
    queryParams.wjid = props.wjid;
    queryParams.bdid = props.bdid;
    queryParams.rwid = props.rwid;
    handleQuery();
  },
  { deep: true }
);

// 采购申请明细表格数据
const pageData = ref<ZxcgMxPageVO[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});
/** 查询采购申请明细 */
function handleQuery() {
  loading.value = true;
  ZxcgMxAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
      if (data.list.length > 0) {
        if (data.list[0].cgml) {
          cgmllx.value = data.list[0].cgml.substring(0, 1);
        }
      }
    })
    .finally(() => {
      loading.value = false;
    });
}
/** 重置采购申请明细查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}
/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  removeIds.value = selection.map((item: any) => item.mxid);
}
/** 打开采购申请明细弹窗 */
function handleEdit(id?: number) {
  dialog.visible = true;
  if (id) {
    dialog.title = "修改采购申请明细";
    ZxcgMxAPI.getFormData(id).then((data) => {
      Object.assign(formData, data);
    });
  } else {
    dialog.title = "新增采购申请明细";
  }
}
/** 关闭采购申请明细弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  Object.assign(formData, {});
}
/** 删除采购申请明细 */
function handleDelete(id?: number) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      ZxcgMxAPI.deleteByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}
onMounted(() => {
  handleQuery();
});

//--------------以下是form表单相关
const dataFormRef = ref(ElForm);
// 采购申请明细表单数据
const formData = reactive<ZxcgMxForm>({});
const formType = reactive({
  add: false,
  edit: false,
  view: false,
});
// 采购申请明细表单校验规则
const rules = reactive({
  Wpmc: [{ required: true, message: "请输入明细编号", trigger: "blur" }],
});
/** 提交采购申请明细表单 */
function handleSubmit() {
  dataFormRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      const id = formData.mxid;
      if (id) {
        ZxcgMxAPI.update(id, formData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        ZxcgMxAPI.add(formData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}
function handleBack() {
  handleCloseDialog();
  handleResetQuery();
}
defineExpose({
  removeIds,
});
</script>
<style lang="scss" scoped></style>
