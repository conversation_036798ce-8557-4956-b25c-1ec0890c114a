<template>
  <el-scrollbar
    ref="scrollArea"
    class="scroll-container"
    :height="props.height"
    @mouseenter="stopScroll"
    @mouseleave="startScroll"
    :datacount="props.datacount"
  >
    <slot />
    <slot v-if="props.datacount > 4" />
    <slot v-if="props.datacount > 4" />
    <slot v-if="props.datacount > 4" />
  </el-scrollbar>
</template>
<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { ElScrollbar } from "element-plus";
import { number } from "echarts";

//设置滚动高度，默认300
const props = defineProps({
  height: {
    type: String,
    default: 300,
  },
  datacount: {
    type: number,
    default: -1,
  },
});

const scrollArea = ref(null);
let timer = null;
const SCROLL_SPEED = 1; // 每次滚动的像素数，可以根据需要调整
const startScroll = () => {
  if (timer) {
    clearInterval(timer);
  }
  timer = setInterval(() => {
    // 获取滚动容器
    const container = scrollArea.value.$el.querySelector(".el-scrollbar__wrap");
    // 判断是否已滚动到底部
    if (container.scrollTop >= container.scrollHeight / 2) {
      // 滚动到顶部
      container.scrollTop = 0;
    } else {
      // 向下滚动
      container.scrollTop += SCROLL_SPEED;
    }
  }, 30); // 根据需要调整滚动间隔
};

const stopScroll = () => {
  if (timer) {
    clearInterval(timer);
  }
};

onMounted(() => {
  startScroll();
});

onUnmounted(() => {
  stopScroll();
});
</script>
<style lang="scss" scoped>
.scroll-container {
  width: 100%;
}
</style>
