import request from "@/utils/request";

const DISPOSE_BASE_URL = "/api/v1/dispose";

const disposeAPI = {
  /** 获取处置分页数据 */
  getPage(queryParams?: disposePageQuery) {
    return request<any, PageResult<disposePageVO[]>>({
      url: `${DISPOSE_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取处置表单数据
   *
   * @param id disposeID
   * @returns dispose表单数据
   */
  getFormData(guid: string) {
    return request<any, disposeForm>({
      url: `${DISPOSE_BASE_URL}/${guid}/form`,
      method: "get",
    });
  },

  /** 添加处置*/
  add(data: disposeForm) {
    return request({
      url: `${DISPOSE_BASE_URL}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新处置
   *
   * @param id disposeID
   * @param data dispose表单数据
   */
  update(id: number, data: disposeForm) {
    return request({
      url: `${DISPOSE_BASE_URL}/update/${id}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 提交处置/api/v1/disposes/submit
   *
   */
  submit(guid: string,data?: any) {
    return request({
      url: `${DISPOSE_BASE_URL}/submit/${guid}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 批量删除处置，多个以英文逗号(,)分割
   *
   * @param ids 处置ID字符串，多个以英文逗号(,)分割
   */

  deleteDetailsByIds(ids: string) {
    return request({
      url: `${DISPOSE_BASE_URL}/details/delete/${ids}`,
      method: "get",
    });
  },

  /**
   * 批量删除处置，多个以英文逗号(,)分割
   *
   * @param ids 处置ID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${DISPOSE_BASE_URL}/delete/${ids}`,
      method: "get",
    });
  },

  /** 添加处置明细*/
  addDetail(data: { czGuid: string; zcGuid: string }) {
    return request({
      url: `${DISPOSE_BASE_URL}/details/add`,
      method: "post",
      data: data,
    });
  },

  // 调拨单明细查询
  getDetailsPage(queryParams?: any) {
    return request<any, PageResult<any[]>>({
      url: `${DISPOSE_BASE_URL}/details/page`,
      method: "get",
      params: queryParams,
    });
  },

  //新增处置单，选明细生成处置单/api/v1/disposes
  addDisposeByDetails(data: addParam) {
    return request({
      url: `${DISPOSE_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  },

  /** 获取已处置资产分页数据 */
  getDisposedPage(queryParams?: disposedPageQuery) {
    return request<any, PageResult<any[]>>({
      url: `${DISPOSE_BASE_URL}/dispose-card/page`,
      method: "get",
      params: queryParams,
    });
  },

  //待处置申请/api/v1/disposes/WaitDisposeCard-page
  getWaitDisposePage(queryParams?: any) {
    return request<any, PageResult<any[]>>({
      url: `${DISPOSE_BASE_URL}/wait-dispose/card-page`,
      method: "get",
      params: queryParams,
    });
  },
};

export default disposeAPI;

/** 处置分页查询参数 */
export interface disposePageQuery extends PageQuery {
  /** 处置单号 || cz+年+月+4位流水号 */
  czbh?: string;
  /** 处置类型：0报废、1报损、2出售、3出租、4出让、5转让、6置换、7赠与 */
  czlx?: string;
  /** 分类集格式 */
  djbm?: string;
  /** 登记时间 ||  */
  djsj?: [string, string];
  /** 节点编码 ||  */
  netcode?: string;
  /** 数据状态 || 0暂存1提交2退回（流程用）0暂存1已发送2已回传登记（接口用） */
  sjzt?: string;
  djr?: string;
  by5?: string;
  type?: string;

  checkstatus?: number;
}
export interface addParam {
  /** 序号 ||  */
  zcguid?: string;
  /** 资产处置guid ||  */
  czlx?: string;
}

/** 处置表单对象 */
export interface disposeForm {
  /** 序号 ||  */
  id?: number;
  /** 资产处置guid ||  */
  guid?: string;
  /** 处置单号 || cz+年+月+4位流水号 */
  czbh?: string;
  /** 处置类型：0报废、1报损、2出售、3出租、4出让、5转让、6置换、7赠与 */
  czlx?: string;
  /** 处置原因 ||  */
  czyy?: string;
  /** 处置资产原值 */
  czyzyz?: number;
  /** 分类集格式 */
  djbm?: string;
  /** 登记人 ||  */
  djr?: string;
  /** 登记时间 ||  */
  djsj?: string;
  /** 节点编码 ||  */
  netcode?: string;
  /** 数据状态 || 0暂存1提交2退回（流程用）0暂存1已发送2已回传登记（接口用） */
  sjzt?: string;
  /** 下一审批人|hidden */
  nexter?: string;
  /** 下一审批部门|hidden */
  nextbm?: string;
  /** 部门资产管理员 */
  frmcode?: string;
  /** 备注 ||  */
  notes?: string;
  /** 公示日期 */
  gsrq?: Date;
  /** 备用a ,终止理由 */
  by1?: string;
  /** 备用b || (被处置的资产的数量) */
  by2?: string;
  /** 备用c （财政处置编号） */
  by3?: string;
  /** 备用d || （受理单的guid） */
  by4?: string;
  /** 备用e || (单据类型，0申请单，1受理单) */
  by5?: string;
  /** 备用f  || (学校资产分管领导) */
  by6?: string;
  /** 受理数量 */
  slsl?: number;
  /** 受理金额 */
  slje?: number;
  /** 部门负责人编码 */
  bmfzr?: string;
  djrname?: string;
  djbmname?: string;
}

/** 处置分页对象 */
export interface disposePageVO {
  /** 序号 ||  */
  id?: number;
  /** 资产处置guid ||  */
  guid?: string;
  /** 处置单号 || cz+年+月+4位流水号 */
  czbh?: string;
  /** 处置类型：0报废、1报损、2出售、3出租、4出让、5转让、6置换、7赠与 */
  czlx?: string;
  /** 处置原因 ||  */
  czyy?: string;
  /** 处置资产原值 */
  czyzyz?: number;
  /** 分类集格式 */
  djbm?: string;
  /** 登记人 ||  */
  djr?: string;
  /** 登记时间 ||  */
  djsj?: Date;
  /** 节点编码 ||  */
  netcode?: string;
  /** 数据状态 || 0暂存1提交2退回（流程用）0暂存1已发送2已回传登记（接口用） */
  sjzt?: string;
  /** 下一审批人|hidden */
  nexter?: string;
  /** 下一审批部门|hidden */
  nextbm?: string;
  /** 部门资产管理员 */
  frmcode?: string;
  /** 备注 ||  */
  notes?: string;
  /** 公示日期 */
  gsrq?: Date;
  /** 备用a ,终止理由 */
  by1?: string;
  /** 备用b || (被处置的资产的数量) */
  by2?: string;
  /** 备用c （财政处置编号） */
  by3?: string;
  /** 备用d || （受理单的guid） */
  by4?: string;
  /** 备用e || (单据类型，0申请单，1受理单) */
  by5?: string;
  /** 备用f  || (学校资产分管领导) */
  by6?: string;
  /** 受理数量 */
  slsl?: number;
  /** 受理金额 */
  slje?: number;
  /** 部门负责人编码 */
  bmfzr?: string;
}

export interface disposedPageQuery extends PageQuery {
  czGuid?: string;
  zcbh?: string;
  zcmc?: string;
  syxz?: string;
  syr?: string;
  sybm?: string;
  czlx?: string;
}
