<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item label="组织机构代码" prop="code">
            <el-input
              v-model="queryParams.code"
              placeholder="组织机构代码"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item label="供应商名称" prop="cname">
            <el-input
              v-model="queryParams.cname"
              placeholder="供应商名称"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon><Search /></template>
              搜索
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon><Refresh /></template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="列表">
        <div>
          <el-button
            type="success"
            plain
            size="small"
            @click="
              formType.view = false;
              handleOpenDialog(0);
            "
          >
            <template #icon><Plus /></template>
            新增
          </el-button>
          <el-button
            type="danger"
            plain
            size="small"
            :disabled="removeIds.length === 0"
            @click="handleDelete()"
          >
            <template #icon><Delete /></template>
            删除
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed />
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          key="code"
          label="组织机构代码"
          prop="code"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="cname"
          label="供应商名称"
          prop="cname"
          min-width="150"
          align="center"
        />

        <el-table-column
          key="address"
          label="单位地址"
          prop="address"
          min-width="150"
          align="center"
        />
        <el-table-column key="zczj" label="注册资金" prop="zczj" min-width="150" align="center" />
        <el-table-column key="zcrq" label="成立日期" prop="zcrq" min-width="150" align="center" />
        <el-table-column
          key="lxrname"
          label="联系人姓名"
          prop="lxrname"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="lxrmobie"
          label="联系人手机"
          prop="lxrmobie"
          min-width="150"
          align="center"
        />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              icon="Edit"
              link
              @click="
                formType.view = false;
                handleOpenDialog(scope.row.id);
              "
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              icon="Delete"
              link
              @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
            <el-button
              v-if="view"
              type="success"
              icon="Document"
              size="small"
              link
              @click="handleOpenDialog(scope.row.id)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- InfoGys表单弹窗 -->
    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      append-to-body
      size="75%"
      :before-close="handleCloseDialog"
    >
      <el-card>
        <template #header>
          <Title name="供应商信息">
            <div>
              <el-button v-if="!formType.view" type="primary" @click="handleSubmit()">
                保存
              </el-button>
            </div>
          </Title>
        </template>

        <el-form
          ref="dataFormRef"
          :model="formData"
          :rules="rules"
          label-width="100px"
          :inline="true"
          :disabled="formType.view"
        >
          <el-form-item label="组织机构编码" prop="code">
            <el-input v-model="formData.code" />
          </el-form-item>
          <el-form-item label="供应商名称" prop="cname">
            <el-input v-model="formData.cname" />
          </el-form-item>
          <el-form-item label="法人姓名" prop="frname">
            <el-input v-model="formData.frname" placeholder="法人姓名" />
          </el-form-item>
          <el-form-item label="单位地址" prop="address">
            <el-input v-model="formData.address" placeholder="单位地址" />
          </el-form-item>
          <el-form-item label="注册资金" prop="zczj">
            <el-input v-model="formData.zczj" placeholder="注册资金" />
          </el-form-item>
          <el-form-item label="成立日期" prop="zcrq">
            <el-date-picker
              v-model="formData.zcrq"
              type="date"
              placeholder="成立日期"
              size="default"
            />
          </el-form-item>
          <el-form-item label="经营范围" prop="jyfw">
            <el-input v-model="formData.jyfw" placeholder="经营范围" />
          </el-form-item>
          <el-form-item label="银行帐号" prop="yhzh">
            <el-input v-model="formData.yhzh" placeholder="银行帐号" />
          </el-form-item>
          <el-form-item label="开户银行" prop="khyh">
            <el-input v-model="formData.khyh" placeholder="开户银行" />
          </el-form-item>
          <el-form-item label="联系人姓名" prop="lxrname">
            <el-input v-model="formData.lxrname" placeholder="联系人姓名" />
          </el-form-item>
          <el-form-item label="联系人手机" prop="lxrmobie">
            <el-input v-model="formData.lxrmobie" placeholder="联系人手机" />
          </el-form-item>
          <el-form-item label="联系人固定电话" prop="lxrtel">
            <el-input v-model="formData.lxrtel" placeholder="联系人固定电话" />
          </el-form-item>
          <el-form-item label="联系人邮箱" prop="lxremail">
            <el-input v-model="formData.lxremail" placeholder="联系人邮箱" />
          </el-form-item>
          <el-form-item label="组织机构代码证照片" prop="zzjgpic">
            <el-input v-model="formData.zzjgpic" placeholder="组织机构代码证照片" />
          </el-form-item>
          <el-form-item label="营业执照注册号" prop="yyzz">
            <el-input v-model="formData.yyzz" placeholder="营业执照注册号" />
          </el-form-item>
          <el-form-item label="地税登记证编号" prop="dsdjz">
            <el-input v-model="formData.dsdjz" placeholder="地税登记证编号" />
          </el-form-item>
          <el-form-item label="营业执照照片" prop="yyzzpic">
            <el-input v-model="formData.yyzzpic" placeholder="营业执照照片" />
          </el-form-item>
          <el-form-item label="税务登记证照片" prop="swdjzpic">
            <el-input v-model="formData.swdjzpic" placeholder="税务登记证照片" />
          </el-form-item>
          <el-form-item label="备注" prop="note">
            <el-input v-model="formData.note" placeholder="备注" />
          </el-form-item>
        </el-form>
        <template #footer />
      </el-card>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "InfoGys",
  inheritAttrs: false,
});
import { useUserStore } from "@/store";
import InfoGysAPI, { InfoGysPageVO, InfoGysForm, InfoGysPageQuery } from "@/api/cg/info-gys";

// 获取路由参数,view/add/check
const route = useRoute();
const type = ref(route.query.type?.toString() || "view");
//显示隐藏权限,用于v-if
const add = type.value == "add";
const view = type.value == "view";
const check = type.value == "check";
//页面参数
const queryFormRef = ref(ElForm);
const loading = ref(false);
const removeIds = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<InfoGysPageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// InfoGys表格数据
const pageData = ref<InfoGysPageVO[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

/** 查询InfoGys */
function handleQuery() {
  loading.value = true;
  InfoGysAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置InfoGys查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  removeIds.value = selection.map((item: any) => item.id);
}

/** 打开InfoGys弹窗 */
function handleOpenDialog(id?: number) {
  dialog.visible = true;
  if (id && id > 0) {
    dialog.title = "修改InfoGys";
    InfoGysAPI.getFormData(id).then((data) => {
      Object.assign(formData, data);
    });
  } else {
    dialog.title = "新增InfoGys";
  }
}

/** 关闭InfoGys弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  Object.assign(formData, {});
}

/** 删除InfoGys */
function handleDelete(id?: number) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      InfoGysAPI.deleteByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

onMounted(() => {
  handleQuery();
});

//--------------以下是form表单相关
const dataFormRef = ref(ElForm);

// InfoGys表单数据
const formData = reactive<InfoGysForm>({});

const formType = reactive({
  add: false,
  edit: false,
  view: false,
});

// InfoGys表单校验规则
const rules = reactive({
  code: [{ required: true, message: "请输入机构编码", trigger: "blur" }],
  cname: [{ required: true, message: "请输入机构名称", trigger: "blur" }],
});

/** 提交InfoGys表单 */
function handleSubmit() {
  dataFormRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      const id = formData.id;
      if (id) {
        InfoGysAPI.update(id, formData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        InfoGysAPI.add(formData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}
</script>
<style lang="scss" scoped></style>
