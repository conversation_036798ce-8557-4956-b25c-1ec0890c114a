<template>
  <div class="agent-list-page">
    <div class="page-header">
      <h2 class="page-title">智能体广场</h2>
      <!-- <el-button type="primary" @click="fetchAgents">
        <el-icon><Refresh /></el-icon>
        刷新列表
      </el-button> -->
    </div>

    <div v-loading="loading" class="agents-container">
      <template v-if="mockAgents.length > 0">
        <el-row :gutter="20">
          <el-col
            v-for="agent in mockAgents"
            :key="agent.id"
            :xs="24"
            :sm="12"
            :md="8"
            :lg="6"
            style="padding: 10px"
          >
            <div class="agent-card" @click="handleAgentClick(agent)">
              <div class="agent-card-header">
                <el-avatar :size="60" :src="agent.avatar" :alt="agent.name" class="agent-avatar">
                  {{ agent.name.charAt(0) }}
                </el-avatar>
                <h3 class="agent-name">{{ agent.name }}</h3>
              </div>
              <div class="agent-card-body">
                <p class="agent-description">{{ agent.description || "暂无描述" }}</p>
                <div v-if="agent.tags && agent.tags.length" class="agent-tags">
                  <el-tag
                    v-for="tag in agent.tags"
                    :key="tag"
                    size="small"
                    effect="light"
                    class="agent-tag"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
              <div class="agent-card-footer">
                <el-button type="primary" size="small" class="start-chat-btn">开始对话</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </template>
      <el-empty v-else description="暂无可用智能体" />
    </div>

    <el-drawer v-model="visible" :title="title" append-to-body size="75%">
      <iframe
        :src="agentid"
        style="width: 100%; height: 100%; min-height: 700px"
        frameborder="0"
        allow="microphone"
      />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";

defineOptions({
  name: "AgentList",
});
// 路由实例
const router = useRouter();
// 状态变量
const loading = ref<boolean>(false);

// 模拟智能体数据
const mockAgents = [
  {
    id: "agent-001",
    name: "教学问答",
    description: "包含课程知识体系，问答内容覆盖作业题，练习题，学科重点，知识图谱等",
    avatar: "https://api.dicebear.com/7.x/bottts/svg?seed=agent001",
    tags: ["助学"],
    agentid: "VYhapMu7ovCcxLBc",
  },
  {
    id: "agent-002",
    name: "教学助手",
    description:
      "按照课程知识点描述生成各类课程知识图谱、教学计划、教学大纲、授课计划，学籍信息、考务信息、课程信息、测试题、考试题、试卷评审等辅助教师教学和教务",
    avatar: "https://api.dicebear.com/7.x/bottts/svg?seed=agent002",
    tags: ["助教"],
    agentid: "bULFZ6FOZjJkSkab",
  },
  {
    id: "agent-003",
    name: "教学管理助手",
    description:
      "按照教学管理要求描述查询各类课程、教学计划、教学大纲、授课计划，学生名册、实验报告、竞赛报告等",
    avatar: "https://api.dicebear.com/7.x/bottts/svg?seed=agent003",
    tags: ["助管"],
    agentid: "00huMRWmegzaF2X9",
  },
  {
    id: "agent-004",
    name: "教学质量检测助手",
    description:
      "按照教学目标要求和教学评价机制，自动生成各类教学评价报告，并主动发现教学和学生能力短板，异常情况下提醒教学质量。",
    avatar: "https://api.dicebear.com/7.x/bottts/svg?seed=agent004",
    tags: ["助监"],
    agentid: "DbQ3Qb7SGtLO3MVS",
  },

  //采购目录匹配：w02GZVTE3m8RYC73
  //可行性报告：vsgvOhfclVf8zsZY
  //询价单：dUu0ox7bLLIlJ5MW
  {
    id: "agent-005",
    name: "采购目录匹配",
    description: "根据输入的货品名称，匹配采购目录中的编码信息",
    avatar: "https://api.dicebear.com/7.x/bottts/svg?seed=agent005",
    tags: ["采购"],
    agentid: "w02GZVTE3m8RYC73",
  },

  {
    id: "agent-006",
    name: "可行性报告",
    description: "根据输入的采购需求，生成可行性报告",
    avatar: "https://api.dicebear.com/7.x/bottts/svg?seed=agent006",
    tags: ["采购"],
    agentid: "vsgvOhfclVf8zsZY",
  },

  {
    id: "agent-007",
    name: "采购询价单",
    description: "根据输入的采购需求生成询价单",
    avatar: "https://api.dicebear.com/7.x/bottts/svg?seed=agent007",
    tags: ["采购"],
    agentid: "dUu0ox7bLLIlJ5MW",
  },
];

//弹窗管理
const visible = ref(false);
const title = ref("");
const agentid = ref("");
// 处理智能体卡片点击
const handleAgentClick = (agent: any) => {
  visible.value = true;
  agentid.value = "http://**************/chatbot/" + agent.agentid;
  title.value = agent.name;
};

// <iframe
//  src="http://**************/chatbot/w02GZVTE3m8RYC73"
//  style="width: 100%; height: 100%; min-height: 700px"
//  frameborder="0"
//  allow="microphone">
// </iframe>

// 组件挂载时获取智能体列表
onMounted(() => {
  // fetchAgents();
});
</script>

<style lang="scss" scoped>
.agent-list-page {
  padding: 20px;
  height: 100%;
  margin: 0;
  background-color: #ffffff;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }
  }

  .agents-container {
    min-height: 300px;
  }

  .agent-card {
    height: 100%;
    margin-bottom: 10px;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;

    &:hover {
      transform: translateY(-6px);
      box-shadow: 0 6px 18px 0 rgba(0, 0, 0, 0.15);

      .start-chat-btn {
        background-color: #409eff;
        color: white;
      }
    }

    .agent-card-header {
      padding: 20px 16px;
      text-align: center;
      background-color: #f5f7fa;
      border-bottom: 1px solid #ebeef5;

      .agent-avatar {
        margin-bottom: 12px;
        border: 2px solid #fff;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      }

      .agent-name {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }

    .agent-card-body {
      flex: 1;
      padding: 20px 16px;

      .agent-description {
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 16px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        min-height: 60px;
      }

      .agent-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .agent-tag {
          margin: 0;
          border-radius: 4px;
        }
      }
    }

    .agent-card-footer {
      padding: 16px;
      border-top: 1px solid #ebeef5;
      text-align: center;

      .start-chat-btn {
        width: 100%;
        transition: all 0.3s ease;
        background-color: #ecf5ff;
        color: #409eff;
        border-color: #d9ecff;
      }
    }
  }

  // 响应式调整
  @media (max-width: 768px) {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .page-title {
        margin-bottom: 8px;
      }
    }

    .agent-card {
      .agent-card-header {
        padding: 16px 12px;

        .agent-avatar {
          margin-bottom: 10px;
        }

        .agent-name {
          font-size: 16px;
        }
      }

      .agent-card-body {
        padding: 16px 12px;

        .agent-description {
          margin-bottom: 14px;
          min-height: 40px;
        }
      }

      .agent-card-footer {
        padding: 12px;
      }
    }
  }
}
</style>
