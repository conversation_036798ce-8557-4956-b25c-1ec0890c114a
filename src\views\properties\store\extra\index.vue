<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <Title name="附属件列表">
          <div v-if="props.editable">
            <el-button
              type="success"
              plain
              @click="
                formType.view = false;
                handleEdit();
              "
            >
              <template #icon><Plus /></template>
              新增
            </el-button>
            <el-button
              type="danger"
              plain
              :disabled="removeIds.length === 0"
              @click="handleDelete()"
            >
              <template #icon><Delete /></template>
              删除
            </el-button>
          </div>
        </Title>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->

      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
      >
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column key="id" label="序号" prop="id" min-width="150" align="center" />
        <el-table-column key="fsjmc" label="名称" prop="fsjmc" min-width="150" align="center" />
        <el-table-column
          key="fsjmc"
          label="单价（元）"
          prop="fsjmc"
          min-width="150"
          align="center"
        />
        <el-table-column key="fjbh" label="数量" prop="fjbh" min-width="150" align="center" />
        <el-table-column key="sl" label="附属件名称" prop="sl" min-width="150" align="center" />
        <el-table-column key="jldw" label="计量单位" prop="jldw" min-width="150" align="center" />
        <el-table-column key="pp" label="品牌" prop="pp" min-width="150" align="center" />
        <el-table-column key="ggxh" label="规格型号" prop="ggxh" min-width="150" align="center" />
        <el-table-column key="fsjsm" label="说明" prop="fsjsm" min-width="150" align="center" />
        <el-table-column fixed="right" label="操作" width="180">
          <template #default="scope">
            <el-button
              v-if="props.editable"
              type="primary"
              size="small"
              icon="Edit"
              link
              @click="
                formType.view = false;
                handleEdit(scope.row.id);
              "
            >
              编辑
            </el-button>
            <el-button
              v-if="props.editable"
              type="danger"
              size="small"
              icon="Delete"
              link
              @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
            <el-button
              v-if="!props.editable"
              type="success"
              icon="Document"
              size="small"
              link
              @click="handleEdit"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 附属件表单弹窗 -->
    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      append-to-body
      size="45%"
      @close="handleCloseDialog"
      closeable
    >
      <el-card>
        <template #header>
          <Title name="附属件信息">
            <div>
              <el-button v-if="!formType.view" type="primary" @click="handleSubmit()">
                保存
              </el-button>
            </div>
          </Title>
        </template>

        <el-form
          ref="dataFormRef"
          :model="formData"
          :rules="rules"
          label-width="100px"
          :inline="true"
          :disabled="formType.view"
        >
          <el-form-item label="附属件名称" prop="fsjmc">
            <el-input v-model="formData.fsjmc" placeholder="附属件名称" />
          </el-form-item>
          <el-form-item label="单价(元)" prop="dj">
            <el-input v-model="formData.dj" placeholder="单价" />
          </el-form-item>
          <el-form-item label="数量" prop="sl">
            <el-input v-model="formData.sl" placeholder="数量" />
          </el-form-item>
          <el-form-item label="计量单位" prop="jldw">
            <el-input v-model="formData.jldw" placeholder="计量单位" />
          </el-form-item>
          <el-form-item label="品牌" prop="pp">
            <el-input v-model="formData.pp" placeholder="品牌" />
          </el-form-item>
          <el-form-item label="规格型号" prop="ggxh">
            <el-input v-model="formData.ggxh" placeholder="规格型号" />
          </el-form-item>
          <el-form-item label="说明" prop="fsjsm">
            <el-input v-model="formData.fsjsm" placeholder="附属件说明" />
          </el-form-item>
        </el-form>
        <template #footer />
      </el-card>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import ZcFsjbAPI, { ZcFsjbPageVO, ZcFsjbForm, ZcFsjbPageQuery } from "@/api/properties/extra";

const props = defineProps({
  //查询详情的调拨单ID和guid
  id: {
    type: Number,
  },
  guid: {
    type: String,
    required: true,
  },
  //是否可编辑
  editable: {
    type: Boolean,
  },
});
// 获取路由参数,view/add/check
const route = useRoute();
const type = ref(route.query.type?.toString() || "view");
//显示隐藏权限,用于v-if
const add = type.value == "add";
const view = type.value == "view";
const check = type.value == "check";
//页面参数
const loading = ref(false);
const removeIds = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<ZcFsjbPageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// 附属件表格数据
const pageData = ref<ZcFsjbPageVO[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

/** 查询附属件 */
function handleQuery() {
  loading.value = true;
  ZcFsjbAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置附属件查询 */
function handleResetQuery() {
  queryParams.pageNum = 1;
  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  removeIds.value = selection.map((item: any) => item.id);
}

/** 打开附属件弹窗 */
function handleEdit(id?: number) {
  dialog.visible = true;
  if (id) {
    dialog.title = "修改附属件";
    ZcFsjbAPI.getFormData(id).then((data) => {
      Object.assign(formData, data);
    });
  } else {
    dialog.title = "新增附属件";
  }
}

/** 关闭附属件弹窗 */
function handleCloseDialog() {
  handleResetQuery();
  dialog.visible = false;
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();

  formData.djsj = undefined;
  formData.id = undefined;
  formData.guid = undefined;
}

/** 删除附属件 */
function handleDelete(id?: number) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      ZcFsjbAPI.deleteByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

onMounted(() => {
  handleQuery();
});

//--------------以下是form表单相关
const dataFormRef = ref(ElForm);

// 附属件表单数据
const formData = reactive<ZcFsjbForm>({
  rkguid: props.guid,
});

const formType = reactive({
  add: false,
  edit: false,
  view: false,
});

// 附属件表单校验规则
const rules = reactive({});

/** 提交附属件表单 */
function handleSubmit() {
  dataFormRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      const id = formData.id;
      if (id) {
        ZcFsjbAPI.update(id, formData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
          })
          .finally(() => (loading.value = false));
      } else {
        ZcFsjbAPI.add(formData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}
</script>
<style lang="scss" scoped></style>
