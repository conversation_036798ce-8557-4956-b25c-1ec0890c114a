<template>
  <el-input v-model="filterText" placeholder="请输入关键字" />
  <el-scrollbar height="500px">
    <el-tree
      ref="treeRef"
      class="filter-tree"
      :data="pageData.treeData"
      :props="pageData.defaultProps"
      :default-expand-all="false"
      :filter-node-method="filterNode"
    />
  </el-scrollbar>
  <div class="center">
    <el-button type="primary" @click="handleSubmit">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import storeAPI from "@/api/properties/store";
import { jycodes } from "@/assets/JYCode";
import { czcodes } from "@/assets/CZCode";
//根据guid获取节点信息
const props = defineProps({
  type: {
    type: String,
    required: true,
  },
});
const pageData = reactive({
  treeData: <any[]>[],

  defaultProps: {
    children: "children",
    label: "label",
  },
});

interface Tree {
  [key: string]: any;
}

const filterText = ref("");
const treeRef = ref<InstanceType<typeof ElTree>>();
const emits = defineEmits(["node-click"]);

watch(filterText, (val) => {
  treeRef.value!.filter(val);
});

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.label.includes(value);
};

//选择节点事件
const handleSubmit = () => {
  if (!treeRef.value!.getCurrentNode().children) {
    emits("node-click", treeRef.value!.getCurrentNode().value);
  }
};

onMounted(() => {
  //判断是选择教育分类还是财政分类
  if (props.type == "JY") {
    pageData.treeData = jycodes.value;
  } else if (props.type == "CZ") {
    pageData.treeData = czcodes.value;
  }
});
</script>
<style lang="scss" scoped>
.center {
  text-align: center;
}
</style>
