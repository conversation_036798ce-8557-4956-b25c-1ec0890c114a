<template>
  <!-- 支持搜索 -->
  <el-select
    v-model="_modelValue"
    filterable
    :loading="loading"
    :placeholder="placeholder"
    clearable
  >
    <el-option v-for="item in options" :key="item.XCODE" :label="item.NAME" :value="item.XCODE" />
  </el-select>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from "vue";
import SysAPI, { UserListQuery } from "@/api/system/sys";
import { ElMessage } from "element-plus";

interface Xcode {
  XCODE: string;
  NAME: string;
}

interface Props {
  modelValue?: string;
  type?: string;
  placeholder?: string;
}

// Component props
const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  type: "",
  placeholder: "请选择",
});

// Component emits
const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
}>();

// Component state
const loading = ref(false);
const options = ref<Xcode[]>([]);

// v-model implementation
const _modelValue = computed({
  get: () => props.modelValue,
  set: (val) => emit("update:modelValue", val || ""),
});

// Lifecycle hooks
onMounted(async () => {
  if (props.type) {
    await loadData();
  }
});

// Data loading handler
const loadData = async () => {
  switch (props.type) {
    case "PropertyManager":
      await getPropertyManagerList();
      break;
    default:
      console.warn(`Unsupported type: ${props.type}`);
      break;
  }
};

// API calls
const getPropertyManagerList = async () => {
  loading.value = true;

  try {
    const params: UserListQuery = {
      rcode: "0202",
      username: "",
      ucode: "",
      dcode: "",
      pageNum: 1,
      pageSize: 100,
    };

    const res = await SysAPI.getUserList(params);

    if (res?.list) {
      options.value = res.list.map((item: any) => ({
        XCODE: item.ucode,
        NAME: item.nickname,
      }));
    }
  } catch (error) {
    console.error("Failed to fetch property manager list:", error);
    ElMessage.error("获取数据失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};
</script>
