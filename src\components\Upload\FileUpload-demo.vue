<!-- 文件上传组件 -->
<template>
  <div class="file-upload-container">
    <!-- 文件列表表格 -->
    <div class="table-wrapper">
      <el-card shadow="never">
        <div class="mb-10px">
          <el-upload v-model:file-list="fileList" :headers="props.headers" :data="props.data" :name="props.name"
            :before-upload="handleBeforeUpload" :on-remove="handleRemove" :on-progress="handleProgress"
            :on-success="handleSuccessFile" :on-error="handleError" :action="props.action" :accept="props.accept"
            :limit="props.limit" :show-file-list="false" class="header-upload">
            <el-button type="primary" :disabled="fileList.length >= props.limit">
              <el-icon class="upload-icon">
                <Upload />
              </el-icon>
              {{ props.uploadBtnText }}
            </el-button>
          </el-upload>
        </div>
        <el-table :data="fileList" style="width: 100%" size="large" border>
          <el-table-column type="index" label="序号" width="80" align="center" />

          <el-table-column label="文件名" min-width="400" align="center">
            <template #default="{ row }">
              <div class="file-name-cell">
                <el-icon>
                  <Document />
                </el-icon>
                <span class="file-name">{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === 'success' ? 'success' : 'warning'">
                {{ row.status === 'success' ? '已上传' : '上传中' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" align="center">
            <template #default="{ row }">
              <div class="operation-cell">
                <el-button type="primary" link @click="downloadFile(row)">
                  <el-icon>
                    <Download />
                  </el-icon>下载
                </el-button>
                <el-button v-if="props.showDelBtn" type="danger" link @click="handleRemove(row)">
                  <el-icon>
                    <Delete />
                  </el-icon>删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 上传进度遮罩 -->
        <div v-if="showUploadPercent" class="upload-overlay">
          <div class="upload-progress-content">
            <el-icon class="upload-icon">
              <Upload />
            </el-icon>
            <div class="progress-info">
              <div class="progress-text">
                正在上传...{{ uploadPercent }}%
              </div>
              <el-progress :percentage="uploadPercent" :color="customColorMethod" :stroke-width="8"
                :show-text="false" />
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  UploadRawFile,
  UploadUserFile,
  UploadFile,
  UploadProgressEvent,
  UploadFiles,
} from "element-plus";

import FileAPI from "@/api/file";
import { getToken } from "@/utils/auth";
import { ResultEnum } from "@/enums/ResultEnum";

const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  /**
   * 文件集合
   */
  modelValue: {
    type: Array<UploadUserFile>,
    default: () => [],
  },
  /**
   * 上传地址
   */
  action: {
    type: String,
    default: FileAPI.uploadUrl,
  },
  /*
    必填
  */
  guid: {
    type: String,
    default: "",
  },
  /*
    必填
  */
  type: {
    type: String,
    default: "",
  },
  /**
   * 文件上传数量限制
   */
  limit: {
    type: Number,
    default: 10,
  },
  /**
   * 是否显示删除按钮
   */
  showDelBtn: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否显示上传按钮
   */
  showUploadBtn: {
    type: Boolean,
    default: true,
  },
  /**
   * 单个文件上传大小限制(单位MB)
   */
  maxSize: {
    type: Number,
    default: 2 * 1024 * 1024,
  },
  /**
   * 上传文件类型
   */
  accept: {
    type: String,
    default: "*",
  },
  /**
   * 上传按钮文本
   */
  uploadBtnText: {
    type: String,
    default: "上传文件",
  },
  /**
   * 是否展示提示信���
   */
  showTip: {
    type: Boolean,
    default: false,
  },
  /**
   * 提示信息内容
   */
  tip: {
    type: String,
    default: "",
  },
  /**
   * 请求头
   */
  headers: {
    type: Object,
    default: () => {
      return {
        Authorization: getToken(),
      };
    },
  },
  /**
   * 请求携带的额外参数
   */
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
  /**
   * 上传文件的参数名
   */
  name: {
    type: String,
    default: "file",
  },
  /**
   * 样式
   */
  style: {
    type: Object,
    default: () => {
      return {
        width: "300px",
      };
    },
  },
});

const fileList = ref([] as UploadUserFile[]);
const valFileList = ref([] as UploadUserFile[]);
const showUploadPercent = ref(false);
const uploadPercent = ref(0);

watch(
  () => props.modelValue,
  (newVal: UploadUserFile[]) => {
    const filePaths = fileList.value.map((file) => file.url);
    const fileNames = fileList.value.map((file) => file.name);
    // 监听modelValue文件集合值未变化时，跳过赋值
    if (
      filePaths.length > 0 &&
      filePaths.length === newVal.length &&
      filePaths.every((x) => newVal.some((y) => y.url === x)) &&
      newVal.every((y) => filePaths.some((x) => x === y.url)) &&
      fileNames.every((x) => newVal.some((y) => y.name === x)) &&
      newVal.every((y) => fileNames.some((x) => x === y.name))
    ) {
      return;
    }

    if (newVal.length <= 0) {
      fileList.value = [];
      valFileList.value = [];
      return;
    }

    fileList.value = newVal.map((file) => {
      return { name: file.name, url: file.url } as UploadFile;
    });

    valFileList.value = newVal.map((file) => {
      return { name: file.name, url: file.url } as UploadFile;
    });
  },
  { immediate: true }
);

/**
 * 限制用户上传文件的大小
 */
function handleBeforeUpload(file: UploadRawFile) {
  if (file.size > props.maxSize) {
    ElMessage.warning("上传文件不能大于" + props.maxSize + "M");
    return false;
  }
  uploadPercent.value = 0;
  showUploadPercent.value = true;
  return true;
}

const handleSuccessFile = (response: any, file: UploadFile) => {
  showUploadPercent.value = false;
  uploadPercent.value = 0;
  if (response.code === ResultEnum.SUCCESS) {
    ElMessage.success("上传成功");
    valFileList.value.push({
      name: file.name,
      url: response.data.url,
    });
    emit("update:modelValue", valFileList.value);
    return;
  } else {
    ElMessage.error(response.msg || "上传失败");
  }
};

const handleError = (error: any) => {
  showUploadPercent.value = false;
  uploadPercent.value = 0;
  ElMessage.error("上传失败");
};

const customColorMethod = (percentage: number) => {
  if (percentage < 30) {
    return "#909399";
  }
  if (percentage < 70) {
    return "#375ee8";
  }
  return "#67c23a";
};

const handleProgress = (event: UploadProgressEvent) => {
  uploadPercent.value = event.percent;
};

/**
 * 删除文件
 */
function handleRemove(removeFile: UploadUserFile) {
  const filePath = removeFile.url;
  if (filePath) {
    FileAPI.deleteByPath(0,filePath).then(() => {
      // 删除成功回调
      valFileList.value = valFileList.value.filter((file) => file.url !== filePath);
      emit("update:modelValue", valFileList.value);
    });
  }
}

/**
 * 下载文件
 */
function downloadFile(file: UploadUserFile) {
  const filePath = file.url;
  if (filePath) {
    FileAPI.downloadFile(filePath, file.name);
  }
}

// 添加新的工具函数
const formatFileSize = (size?: number) => {
  if (!size) return '未知';
  const units = ['B', 'KB', 'MB', 'GB'];
  let index = 0;
  let fileSize = size;

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }

  return `${fileSize.toFixed(2)} ${units[index]}`;
};

const formatDate = (timestamp?: number) => {
  if (!timestamp) return '未知';
  const date = new Date(timestamp);
  return date.toLocaleString();
};
</script>

<style lang="scss" scoped>
.file-upload-container {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 10px;
  }

  .header-upload {
    :deep(.el-upload) {
      display: inline-block;
    }
  }

  .file-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .file-name {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .operation-cell {
    display: flex;
    justify-content: center;
    gap: 12px;
  }

  .table-wrapper {
    position: relative;
    width: 100%;
  }

  .upload-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(2px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .upload-progress-content {
    text-align: center;
    padding: 20px;
    border-radius: 8px;
    background: white;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .upload-icon {
      font-size: 48px;
      color: var(--el-color-primary);
      margin-bottom: 16px;
      animation: bounce 1s infinite;
    }

    .progress-info {
      width: 300px;

      .progress-text {
        margin-bottom: 12px;
        color: var(--el-text-color-primary);
        font-size: 14px;
      }

      :deep(.el-progress-bar) {
        .el-progress-bar__outer {
          border-radius: 4px;
        }
      }
    }
  }
}

@keyframes bounce {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}
</style>