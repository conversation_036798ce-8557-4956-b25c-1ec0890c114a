<!-- cursor测试:1 -->
<template>
  <div>
    <Title name="盘点任务信息">
      <div v-if="editable">
        <el-button type="primary" @click="handleSave()">保存</el-button>
        <el-button type="danger" @click="handleSubmit()">提交</el-button>
      </div>
    </Title>
    <el-form
      ref="dataFormRef"
      :model="formData"
      :rules="rules"
      label-width="auto"
      :inline="true"
      :disabled="!editable"
    >
      <el-form-item label="资产编号" prop="zcbh">
        <el-input v-model="formData.zcbh" :disabled="true" placeholder="资产编号" />
      </el-form-item>
      <el-form-item label="任务名称" prop="zcmc">
        <el-input v-model="formData.zcmc" placeholder="资产名称" :disabled="true" />
      </el-form-item>
      <el-form-item label="规格型号" prop="ggxh">
        <el-input v-model="formData.ggxh" placeholder="资产名称" :disabled="true" />
      </el-form-item>
      <el-form-item label="数量" prop="sl">
        <el-input v-model="formData.sl" placeholder="数量" :disabled="true" />
      </el-form-item>
      <el-form-item label="保管员" prop="syr">
        <DDLUserList
          :key="formData.sybm"
          v-model="formData.syr"
          :dcode="formData.sybm"
          :disabled="true"
        />
      </el-form-item>
      <br />
      <el-form-item label="存放地点" prop="cfdd">
        <el-cascader
          v-model="formData.cfdd"
          class="cascader"
          style="width: 100%"
          :options="locationOptions"
          :props="cascaderProps"
          :disabled="!props.editable"
          :placeholder="formData.cfddname || '请选择存放地点'"
          filterable
          clearable
        />
      </el-form-item>
      <el-form-item label="盘点结果" prop="qcjg">
        <DDLXcode v-model="formData.qcjg" xcode="020232" />
      </el-form-item>
      <br />
      <el-form-item label="盘点情况" prop="qcqk" style="width: 800px">
        <el-input
          v-model="formData.qcqk"
          type="textarea"
          maxlength="200"
          show-word-limit
          :rows="5"
        />
      </el-form-item>
      <br />
      <el-form-item label="备注" prop="notes" style="width: 800px">
        <el-input
          v-model="formData.notes"
          type="textarea"
          maxlength="200"
          show-word-limit
          :rows="5"
        />
      </el-form-item>
    </el-form>
    <!-- 查看资产弹窗 -->
    <el-drawer v-model="itemVisible" append-to-body size="70%">
      <CardPanel :key="itemGuid" :guid="itemGuid" :rkguid="itemRkGuid" />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "taskEdit",
});
import { ElLoading } from "element-plus";
import { useUserStore } from "@/store";
import taskAPI, { taskForm } from "@/api/properties/task";
import SysAcodeApi, { SysAcodePageQuery } from "@/api/system/sys-acode";
import type { CascaderProps, CascaderOption } from "element-plus";
const locationOptions = ref<CascaderOption[]>([]);

const cascaderProps = reactive<CascaderProps>({
  lazy: true,
  emitPath: false,
  lazyLoad(node, resolve) {
    const params = reactive<SysAcodePageQuery>({
      preCode: "0101",
      pageNum: 1,
      pageSize: 100,
    });

    const { value } = node;
    //第一次加载判断是否是根节点
    if (node.root) {
      params.preCode = "0101";
    } else {
      params.preCode = value as string;
    }

    SysAcodeApi.getAcodelist(params).then((res) => {
      const nodes: CascaderOption[] = res.list.map((item: any) => ({
        value: item.xcode,
        label: item.name,
        leaf: item.xcode.length === 12,
      }));
      resolve(nodes);
    });
  },
});
//————————————————————————————————————————————暴露的方法,和请求参数
//组件参数
const props = defineProps({
  id: {
    type: Number,
  },
  guid: {
    type: String,
    required: true,
  },
  //保存成功后需要关闭抽屉并刷新数据
  RefreshFatherDrawer: {
    type: Function,
    required: true,
  },
  //是否可编辑
  editable: {
    type: Boolean,
  },
  dcbm: {
    type: String,
    required: true,
  },
});
//——————————————————————————————————————————————————form查询相关

const dcbmDisabled = ref(false);
const netcode = ref("");

//初始化一些用户的信息
const { nickname, dcode } = useUserStore().userInfo;
//表单
const formData = reactive<taskForm>({
  guid: "",
});
const dataFormRef = ref<InstanceType<typeof ElForm>>();
const rules = reactive({
  qcjg: [{ required: true, message: "盘点结果不能为空", trigger: "blur" }],
});
/** 初始化任务信息 */
function handleFormQuery(guid?: string) {
  if (guid) {
    taskAPI
      .getFormData(guid)
      .then((data) => {
        Object.assign(formData, data);
      })
      .catch((error) => {
        console.error("获取任务数据失败:", error);
        ElMessage.error("获取任务数据失败");
      });
  } else {
    // 进去前给个新的GUID
    formData.guid = props.guid;
  }
}

//——————————————————————————————————————————————————form操作相关
const handleSave = async () => {
  await dataFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const submitData = { ...formData };
      const id = formData.id;
      const apiCall = id ? taskAPI.update(id, submitData) : taskAPI.addtask(submitData);
      await apiCall
        .then(async (res) => {
          ElMessage.success("盘点任务保存成功");
          //保存成功后，重新用id去查询一次数据
          if (!id) {
            handleFormQuery(formData.guid);
          }
          props.RefreshFatherDrawer();
        })
        .catch((error) => {
          ElMessage.error("操作失败，失败原因：" + error.message);
        })
        .finally(() => {});
    }
  });
};
/** 提交任务表单 */
const handleSubmit = async () => {
  ElMessageBox.confirm("确定提交吗?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    //第一层confirm
    .then(async () => {
      await handleSave().then(async () => {
        //第二层保存完成
        const submitData = { ...formData };
        const dialogloading = ElLoading.service({
          lock: true,
          text: "处理中",
        });
        await taskAPI
          .submit(submitData.guid || "")
          .then(() => {
            //第三层提交完成
            ElMessage.success("盘点任务提交成功");
            props.RefreshFatherDrawer(true);
          })
          .finally(() => {
            dialogloading.close();
          })
          .catch((error) => {
            ElMessage.error(error.message);
          });
      });
    })
    .catch(() => {});
};

//——————————————————————————————————————————————————list查询相关

const loading = ref(false);

const total = ref(0);

//—————————————————————————————————————————————资产选择弹窗
const keyId = ref(0);
// 弹窗显隐
const choosedialog = reactive({
  title: "资产任务明细",
  visible: false,
});
const handleAddtaskPorperties = async () => {
  await dataFormRef.value?.validate();
  const api = handleSave();
  await api.then(() => {
    choosedialog.visible = true;
  });
};

//—————————————————————————————————————————————资产查看弹窗
const itemVisible = ref(false);
const itemGuid = ref("");
const itemRkGuid = ref("");
const handleRowView = (guid: string, rkguid: string) => {
  itemGuid.value = guid;
  itemRkGuid.value = rkguid;
  itemVisible.value = true;
};

onMounted(() => {
  if (props.id) {
    handleFormQuery(props.guid);
  } else {
    formData.guid = props.guid;
  }
});
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}

/* 自定义对话框样式 */
.custom-dialog {
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  background-color: #ffffff;
  .el-dialog__header {
    background-color: #409eff;
    color: #ffffff;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    .el-dialog__title {
      color: #ffffff;
    }
  }
  .el-dialog__body {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.9);
  }
}
</style>
