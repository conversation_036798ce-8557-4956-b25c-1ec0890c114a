<!-- 入库单 -->
<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item v-if="check || balance || view" prop="rkdh" label="入库单号">
            <el-input v-model="queryParams.rkdh" />
          </el-form-item>
          <el-form-item v-if="add || check || balance || view" prop="b" label="资产名称">
            <el-input v-model="queryParams.zcmc" />
          </el-form-item>
          <el-form-item prop="djsj" label="登记时间">
            <el-date-picker
              v-model="queryParams.djsj"
              :editable="false"
              class="filter-item"
              type="daterange"
              range-separator="~"
              start-placeholder="开始时间"
              end-placeholder="截止时间"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item v-if="check || view" prop="ucode" label="登记人">
            <el-input v-model="queryParams.ucode" />
          </el-form-item>
          <el-form-item v-if="check || view" prop="djbm" label="登记部门">
            <!-- 下拉框 -->
            <el-tree-select
              v-model="queryParams.djbm"
              placeholder="请选择申购部门"
              :data="deptOptions"
              filterable
              check-strictly
              :render-after-expand="false"
            />
          </el-form-item>
          <el-form-item v-if="view" prop="f" label="状态">
            <!-- 下拉框 -->
            <el-input v-model="queryParams.f" />
          </el-form-item>
          <el-form-item v-if="balance" prop="g" label="凭证号">
            <el-input v-model="queryParams.g" />
          </el-form-item>
          <el-form-item v-if="balance" prop="h" label="登记方式">
            <!-- 下拉框 -->
            <el-input v-model="queryParams.h" />
          </el-form-item>

          <el-form-item v-if="balance" prop="i" label="对账月份">
            <el-date-picker
              v-model="queryParams.i"
              :editable="false"
              type="month"
              value-format="YYYY-MM"
              placeholder="请选择对账月份"
            />
          </el-form-item>

          <el-form-item v-if="balance" prop="j" label="凭证日期">
            <el-date-picker
              v-model="queryParams.j"
              :editable="false"
              class="filter-item"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="截止日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button icon="refresh" @click="handleResetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="入库单列表">
        <div>
          <el-button v-if="add" plain type="danger" icon="plus" @click="handleOpenCatalogDialog">
            新增
          </el-button>
          <el-button
            v-if="add"
            plain
            type="danger"
            icon="delete"
            :disabled="removeIds.length === 0"
            @click="handleDelete()"
          >
            删除
          </el-button>
          <el-button
            v-if="add"
            plain
            type="danger"
            icon="CopyDocument"
            :disabled="removeIds.length === 0"
            @click="handleCopy"
          >
            复制
          </el-button>
          <el-button v-if="add" plain type="danger" icon="Top" @click="handleForward">
            提交
          </el-button>
          <el-button v-if="add" plain type="primary" icon="Bottom" @click="handleExcelImport">
            Excel模板导入
          </el-button>
          <el-button v-if="add" plain type="primary" icon="Bottom" @click="handlePurchaseImport">
            采购导入
          </el-button>

          <el-button v-if="check" plain type="danger" icon="Check" @click="handleCheckForward">
            批量审核通过
          </el-button>
          <el-button v-if="check" plain type="danger" icon="Close" @click="handleCheckAfter">
            批量退回
          </el-button>

          <el-button
            v-if="balance"
            plain
            type="danger"
            icon="Download"
            @click="handleGetVoucher"
            :disabled="true"
          >
            获取凭证号
          </el-button>
          <el-button
            v-if="balance"
            plain
            type="danger"
            icon="Edit"
            @click="handleEditVoucher"
            :disabled="removeIds.length === 0"
          >
            修改会计凭证
          </el-button>
          <el-button
            v-if="balance"
            plain
            type="danger"
            icon="DocumentChecked"
            @click="handleCheckAccount"
            :disabled="true"
          >
            财务对账
          </el-button>
          <el-button
            v-if="balance"
            plain
            type="primary"
            icon="Document"
            @click="handleViewAccount"
            :disabled="true"
          >
            对账查看
          </el-button>
          <el-button v-if="view" plain type="primary" icon="Document" @click="handlePrintStore">
            批量打印入库单
          </el-button>
          <el-button
            v-if="view"
            plain
            type="primary"
            icon="Document"
            @click="handleMergePrintStore"
          >
            合并打印入库单
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        v-if="!balance"
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" align="center" fixed />
        <el-table-column label="序号" type="index" width="60" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="入库单号" prop="zcrkdh" width="150" fixed />
        <el-table-column label="资产名称" prop="zcmc" width="200" fixed />
        <el-table-column label="数量" prop="sl" width="80" />
        <el-table-column label="原值" prop="je" min-width="100" />
        <!-- <el-table-column label="发票号码？" prop="e" width="150" /> -->
        <el-table-column label="登记人" prop="djrname" width="100" />
        <el-table-column label="登记部门" prop="djbmname" width="200" />
        <el-table-column label="登记方式" prop="zcdjfsname" width="200" />
        <el-table-column label="登记时间" prop="djsj" width="200" />
        <el-table-column label="状态" prop="netcodename" width="130" fixed="right" />
        <!-- ********************** 操作列 **********************
         按钮：查询View；编辑Edit；删除Delete -->
        <el-table-column label="操作" fixed="right" width="275">
          <template #default="scope">
            <el-button
              v-if="add"
              type="primary"
              icon="Edit"
              size="small"
              @click="
                dialog.title = '入库编辑';
                handleOpenDialog(true, scope.row, scope.row.czcode);
              "
            >
              编辑
            </el-button>
            <el-button
              v-if="add"
              type="danger"
              icon="Delete"
              size="small"
              @click="handleDelete(scope.row.guid)"
            >
              删除
            </el-button>
            <el-button
              v-if="check || balance"
              type="primary"
              icon="DocumentChecked"
              size="small"
              @click="
                dialog.title = '入库审核';
                handleOpenDialog(false, scope.row, scope.row.czcode);
              "
            >
              审核
            </el-button>
            <el-button
              v-if="balance || view"
              type="success"
              size="small"
              icon="Document"
              @click="
                dialog.title = '入库查询';
                handleOpenDialog(false, scope.row, scope.row.czcode);
              "
            >
              查看
            </el-button>
            <el-button
              v-if="view && scope.row.isCallBack == '1'"
              type="danger"
              icon="Remove"
              size="small"
              @click="handleCallBack(scope.row.guid)"
            >
              收回
            </el-button>
            <el-button
              v-if="view"
              type="primary"
              icon="Document"
              size="small"
              @click="hancleRowPrint"
            >
              打印标签
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-table
        v-if="balance"
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" align="center" fixed />
        <el-table-column label="会计凭证号" prop="kjpzh" width="200" fixed />
        <el-table-column label="会计凭证时间" prop="kjpzsj" width="200" fixed />
        <el-table-column label="入库单号" prop="zcrkdh" width="150" />
        <el-table-column label="资产名称" prop="zcmc" width="200" />
        <el-table-column label="数量" prop="sl" width="100" />
        <el-table-column label="单价(元)" prop="dj" width="100" />
        <el-table-column label="原值(元)" prop="je" width="100" />
        <el-table-column label="供应商" prop="gys" width="200" />
        <el-table-column label="登记方式" prop="zcdjfsname" width="100" />
        <el-table-column label="登记时间" prop="djsj" width="200" />
        <!-- ********************** 操作列 **********************
         按钮：查询View；编辑Edit；删除Delete -->
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="scope">
            <el-button
              v-if="add"
              type="primary"
              icon="Edit"
              size="small"
              @click="
                dialog.title = '入库编辑';
                handleOpenDialog(true, scope.row, scope.row.czcode);
              "
            >
              编辑
            </el-button>
            <el-button
              v-if="add"
              type="danger"
              icon="Delete"
              size="small"
              @click="handleDelete(scope.row.guid)"
            >
              删除
            </el-button>
            <el-button
              v-if="check || balance"
              type="primary"
              icon="DocumentChecked"
              size="small"
              @click="
                dialog.title = '入库审核';
                handleOpenDialog(false, scope.row, scope.row.czcode);
              "
            >
              审核
            </el-button>
            <el-button
              v-if="view"
              type="success"
              size="small"
              icon="Document"
              @click="
                dialog.title = '入库查询';
                handleOpenDialog(false, scope.row, scope.row.czcode);
              "
            >
              查看
            </el-button>
            <el-button
              v-if="view"
              type="primary"
              icon="Document"
              size="small"
              @click="hancleRowPrint"
            >
              打印标签
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 10"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <!-- 入库单的弹窗 -->
    <el-drawer v-model="dialog.storeVisible" :title="dialog.title" append-to-body size="85%">
      <!-- 设置V-if是为了关闭弹窗的时候销毁组件 -->
      <el-card>
        <StoreInfo
          :id="itemId"
          :key="itemId"
          :guid="itemGuid"
          :editable="itemEditable"
          :zclxbh="itemZclxbh"
          :netcode="itemNetcode"
          :dcbm="itemDcbm"
          :checkstatus="queryParams.checkstatus"
          :czcode="itemCzcode"
          :RefreshFatherDrawer="handleCloseDialog"
        />
      </el-card>
    </el-drawer>

    <!-- 查看对账 -->
    <el-dialog v-model="dialog.accountVisible" :title="dialog.title" width="60%" />

    <!-- 财政分类选择 -->
    <el-dialog v-model="dialog.catalogVisible" :title="dialog.title" width="60%">
      <TreeCzJyCode type="CZ" @node-click="handleCzcodeNodeClick" />
    </el-dialog>

    <!-- 数据导入 -->
    <DataImport v-model="importDialogVisible" @import-success="handleQuery()" />

    <!-- 会计凭证登记弹窗 -->
    <el-dialog v-model="dialog.kjpzVisible" title="批量修改会计凭证" width="40%">
      <el-form ref="kjpzFormRef" label-width="120px" :model="kjpzForm">
        <el-form-item label="会计凭证号" prop="kjpzh">
          <el-input v-model="kjpzForm.kjpzh" placeholder="请输入会计凭证号" />
        </el-form-item>
        <el-form-item label="会计凭证时间" prop="kjpzsj">
          <el-date-picker v-model="kjpzForm.kjpzsj" type="date" placeholder="选择日期" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialog.kjpzVisible = false">取消</el-button>
        <el-button type="primary" @click="handleKjpzSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import storeAPI from "@/api/properties/store";
import { useRoute } from "vue-router";
// import StoreInfo from "@/views/properties/store/components/storeEdit.vue";
import StoreInfo from "@/views/properties/store/components/index.vue";
import TreeCzJyCode from "@/components/Properties/TreeCzJyCode/index.vue";
import { getGuid } from "@/utils/guid";
import { czcodes, fetchCzCodes } from "@/assets/CZCode";
import { jycodes, fetchJyCodes } from "@/assets/JYCode";
import DeptAPI from "@/api/system/dept";
// 获取路由参数,view/add/balance/check
const route = useRoute();
const type = ref(route.query.type?.toString() || "view");
//显示隐藏权限,用于v-if
const add = type.value == "add";
const view = type.value == "view";
const balance = type.value == "balance";
const check = type.value == "check";
//页面状态保持
const queryFormRef = ref(ElForm);
const loading = ref(false);
const removeIds = ref<string[]>([]);
const total = ref(0);
const deptOptions = ref<OptionType[]>();
const queryParams = reactive<any>({
  pageNum: 1,
  pageSize: 10,
  zcmc: "", //	资产名称	query	false
  zclxbh: "", //	资产类型编号	query	false
  zcrkdh: "", //	资产入库单号	query	false
  djbm: "", //	登记部门	query	false
  djsj: "", //	登记时间	query	false
  ucode: "", //	登记人	query	false
  //type: type.value == "balance" ? "check" : type.value, //	view:查看 edit:暂存	query	false
  netcode: "", //	数据编码	query	false
  sjzt: "", //
  type: type.value,
  checkstatus: 0, //用于查询已审和待审
});
// 列表数据
const pageData = ref<any[]>([]);
// 弹窗
const dialog = reactive({
  title: "",
  storeVisible: false,
  accountVisible: false,
  catalogVisible: false, // 采购目录选择弹窗
  kjpzVisible: false,
});

const kjpzForm = reactive<any>({
  kjpzsj: new Date().toISOString().slice(0, 19).replace("T", " "), //验收日期
});
// 导入弹窗显示状态
const importDialogVisible = ref(false);
//主查询方法
function handleQuery() {
  loading.value = true;
  console.log("params", queryParams);
  storeAPI
    .getPage(queryParams)
    .then((res: any) => {
      pageData.value = res.list;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
//重置查询
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.pageNum = 1;
  //handleQuery();
}

// 选中项发生变化
function handleSelectionChange(selection: any[]) {
  removeIds.value = selection.map((item: any) => item.guid);
}

/** 删除入库单 */
function handleDelete(guid?: number) {
  const guids = [guid || removeIds.value].join(",");
  if (!guids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      storeAPI.deleteByIds(guids).then(() => {
        ElMessage.success("删除成功");
        handleQuery();
      });
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

//复制
const handleCopy = () => {
  const guids = removeIds.value.join(",");
  if (!guids) {
    ElMessage.warning("请勾选复制项");
    return;
  }

  ElMessageBox.confirm("确认复制已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      storeAPI.copyByIds(guids).then(() => {
        ElMessage.success("复制成功");
        handleQuery();
      });
    },
    () => {
      ElMessage.info("已取消复制");
    }
  );
};
//入库单提交
const handleForward = () => {
  //功能未开发
  ElMessage.error("功能未开发");
};
//Excel导入
const handleExcelImport = () => {
  importDialogVisible.value = true;
};
//采购导入
const handlePurchaseImport = () => {};
//批量审核
const handleCheckForward = () => {};
//批量退回
const handleCheckAfter = () => {};
//获取凭证号
const handleGetVoucher = () => {};
//编辑凭证号
const handleEditVoucher = () => {
  const guids = removeIds.value.join(",");
  if (!guids) {
    ElMessage.warning("请勾选编辑项");
    return;
  }

  dialog.kjpzVisible = true;
};

const handleKjpzSubmit = () => {
  const guids = removeIds.value.join(",");
  if (!guids) {
    ElMessage.warning("请勾选编辑项");
    return;
  }

  ElMessageBox.confirm("确认修改已选中的数据项?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      storeAPI.updateKjpzhByIds(guids, kjpzForm).then(() => {
        ElMessage.success("修改成功");
        dialog.kjpzVisible = false;
        kjpzForm.kjpzh = "";
        handleQuery();
      });
    },
    () => {
      ElMessage.info("已取消修改");
    }
  );
};
//对账
const handleCheckAccount = () => {};
//打印入库单
const handlePrintStore = () => {};
//合并打印入库单
const handleMergePrintStore = () => {};
//单行审核
const hancleRowCheck = () => {};
//单行查看
const hancleRowView = () => {
  //打开弹窗，设置组件的props，设置弹窗标题
  // pageData.dv.storeInfo = true;
  // pageData.dv.title = "入库单查看";
  // pageData.storeType = "view";
};
//单行打印
const hancleRowPrint = () => {};

//————————————————————————————————————————————弹窗相关
/** 打开入库弹窗 */
const itemGuid = ref("");
const itemId = ref();
const itemZclxbh = ref();
const itemCzcode = ref("");
const itemDcbm = ref<string>("");
const itemEditable = ref<boolean>(false);
const itemNetcode = ref<string>("");
// 新增
// 打开采购目录选择对话框
const handleOpenCatalogDialog = () => {
  dialog.catalogVisible = true;
  dialog.title = "选择财政分类";
};

// 处理采购目录选择
const handleCzcodeNodeClick = (treeNodeValue: string) => {
  console.log("treeNodeValue", treeNodeValue);
  if (!treeNodeValue) {
    ElMessage.warning("请选择财政分类");
    return;
  }
  dialog.title = "新增入库";
  dialog.catalogVisible = false;
  handleOpenDialog(true, undefined, treeNodeValue);
};
const handleOpenDialog = (editable: boolean, row?: any, czcode?: string) => {
  itemDcbm.value = row?.dcbm || "";
  itemNetcode.value = row?.netcode || "";
  itemEditable.value = editable;
  //打开弹窗，如果没有guid则生成一个GUID
  itemGuid.value = row?.guid || getGuid();
  itemId.value = row?.id;
  itemZclxbh.value = row?.zclxbh;
  itemCzcode.value = czcode || "";
  dialog.storeVisible = true;
};

/** 关闭弹窗 */
function handleCloseDialog(close?: boolean) {
  if (close) {
    dialog.storeVisible = false;
  }
  handleQuery();
}
//查看对账
const handleViewAccount = () => {
  // pageData.dv.viewAccout = true; //显示弹窗
  // pageData.dv.title = "对账查看";
};

//单行编辑
const hancleRowEdit = () => {
  //打开弹窗，设置组件的props，设置弹窗标题
  // pageData.dv.storeInfo = true;
  // pageData.dv.title = "入库单编辑";
  // pageData.storeType = "edit";
};

const handleCallBack = (guid: string) => {
  ElMessageBox.confirm("确认收回吗?收回后可再次提交该单据", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      loading.value = true;
      storeAPI
        .callback(guid)
        .then(() => {
          ElMessage.success("操作成功");
          handleQuery();
        })
        .catch((error) => {
          ElMessage.error("操作失败，失败原因：" + error.message);
        })
        .finally(() => (loading.value = false));
    })
    .catch(() => {
      ElMessage.info("已取消收回");
    });
};

//————————————————————————生命周期
onMounted(() => {
  // 加载部门选项
  DeptAPI.getOptionsCode().then((res) => {
    deptOptions.value = res;
    console.log("部门选项加载完成:", deptOptions.value.length);
  });
  handleQuery();
});
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}
</style>
