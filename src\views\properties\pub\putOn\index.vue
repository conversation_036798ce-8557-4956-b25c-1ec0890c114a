<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item label="资产编号：" prop="zcbh">
            <el-input v-model="queryParams.zcbh" placeholder="资产编号" />
          </el-form-item>
          <el-form-item label="资产名称：" prop="zcmc">
            <el-input v-model="queryParams.zcmc" placeholder="资产名称" />
          </el-form-item>
          <el-form-item label="资产状态：">
            <el-select v-model="queryParams.zczt" placeholder="请选择资产状态" clearable>
              <el-option label="领用" value="1" />
              <el-option label="借用" value="2" />
              <el-option label="调拨" value="3" />
              <el-option label="调剂" value="4" />
              <el-option label="处置" value="5" />
            </el-select>
          </el-form-item>
          <el-form-item label="上架状态：">
            <el-select v-model="queryParams.zcxz" placeholder="请选择上架状态" clearable>
              <el-option label="上架" value="1" />
              <el-option label="下架" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon><Search /></template>
              搜索
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="公共资产列表">
        <div>
          <el-button
            :disabled="selectedGuids.length === 0"
            type="danger"
            plain
            @click="handleOpenDialog"
          >
            <template #icon><Top /></template>
            上架
          </el-button>
          <el-button
            :disabled="selectedGuids.length === 0"
            type="danger"
            plain
            @click="handleToggle(0)"
          >
            <template #icon><Bottom /></template>
            下架
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        stripe
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed />
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="资产编号" prop="zcbh" width="130" align="center" fixed />
        <el-table-column label="资产名称" prop="zcmc" width="300" align="center" fixed />
        <el-table-column label="规格型号" prop="ggxh" align="center" />
        <el-table-column label="数量" prop="sl" width="60" align="center" />
        <el-table-column label="原值(元)" prop="je" width="150" align="center" />
        <el-table-column label="使用部门" prop="sybmname" width="200" align="center" />
        <el-table-column label="资产状态" prop="locName" width="100" align="center" />
        <el-table-column label="上架状态" prop="zcxzName" width="100" align="center" />
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :page-size="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <el-dialog v-model="dialogVisible" width="40%">
      <el-form style="margin: 30px">
        <el-form-item label="请选择上架方式：">
          <el-radio-group v-model="putOnParams.type">
            <el-radio value="jy" size="large" border>借用</el-radio>
            <el-radio value="ly" size="large" border>领用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleToggle(1)">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "dispose",
  inheritAttrs: false,
});
import pubAPI, { pubPutOnQuery } from "@/api/properties/pub";

//页面参数
const queryFormRef = ref(ElForm);

const loading = ref(false);
const selectedGuids = ref<number[]>([]);
const total = ref(0);

// 处置表格数据
const pageData = ref<pubPutOnQuery[]>([]);

const queryParams = reactive<pubPutOnQuery>({
  pageNum: 1,
  pageSize: 30,
  zczt: "",
  zcxz: "",
});
const handleQuery = () => {
  loading.value = true;
  console.log(queryParams);
  pubAPI
    .getPutOnPage(queryParams)
    .then((res) => {
      pageData.value = res.list;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
};

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  selectedGuids.value = selection.map((item: any) => item.guid);
}

const handleToggle = (type: number) => {
  const guids = selectedGuids.value.join(",");
  var name = "";
  if (type == 0) {
    name = "下架";
  } else {
    name = "上架";
  }
  ElMessageBox.confirm("确定要" + name + "吗？", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      if (type) {
        //上架
        if (putOnParams.type == "") {
          ElMessage.error("请选择上架类型！");
          return;
        } else {
          pubAPI
            .putOn(putOnParams, guids)
            .then(() => {
              ElMessage.success("操作成功！");
              handleQuery();
              dialogVisible.value = false;
            })
            .catch(() => {})
            .finally(() => {});
        }
      } else {
        //下架
        pubAPI
          .putDown(guids)
          .then(() => {
            ElMessage.success("操作成功！");
            handleQuery();
          })
          .catch(() => {})
          .finally(() => {});
      }
    })
    .catch(() => {});
};

const dialogVisible = ref(false);
const putOnParams = reactive<any>({
  type: "",
});
const handleOpenDialog = () => {
  dialogVisible.value = true;
};
onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.el-form-item {
  .el-input,
  .el-cascader,
  .el-select,
  .el-autocomplete {
    width: 180px;
  }
}
</style>
