<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item label="银行名称" prop="branchname">
            <el-input
              v-model="queryParams.branchname"
              placeholder="银行名称"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon><Search /></template>
              搜索
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon><Refresh /></template>
              重置
            </el-button>
            <el-button @click="handleSelect">
              <template #icon><Pointer /></template>
              确定
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="列表" />
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @current-change="handleCurrentChange"
      >
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          key="branchname"
          label="银行名称"
          prop="branchname"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="branchcode"
          label="联行号"
          prop="branchcode"
          min-width="150"
          align="center"
        />
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store";
import SysBranchinfoAPI, {
  SysBranchinfoPageVO,
  SysBranchinfoForm,
  SysBranchinfoPageQuery,
} from "@/api/cg/sys-branchinfo";

//页面参数
const queryFormRef = ref(ElForm);
const loading = ref(false);
const lhh = ref("");
const total = ref(0);

const queryParams = reactive<SysBranchinfoPageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// sysbranch表格数据
const pageData = ref<SysBranchinfoPageVO[]>([]);

/** 查询sysbranch */
function handleQuery() {
  loading.value = true;
  SysBranchinfoAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置sysbranch查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleCurrentChange(row: SysBranchinfoPageVO) {
  lhh.value = row.branchcode as string;
}
const emits = defineEmits(["getLhh"]);

const handleSelect = () => {
  emits("getLhh", lhh.value);
};
onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped></style>
