<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-card class="search-card" :body-style="{ padding: '24px' }">
      <el-form :model="searchForm" inline class="search-form">
        <div class="form-item-group">
          <el-form-item label="订单号">
            <el-input v-model="searchForm.orderNo" placeholder="请输入订单号" clearable />
          </el-form-item>
          <el-form-item label="景点名称">
            <el-input v-model="searchForm.ticketName" placeholder="请输入景点名称" clearable />
          </el-form-item>
          <el-form-item label="订单状态">
            <el-select v-model="searchForm.status" placeholder="请选择订单状态" clearable>
              <el-option
                v-for="item in ticketOrderStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="form-item-group">
          <el-form-item label="游玩日期" class="date-range-item">
            <el-date-picker
              v-model="searchForm.visitDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item class="search-buttons">
            <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="resetForm">重置</el-button>
            <el-button type="success" :icon="Download" @click="handleExport">导出</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-card>

    <!-- 列表视图 -->
    <el-card class="table-card" :body-style="{ padding: '20px' }">
      <div class="table-container">
        <el-table :data="currentPageData" border style="width: 100%" :loading="loading">
          <el-table-column prop="orderNo" label="订单号" width="180" />
          <el-table-column prop="ticketName" label="景点名称" min-width="180" />
          <el-table-column prop="quantity" label="数量" width="80" />
          <el-table-column prop="totalAmount" label="总金额" width="120">
            <template #default="{ row }">¥{{ row.totalAmount.toFixed(2) }}</template>
          </el-table-column>
          <el-table-column prop="visitorName" label="游客姓名" width="120" />
          <el-table-column prop="visitorPhone" label="联系电话" width="140" />
          <el-table-column prop="visitDate" label="游玩日期" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getTicketOrderStatusType(row.status)">
                {{ getTicketOrderStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180" />
          <el-table-column label="操作" fixed="right" width="200">
            <template #default="{ row }">
              <el-button :icon="View" link type="primary" @click="handleView(row)">查看</el-button>
              <el-button
                v-if="row.status === TicketOrderStatus.Pending"
                link
                type="success"
                @click="handleConfirm(row)"
              >
                确认
              </el-button>
              <el-button
                v-if="row.status === TicketOrderStatus.Paid"
                link
                type="info"
                @click="handleComplete(row)"
              >
                完成
              </el-button>
              <el-button
                v-if="[TicketOrderStatus.Pending, TicketOrderStatus.Paid].includes(row.status)"
                link
                type="danger"
                @click="handleRefund(row)"
              >
                退款
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="page.current"
            v-model:page-size="page.size"
            :total="page.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 订单详情对话框 -->
    <el-dialog v-model="dialogVisible" title="订单详情" width="600px" :close-on-click-modal="false">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单号">{{ currentOrder.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="景点名称">{{ currentOrder.ticketName }}</el-descriptions-item>
        <el-descriptions-item label="购买数量">{{ currentOrder.quantity }}</el-descriptions-item>
        <el-descriptions-item label="总金额">
          ¥{{ currentOrder.totalAmount?.toFixed(2) }}
        </el-descriptions-item>
        <el-descriptions-item label="游客姓名">{{ currentOrder.visitorName }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">
          {{ currentOrder.visitorPhone }}
        </el-descriptions-item>
        <el-descriptions-item label="游玩日期">{{ currentOrder.visitDate }}</el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <el-tag :type="getTicketOrderStatusType(currentOrder.status!)">
            {{ getTicketOrderStatusText(currentOrder.status!) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentOrder.createTime }}</el-descriptions-item>
        <el-descriptions-item label="支付时间">
          {{ currentOrder.payTime || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="使用时间">
          {{ currentOrder.useTime || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="退款时间">
          {{ currentOrder.refundTime || "-" }}
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search, Refresh, Download, View } from "@element-plus/icons-vue";
import {
  TicketOrderStatus,
  TicketOrderInfo,
  ticketOrderStatusOptions,
  getTicketOrderStatusType,
  getTicketOrderStatusText,
  mockTicketOrders,
} from "@/views/tourism/data";

// 搜索表单
const searchForm = reactive({
  orderNo: "",
  ticketName: "",
  status: undefined as undefined | TicketOrderStatus,
  visitDateRange: [] as string[],
});

// 分页配置
const page = reactive({
  current: 1,
  size: 10,
  total: 0,
});

// 状态变量
const loading = ref(false);
const dialogVisible = ref(false);
const currentOrder = ref<Partial<TicketOrderInfo>>({});

// 订单数据
const orderList = ref<TicketOrderInfo[]>(mockTicketOrders);

// 计算当前页数据
const currentPageData = computed(() => {
  const filteredData = orderList.value.filter((order) => {
    if (searchForm.orderNo && !order.orderNo.includes(searchForm.orderNo)) {
      return false;
    }
    if (searchForm.ticketName && !order.ticketName.includes(searchForm.ticketName)) {
      return false;
    }
    if (searchForm.status !== undefined && order.status !== searchForm.status) {
      return false;
    }
    if (searchForm.visitDateRange && searchForm.visitDateRange.length === 2) {
      const [startDate, endDate] = searchForm.visitDateRange;
      const visitDate = order.visitDate;
      if (visitDate < startDate || visitDate > endDate) {
        return false;
      }
    }
    return true;
  });

  page.total = filteredData.length;
  const start = (page.current - 1) * page.size;
  const end = start + page.size;
  return filteredData.slice(start, end);
});

// 搜索
const handleSearch = () => {
  loading.value = true;
  page.current = 1;
  setTimeout(() => {
    loading.value = false;
  }, 300);
};

// 重置表单
const resetForm = () => {
  searchForm.orderNo = "";
  searchForm.ticketName = "";
  searchForm.status = undefined;
  searchForm.visitDateRange = [];
  page.current = 1;
  handleSearch();
};

// 导出订单
const handleExport = () => {
  const exportData = currentPageData.value.map((order) => ({
    订单号: order.orderNo,
    景点名称: order.ticketName,
    购买数量: order.quantity,
    总金额: order.totalAmount,
    游客姓名: order.visitorName,
    联系电话: order.visitorPhone,
    游玩日期: order.visitDate,
    订单状态: getTicketOrderStatusText(order.status),
    创建时间: order.createTime,
  }));

  // 这里添加导出逻辑，可以使用第三方库如 xlsx
  console.log("导出数据:", exportData);
  ElMessage.success("订单数据导出成功");
};

// 查看详情
const handleView = (row: TicketOrderInfo) => {
  currentOrder.value = row;
  dialogVisible.value = true;
};

// 确认订单
const handleConfirm = async (row: TicketOrderInfo) => {
  try {
    await ElMessageBox.confirm("确认该订单已支付？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    loading.value = true;
    row.status = TicketOrderStatus.Paid;
    row.payTime = new Date().toISOString();
    ElMessage.success("订单已确认");
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("操作失败");
    }
  } finally {
    loading.value = false;
  }
};

// 完成订单
const handleComplete = async (row: TicketOrderInfo) => {
  try {
    await ElMessageBox.confirm("确认该订单已使用？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    loading.value = true;
    row.status = TicketOrderStatus.Used;
    row.useTime = new Date().toISOString();
    ElMessage.success("订单已完成");
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("操作失败");
    }
  } finally {
    loading.value = false;
  }
};

// 退款
const handleRefund = async (row: TicketOrderInfo) => {
  try {
    await ElMessageBox.confirm("确认要对该订单进行退款操作？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    loading.value = true;
    row.status = TicketOrderStatus.Refunded;
    row.refundTime = new Date().toISOString();
    ElMessage.success("退款操作已完成");
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("操作失败");
    }
  } finally {
    loading.value = false;
  }
};

// 分页处理
const handleSizeChange = (val: number) => {
  page.size = val;
  page.current = 1;
};

const handleCurrentChange = (val: number) => {
  page.current = val;
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);

  .search-card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    background-color: #fff;

    .search-form {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .form-item-group {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: flex-start;

        @media screen and (max-width: 1400px) {
          flex-direction: column;
          gap: 12px;
        }
      }

      :deep(.el-form-item) {
        margin: 0;

        .el-input,
        .el-select {
          width: 240px;
        }

        &.date-range-item {
          .el-date-editor {
            width: 400px;
          }
        }

        &.search-buttons {
          margin-left: auto;
          display: flex;
          gap: 8px;

          @media screen and (max-width: 1400px) {
            margin-left: 0;
            width: 100%;
            display: flex;
            justify-content: flex-end;
          }

          .el-button {
            margin: 0;
            min-width: 88px;

            &:not(:first-child) {
              margin-left: 8px;
            }
          }
        }

        .el-form-item__label {
          font-weight: 500;
          color: #606266;
          padding-right: 12px;
        }
      }
    }
  }

  .table-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .table-container {
      :deep(.el-table) {
        border-radius: 8px;
        overflow: hidden;

        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: 600;
          height: 48px;
        }

        td {
          padding: 12px 0;
        }

        .el-table__row {
          transition: all 0.3s;

          &:hover {
            background-color: #f5f7fa;
          }
        }

        .el-tag {
          border-radius: 4px;
          padding: 2px 8px;

          &.el-tag--warning {
            background-color: #fdf6ec;
            border-color: #faecd8;
            color: #e6a23c;
          }

          &.el-tag--success {
            background-color: #f0f9eb;
            border-color: #e1f3d8;
            color: #67c23a;
          }

          &.el-tag--danger {
            background-color: #fef0f0;
            border-color: #fde2e2;
            color: #f56c6c;
          }

          &.el-tag--info {
            background-color: #f4f4f5;
            border-color: #e9e9eb;
            color: #909399;
          }
        }

        .operation-column {
          .el-button {
            padding: 4px 8px;

            &.el-button--primary {
              color: #409eff;
            }

            &.el-button--success {
              color: #67c23a;
            }

            &.el-button--danger {
              color: #f56c6c;
            }

            &.el-button--info {
              color: #909399;
            }
          }
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      padding: 10px 20px;
      text-align: right;
      background-color: #fff;
      border-top: 1px solid #f0f0f0;
    }
  }
}
</style>
