import request from "@/utils/request";

const CLEARANCE_BASE_URL = "/api/v1/clearance";

const clearAPI = {
  /** 获取待核销的处置单列表/api/v1/disposes/WaitClearance-page */
  getWaitClearPage(queryParams?: any) {
    return request<any, PageResult<any[]>>({
      url: `${CLEARANCE_BASE_URL}/wait-clearance/page`,
      method: "get",
      params: queryParams,
    });
  },

  /** 核销单分页列表 */
  getClearPage(queryParams?: any) {
    return request<any, PageResult<any[]>>({
      url: `${CLEARANCE_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },

  /** 多选处置单添加核销单*/
  addClear(data: any) {
    return request({
      url: `${CLEARANCE_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  },

  // 删除核销单/api/v1/disposes/{Clearanceguids}/Clearance
  deleteByIds(ids: string) {
    return request({
      url: `${CLEARANCE_BASE_URL}/delete/${ids}`,
      method: "get",
    });
  },
  // ---------------------新增---------------------
  //删除核销单中的处置单 /api/v1/disposes/{Disposeguids}/ClearanceDispose
  deleteDetailsByGuids(guids: string) {
    return request({
      url: `${CLEARANCE_BASE_URL}/clearance-dispose/delete/${guids}`,
      method: "get",
    });
  },

  //获取核销表单数据/api/v1/disposes/{guid}/form
  getFormData(guid: string) {
    return request<any, any>({
      url: `${CLEARANCE_BASE_URL}/${guid}/form`,
      method: "get",
    });
  },

  //！！！！核销受理时退回处置明细/api/v1/disposes/ClearanceDetails-return
  //勾选待核销的资产明细进行退回
  afterDisposeDetailPage(queryParams: { ClearanceDetailsids: string }) {
    return request<any, PageResult<any[]>>({
      url: `${CLEARANCE_BASE_URL}/clearance-details/return`,
      method: "post",
      params: queryParams,
    });
  },

  //核销明细 /api/v1/disposes/ClearanceDispose-page
  getClearDetailPage(queryParams?: any) {
    return request<any, PageResult<any[]>>({
      url: `${CLEARANCE_BASE_URL}/clearance-details/page`,
      method: "get",
      params: queryParams,
    });
  },

  //新增核销单的处置单 /api/v1/disposes/ClearanceDispose/post-form
  addClearDetail(data: { ClearanceGuid: string; Disposeguids: string }) {
    return request({
      url: `${CLEARANCE_BASE_URL}/clearance-dispose/add`,
      method: "post",
      data: data,
    });
  },

  //更新核销单 /api/v1/disposes/{id}/Clearance-update
  update(id: string, data: any) {
    return request({
      url: `${CLEARANCE_BASE_URL}/update/${id}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 提交处置/api/v1/disposes/submit
   *
   */
  submit(guid: string) {
    return request({
      url: `${CLEARANCE_BASE_URL}/submit/${guid}`,
      method: "post",
    });
  },

  // 核销资产分页列表
  getClearanceDetailsPage(queryParams?: any) {
    return request({
      url: `${CLEARANCE_BASE_URL}/clearance-card/page`,
      method: "get",
      params: queryParams,
    });
  },
};

export default clearAPI;
