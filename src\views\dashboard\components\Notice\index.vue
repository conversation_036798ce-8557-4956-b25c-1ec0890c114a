<template>
  <div>
    <el-card>
      <template #header>
        <div class="flex-x-between">
          <div class="flex-y-center">{{ props.name }}</div>
          <el-button type="primary" size="small" plain @click="listDialogVisible = true">
            更多
          </el-button>
        </div>
      </template>

      <ScrollbarLoop height="200" :datacount="pagedata.length">
        <el-table
          :data="pagedata"
          style="width: 100%"
          :show-header="false"
          @row-click="handleRowClick"
        >
          <el-table-column prop="title" label="标题" />
          <el-table-column prop="publishTime" label="日期" width="100" />
        </el-table>
      </ScrollbarLoop>
    </el-card>

    <!-- 【弹出框】公告列表 -->
    <el-dialog v-model="listDialogVisible" :title="props.name" width="80%">
      <el-table
        v-loading="loading"
        :data="pagedata"
        highlight-current-row
        :border="true"
        height="480px"
      >
        <el-table-column prop="title" label="标题" />
        <el-table-column prop="publishTime" label="日期" width="100" />
        <el-table-column label="操作" fixed="right" width="150">
          <template #default="scope">
            <el-button type="primary" size="small" @click="hancleGoToDo(scope.row.id)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 【弹出框】公告详情 -->
    <el-dialog v-model="infoDialogVisible" :title="props.name" width="60%" height="60%"></el-dialog>
  </div>
  <NoticeDetail ref="noticeDetailRef" />
</template>

<script setup lang="ts">
import NoticeAPI, { NoticePageVO } from "@/api/system/notice";

const listDialogVisible = ref(false);
const infoDialogVisible = ref(false);

const loading = ref(false);
const noticeDetailRef = ref();
//组件参数
const props = defineProps({
  name: {
    type: String,
    default: "通知公告",
  },
  //展示高度，默认200
  height: {
    type: String,
    default: "200",
  },
  // 内容筛选项，传Xcode分类。默认全部
  filter: {
    type: String,
    default: "",
  },
});

/*const pagedata = reactive({
  tableData: [
    { name: "11111111111111111", data: "2025-1-1" },
    { name: "22222222222222222", data: "2025-1-1" },
    { name: "33333333333333333", data: "2025-1-1" },
    { name: "44444444444444444", data: "2025-1-1" },
    { name: "55555555555555555", data: "2025-1-1" },
    { name: "66666666666666666", data: "2025-1-1" },
    { name: "77777777777777777", data: "2025-1-1" },
  ],
});*/

const pagedata = ref(<NoticePageVO[]>[]);

//通知公告

const hancleGoToDo = (id: any) => {
  console.log(id);
  noticeDetailRef.value.openNotice(id);
};

/*const handleRowClick = (row: any) => {
  console.log(row.name);
  infoDialogVisible.value = true;
 
};*/
// 阅读通知公告
const handleRowClick = (row: any) => {
  var id = row.id;
  noticeDetailRef.value.openNotice(id);
  /*const index = pagedata.value.findIndex((notice) => notice.id === id);
  if (index >= 0) {
    pagedata.value.splice(index, 1); // 从消息列表中移除已读消息
  }*/
};

onMounted(() => {
  NoticeAPI.getList(10).then((data) => {
    var len = data.length;
    for (var i = 0; i < len; i++) {
      data[i].publishTime = data[i].publishTime?.toString().substring(0, 10);
    }
    pagedata.value = data;
  });
});
</script>
<style lang="scss" scoped></style>
