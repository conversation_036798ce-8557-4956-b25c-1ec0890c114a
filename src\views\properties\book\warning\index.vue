<!-- cursor测试:1 -->
<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item v-if="view" label="资产编号" prop="zcbh">
            <el-input
              v-model="queryParams.zcbh"
              placeholder="资产编号"
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item v-if="view" label="资产名称" prop="zcmc">
            <el-input
              v-model="queryParams.zcmc"
              placeholder="资产名称"
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item v-if="view" label="使用人" prop="syr">
            <el-input
              v-model="queryParams.syr"
              placeholder="使用人"
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item v-if="view" label="原部门" prop="zcbm">
            <el-input
              v-model="queryParams.zcbm"
              placeholder="原部门"
              @keyup.enter="handleQuery()"
            />
          </el-form-item>     
          <el-form-item v-if="view" label="现部门" prop="sybm">
            <el-input
              v-model="queryParams.sybm"
              placeholder="现部门"
              @keyup.enter="handleQuery()"
            />
          </el-form-item>       
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon>
                <Search />
              </template>
              搜索
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon>
                <Refresh />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="资产预警列表">
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        stripe
        border>
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column key="zcbh" label="资产编号" prop="zcbh" min-width="120" align="center" />
        <el-table-column
          key="zcmc"
          label="资产名称"
          prop="zcmc"
          min-width="120"
          align="center"
        />
        <el-table-column
          key="syrname"
          label="使用人"
          prop="syrname"
          min-width="120"
          align="center"
        />
        <el-table-column
          key="zcbmname"
          label="原部门"
          prop="zcbmname"
          min-width="120"
          align="center"
        />
        <el-table-column key="ogly" label="原部门管理员" prop="ogly" min-width="120" align="center" />
        <el-table-column key="syrbmname" label="现部门" prop="syrbmname" min-width="120" align="center" />
        <el-table-column
          key="ngly"
          label="现部门管理员"
          prop="ngly"
          min-width="120"
          align="center"
        />
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 10"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 验收表单弹窗 -->
    <el-drawer v-model="dialog.visible" :title="dialog.title" append-to-body size="80%">
      <el-card>
        <!-- 
        id、guid常规参数
        key用于刷新组件
        editable用于区分是否可编辑
        dcbm调出部门特殊字段
        netcode新增编辑时为空,审核时需要 
        RefreshFatherDrawer关闭抽屉并刷新数据  -->
      </el-card>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import warningAPI, { warningPageVO, warningPageQuery } from "@/api/properties/warning";
//import TransferView from "./user/transfer/components/transfer-view.vue";
//————————————————————————————————————————————暴露的方法,和请求参数
function RefreshFatherDrawer(close?: boolean) {
  //是否关闭弹窗由参数决定，默认不关闭
  if (close) {
    dialog.visible = false;
  }
  // itemId.value = undefined;
  handleQuery();
}
// 获取路由参数,view/add/check
const route = useRoute();
const type = ref(route.query.type?.toString() || "view");
//显示隐藏权限,用于v-if
const add = type.value == "add";
const view = type.value == "view";
const check = type.value == "check";

//————————————————————————————————————————————查询相关
const queryFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);
const queryParams = reactive<warningPageQuery>({
  pageNum: 1,
  pageSize: 10,
});
// 变动表格数据
const pageData = ref<warningPageVO[]>([]);

/** 查询变动 */
function handleQuery() {
  loading.value = true;
  console.log("刷新列表数据");
  warningAPI
    .getPage(queryParams)
    .then((data) => {

      pageData.value = data.list;
      total.value = data.total;
      console.log("data");
      console.log(pageData.value);
    })
    .catch((error) => {
      console.error("获取变动数据失败:", error);
      ElMessage.error("获取变动数据失败");
      pageData.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置验收查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}
const handleChangeRadio = () => {
  handleResetQuery();
};


//————————————————————————————————————————————弹窗相关
// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});
/** 打开调拨弹窗 */
const itemGuid = ref<string>("");
const itemId = ref<number | undefined>();
const itemDcbm = ref<string>("");
const itemEditable = ref<boolean>(false);
const itemNetcode = ref<string>("");
function handleOpenDialog(editable: boolean, row?: warningPageVO) {
  console.log("type.value", type.value);
}

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}

.flex {
  display: flex;
  flex-direction: row;
}
</style>