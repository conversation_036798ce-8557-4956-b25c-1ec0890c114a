import dayjs from "dayjs"; // 引入日期处理工具

export const format = (date: string | Date, formatString: string) => {
  return dayjs(date).format(formatString);
};

// 辅助函数：格式化为 YYYY-MM-DD HH:mm:ss（本地时间）
export const formatLocalDateTime = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};
