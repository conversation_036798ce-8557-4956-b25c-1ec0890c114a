<template>
  <div class="smart-login-wrapper">
    <!-- 设备切换按钮（调试模式） -->
    <div v-if="showDebugSwitch" class="debug-switch">
      <button @click="toggleDevice" class="debug-btn">
        {{ isMobile ? '📱 移动端' : '💻 PC端' }} (点击切换)
      </button>
    </div>

    <!-- PC端登录页面 -->
    <PCLoginPage v-if="!isMobile" />
    
    <!-- 移动端登录页面 -->
    <MobileLoginPage v-else />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/store'
import { DeviceEnum } from '@/enums/DeviceEnum'

// 导入登录页面组件
import PCLoginPage from './index.vue'
import MobileLoginPage from '../mobile/login/index.vue'

const route = useRoute()
const appStore = useAppStore()

// 是否为移动端
const isMobile = computed(() => appStore.device === DeviceEnum.MOBILE)

// 是否显示调试切换按钮
const showDebugSwitch = computed(() => {
  return route.query.debug === 'true'
})

// 切换设备类型（仅调试模式）
const toggleDevice = () => {
  if (showDebugSwitch.value) {
    const newDevice = isMobile.value ? DeviceEnum.DESKTOP : DeviceEnum.MOBILE
    appStore.toggleDevice(newDevice)
  }
}
</script>

<style lang="scss" scoped>
.smart-login-wrapper {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.debug-switch {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;

  .debug-btn {
    padding: 8px 16px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 12px;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: all 0.2s;

    &:hover {
      background: rgba(0, 0, 0, 0.8);
      transform: scale(1.05);
    }
  }
}

// 移动端适配
@media screen and (max-width: 992px) {
  .debug-switch {
    top: 10px;
    right: 10px;

    .debug-btn {
      padding: 6px 12px;
      font-size: 11px;
    }
  }
}
</style>
