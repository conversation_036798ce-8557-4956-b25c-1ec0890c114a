<template>
  <div class="smart-login-wrapper">
    <!-- 调试切换按钮 -->
    <div v-if="showDebugSwitch" class="debug-switch">
      <button @click="toggleDevice" class="debug-btn">
        {{ isMobile ? "📱 移动端" : "💻 PC端" }} ({{ screenWidth }}px)
      </button>
    </div>

    <!-- PC端登录页面 -->
    <div v-if="!isMobile" class="pc-login">
      <Suspense>
        <template #default>
          <component :is="PCLoginPage" />
        </template>
        <template #fallback>
          <div class="loading">加载PC端登录页面...</div>
        </template>
      </Suspense>
    </div>

    <!-- 移动端登录页面 -->
    <div v-else class="mobile-login">
      <Suspense>
        <template #default>
          <component :is="MobileLoginPage" />
        </template>
        <template #fallback>
          <div class="loading">加载移动端登录页面...</div>
        </template>
      </Suspense>
    </div>
  </div>
</template>

<script setup lang="ts">
// 使用异步组件导入登录页面
const PCLoginPage = defineAsyncComponent(() => import("./index.vue"));
const MobileLoginPage = defineAsyncComponent(() => import("../mobile/login/index.vue"));

const route = useRoute();

// 响应式屏幕宽度
const screenWidth = ref(window.innerWidth);

// 基于屏幕宽度判断是否为移动端（1024px为分界点）
const isMobile = computed(() => screenWidth.value < 1024);

// 是否显示调试切换按钮
const showDebugSwitch = computed(() => {
  return route.query.debug === "true";
});

// 监听窗口大小变化
const handleResize = () => {
  screenWidth.value = window.innerWidth;
};

// 切换设备类型（仅调试模式）
const toggleDevice = () => {
  if (showDebugSwitch.value) {
    // 在调试模式下，手动切换屏幕宽度来模拟设备切换
    screenWidth.value = isMobile.value ? 1200 : 800;
  }
};

// 生命周期钩子
onMounted(() => {
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});
</script>

<style lang="scss" scoped>
.smart-login-wrapper {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.debug-switch {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;

  .debug-btn {
    padding: 8px 16px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 12px;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: all 0.2s;

    &:hover {
      background: rgba(0, 0, 0, 0.8);
      transform: scale(1.05);
    }
  }
}

.debug-info {
  position: fixed;
  top: 60px;
  right: 20px;
  z-index: 9999;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  font-size: 10px;
  color: #333;
}

.pc-login,
.mobile-login {
  width: 100%;
  height: 100%;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  font-size: 16px;
  color: #666;
}

// 移动端适配 (1024px以下)
@media screen and (max-width: 1023px) {
  .debug-switch {
    top: 10px;
    right: 10px;

    .debug-btn {
      padding: 6px 12px;
      font-size: 11px;
    }
  }
}
</style>
