<template>
  <div class="smart-login-wrapper">
    <!-- 设备切换按钮（调试模式） -->
    <div v-if="showDebugSwitch" class="debug-switch">
      <button @click="toggleDevice" class="debug-btn">
        {{ isMobile ? "📱 移动端" : "💻 PC端" }} (点击切换)
      </button>
    </div>

    <!-- 显示当前设备类型（调试信息） -->
    <div v-if="showDebugSwitch" class="debug-info">
      当前设备: {{ isMobile ? "Mobile" : "Desktop" }}
    </div>

    <!-- PC端登录页面 -->
    <div v-if="!isMobile" class="pc-login">
      <Suspense>
        <template #default>
          <component :is="PCLoginPage" />
        </template>
        <template #fallback>
          <div class="loading">加载PC端登录页面...</div>
        </template>
      </Suspense>
    </div>

    <!-- 移动端登录页面 -->
    <div v-else class="mobile-login">
      <Suspense>
        <template #default>
          <component :is="MobileLoginPage" />
        </template>
        <template #fallback>
          <div class="loading">加载移动端登录页面...</div>
        </template>
      </Suspense>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store";
import { DeviceEnum } from "@/enums/DeviceEnum";

// 使用异步组件导入登录页面
const PCLoginPage = defineAsyncComponent(() => import("./index.vue"));
const MobileLoginPage = defineAsyncComponent(() => import("../mobile/login/index.vue"));

const route = useRoute();
const appStore = useAppStore();

// 是否为移动端
const isMobile = computed(() => appStore.device === DeviceEnum.MOBILE);

// 是否显示调试切换按钮
const showDebugSwitch = computed(() => {
  return "true";
});

// 切换设备类型（仅调试模式）
const toggleDevice = () => {
  if (showDebugSwitch.value) {
    const newDevice = isMobile.value ? DeviceEnum.DESKTOP : DeviceEnum.MOBILE;
    appStore.toggleDevice(newDevice);
  }
};
</script>

<style lang="scss" scoped>
.smart-login-wrapper {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.debug-switch {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;

  .debug-btn {
    padding: 8px 16px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 12px;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: all 0.2s;

    &:hover {
      background: rgba(0, 0, 0, 0.8);
      transform: scale(1.05);
    }
  }
}

.debug-info {
  position: fixed;
  top: 60px;
  right: 20px;
  z-index: 9999;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  font-size: 10px;
  color: #333;
}

.pc-login,
.mobile-login {
  width: 100%;
  height: 100%;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  font-size: 16px;
  color: #666;
}

// 移动端适配
@media screen and (max-width: 992px) {
  .debug-switch {
    top: 10px;
    right: 10px;

    .debug-btn {
      padding: 6px 12px;
      font-size: 11px;
    }
  }
}
</style>
