<template>
  <el-form :model="formData" :rules="rules" ref="formRef" label-width="120px" :inline="true"
    :disabled="!props.editable">
    <el-form-item label="实收资本(元)">
      <el-input v-model="formData.sszb" placeholder="请输入实收资本" />
    </el-form-item>

    <el-form-item label="批准日期">
      <el-date-picker v-model="formData.pzrq" type="date" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择批准日期" />
    </el-form-item>

    <el-form-item label="批准文号">
      <el-input v-model="formData.pzwh" placeholder="请输入批准文号" />
    </el-form-item>

    <el-form-item label="注册资本(元)">
      <el-input v-model="formData.zczb" placeholder="请输入注册资本" />
    </el-form-item>

    <el-form-item label="实际出资金额(元)">
      <el-input v-model="formData.sjczje" placeholder="请输入实际出资金额" />
    </el-form-item>

    <el-form-item label="联系电话">
      <el-input v-model="formData.lxdh" placeholder="请输入联系电话" maxlength="20" />
    </el-form-item>

    <el-form-item label="营业期限-开始时间">
      <el-date-picker v-model="formData.kssj" type="date" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择开始时间" />
    </el-form-item>

    <el-form-item label="营业期限-截止时间">
      <el-date-picker v-model="formData.jzsj" type="date" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择截止时间" />
    </el-form-item>

    <el-form-item label="持股比例">
      <el-input v-model="formData.cgbl" placeholder="请输入持股比例" maxlength="20" />
    </el-form-item>

    <el-form-item label="是否实际控制" prop="sfsjkz">
      <el-select v-model="formData.sfsjkz" placeholder="请选择是否实际控制">
        <el-option label="否" value="0" />
        <el-option label="是" value="1" />
      </el-select>
    </el-form-item>

    <el-form-item label="单位类型" prop="dwlx">
      <DDLXcode v-model="formData.dwlx" xcode="020224" clearable />
    </el-form-item>

    <el-form-item label="统一信用代码">
      <el-input v-model="formData.tydm" placeholder="请输入统一信用代码" maxlength="30" />
    </el-form-item>

    <el-form-item label="批准单位">
      <el-input v-model="formData.pzdw" placeholder="请输入批准单位" maxlength="50" />
    </el-form-item>

    <el-form-item label="法人代表">
      <el-input v-model="formData.frdb" placeholder="请输入法人代表" maxlength="10" />
    </el-form-item>

    <el-form-item label="法人身份证号">
      <el-input v-model="formData.frsfz" placeholder="请输入法人身份证号" maxlength="20" />
    </el-form-item>

    <el-form-item label="企业级别" prop="qyjb">
      <DDLXcode v-model="formData.qyjb" xcode="020223" clearable />
    </el-form-item>

    <el-form-item label="组织形式" prop="zzxs">
      <DDLXcode v-model="formData.zzxs" xcode="020225" clearable />
    </el-form-item>

    <el-form-item label="登记机关">
      <el-input v-model="formData.djjg" placeholder="请输入登记机关" maxlength="30" />
    </el-form-item>

    <el-form-item label="是否上市" prop="sfss">
      <el-select v-model="formData.sfss" placeholder="请选择是否上市">
        <el-option label="否" value="0" />
        <el-option label="是" value="1" />
      </el-select>
    </el-form-item>

    <el-form-item label="经营范围" prop="jyfw">
      <el-input v-model="formData.jyfw" placeholder="请输入经营范围" maxlength="50" />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import storeAPI, { ZcinfoDifferentForm } from "@/api/properties/store";
import { ElLoading } from "element-plus";
// 组件属性定义
const props = defineProps({
  guid: {
    type: String
  },
  zclxbh: {
    type: String
  },
  editable: {
    type: Boolean,
    required: true
  }
})

// 表单数据
const formData = reactive<ZcinfoDifferentForm>({
  zclxbh: props.zclxbh || '',
})
// 表单验证规则
const rules = reactive({
  sfsjkz: [{ required: true, message: '请选择是否实际控制', trigger: 'change' }],
  dwlx: [{ required: true, message: '请选择单位类型', trigger: 'change' }],
  qyjb: [{ required: true, message: '请选择企业级别', trigger: 'change' }],
  zzxs: [{ required: true, message: '请选择组织形式', trigger: 'change' }],
  sfss: [{ required: true, message: '请选择是否上市', trigger: 'change' }],
  jyfw: [{ required: true, message: '请输入经营范围', trigger: 'blur' }]
})

// 表单引用
const formRef = ref(ElForm);

// 监听值变化
watch(
  () => [props.guid, props.zclxbh],
  (val) => {
    console.log('props:', val)
    if (val && val[0] != '' && val[1] != undefined) {
      console.log('props:', val, val[0], val[1])
      //获取表单数据
      storeAPI.getFormDataDiff({ guid: val[0], zclxbh: val[1] }).then((res) => {
        Object.assign(formData, res)
      })

      formData.zclxbh = val[1]
      formData.guid = val[0]
    }
  },
  { deep: true, immediate: true }
)

// 重置表单
const resetForm = () => {
  formRef.value.resetFields()
}

// 提交表单
const submitForm = () => {
  return new Promise((resolve, reject) => {
    formRef.value?.validate((valid) => {
      if (valid) {
        // 显示加载状态
        const loading = ElLoading.service({
          lock: true,
          text: '正在保存...',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        storeAPI.updateDiff(formData).then((res) => {
          // 关闭加载状态
          loading.close()
          // 显示成功消息
          ElMessage({
            type: 'success',
            message: '保存成功'
          })
          console.log('表单提交成功:', res)
          resolve(res)
        }).catch(error => {
          // 关闭加载状态
          loading.close()
          // 显示错误消息
          ElMessage({
            type: 'error',
            message: '保存失败: ' + (error.message || '未知错误')
          })
          console.error('表单提交失败:', error)
          reject(error)
        })
      } else {
        ElMessage({
          type: 'warning',
          message: '请填写必填项'
        })
        reject(new Error('表单验证失败'))
        return false
      }
    })
  })
}


// 表单验证方法
const validateForm = () => {
  return new Promise((resolve, reject) => {
    formRef.value?.validate((valid) => {
      if (valid) {
        resolve(true)
      } else {
        reject(new Error('表单验证失败'))
      }
    })
  })
}


// 导出方法供父组件使用
defineExpose({
  submitForm,
  resetForm,
  validate: validateForm
})
</script>

<style scoped>
/* 自定义样式 */
.el-form-item {
  width: 330px;
}
</style>