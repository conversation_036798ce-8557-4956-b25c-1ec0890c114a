import request from "@/utils/request";

const FileAPI = {
  /**
   * 文件上传地址
   */
  uploadUrl: import.meta.env.VITE_APP_BASE_API + "/api/v1/files",
  /**
   * 获取文件列表
   *
   * @param file
   */
  getFileList(guid: string, code: string) {
    return request<any, FileInfo[]>({
      url: "/api/v1/files/list",
      method: "get",
      params: {
        guid: guid,
        code: code,
      },
    });
  },
  /**
   * 获取图片集列表
   *
   * @param file
   */
  getFileListByCode(guid: string, code: string) {
    return request<any, FileInfo[]>({
      url: "/api/v1/files/listByCode",
      method: "get",
      params: {
        guid: guid,
        code: code,
      },
    });
  },
  /**
   * 上传文件
   *
   * @param file
   */
  upload(file: File, guid: string, code: string) {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("guid", guid);
    formData.append("code", code);
    return request<any, FileInfo>({
      url: "/api/v1/files",
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  /**
   * 删除文件
   *
   * @param filePath 文件完整路径
   */
  deleteByPath(uid?: number, filePath?: string) {
    return request({
      url: "/api/v1/files",
      method: "delete",
      params: { filePath: filePath, uid: uid },
    });
  },

  /**
   * 下载文件
   * @param url
   * @param fileName
   */
  downloadFile(url: string, fileName?: string) {
    return request({
      url: `/api/v1/files/download/${url}`,
      method: "get",
      responseType: "blob",
    }).then((res) => {
      const blob = new Blob([res.data]);
      const a = document.createElement("a");
      const url = window.URL.createObjectURL(blob);
      a.href = url;
      a.download = fileName || "下载文件";
      a.click();
      window.URL.revokeObjectURL(url);
    });
  },
};

export default FileAPI;

/**
 * 文件API类型声明
 */
export interface FileInfo {
  uid: number;
  /** 文件名 */
  name: string;
  /** 文件路径 */
  url: string;
  /** 文件是否上传 */
  isRequired: boolean;
  id: number;
  guid: string;
  frmcode: string;
  fjcode: string;
  fjname: string;
  fjmc: string;
  filesize: string;
  filekide: string;
  djsj: string;
  djr: string;
  md5: string;
  notes: string;
  baseurl: string;
}
