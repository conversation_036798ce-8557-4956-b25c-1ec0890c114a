<template>
  <div class="flow-chart-demo">
    <el-card>
      <template #header>
        <div class="demo-header">
          <h3>工作流程图演示</h3>
          <el-button-group>
            <el-button
              :type="demoType === 'serial' ? 'primary' : 'default'"
              @click="loadSerialDemo"
            >
              串行流程
            </el-button>
            <el-button
              :type="demoType === 'parallel' ? 'primary' : 'default'"
              @click="loadParallelDemo"
            >
              并行流程
            </el-button>
            <el-button :type="demoType === 'mixed' ? 'primary' : 'default'" @click="loadMixedDemo">
              混合流程
            </el-button>
          </el-button-group>
        </div>
      </template>

      <FlowChart :steps="demoSteps" :current-netcode="currentNetcode" />

      <div class="demo-controls">
        <el-button @click="simulateProgress" :disabled="isSimulating">
          {{ isSimulating ? "模拟进行中..." : "模拟流程进度" }}
        </el-button>
        <el-button @click="resetDemo">重置演示</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { WorkFlowChartVO } from "@/api/system/workflow";
import FlowChart from "./FlowChart.vue";

defineOptions({
  name: "FlowChartDemo",
});

const demoType = ref<"serial" | "parallel" | "mixed">("serial");
const demoSteps = ref<WorkFlowChartVO[]>([]);
const currentNetcode = ref("");
const isSimulating = ref(false);

const demoDescriptions = {
  serial: "串行流程：节点按顺序执行，一个完成后才能进行下一个",
  parallel: "并行流程：多个节点可以同时执行，提高效率",
  mixed: "混合流程：结合串行和并行，适用于复杂的业务场景",
};

// 串行流程演示数据
const serialDemoData: WorkFlowChartVO[] = [
  {
    netcode: "start",
    netname: "发起申请",
    nickname: "张三",
    time: "2024-01-15 09:00:00",
    netstate: "1",
    level: 0,
    branchIndex: 0,
    flowType: "serial",
    order: 0,
  },
  {
    netcode: "initial_review",
    netname: "初步审核",
    nickname: "李四",
    time: "2024-01-15 09:30:00",
    netstate: "1",
    level: 1,
    branchIndex: 0,
    flowType: "serial",
    order: 1,
  },
  {
    netcode: "dept_review",
    netname: "部门审核",
    nickname: "王五",
    time: "2024-01-15 10:30:00",
    netstate: "1",
    level: 2,
    branchIndex: 0,
    flowType: "serial",
    order: 2,
  },
  {
    netcode: "manager_review",
    netname: "主管审核",
    nickname: "赵六",
    time: "2024-01-15 11:00:00",
    netstate: "1",
    level: 3,
    branchIndex: 0,
    flowType: "serial",
    order: 3,
  },
  {
    netcode: "finance_review",
    netname: "财务审核",
    nickname: "钱七",
    time: "2024-01-15 14:00:00",
    netstate: "1",
    level: 4,
    branchIndex: 0,
    flowType: "serial",
    order: 4,
  },
  {
    netcode: "budget_check",
    netname: "预算检查",
    nickname: "孙八",
    time: "2024-01-15 15:00:00",
    netstate: "1",
    level: 5,
    branchIndex: 0,
    flowType: "serial",
    order: 5,
  },
  {
    netcode: "legal_review",
    netname: "法务审核",
    nickname: "周九",
    time: "",
    netstate: "2",
    level: 6,
    branchIndex: 0,
    flowType: "serial",
    order: 6,
  },
  {
    netcode: "compliance_check",
    netname: "合规检查",
    nickname: "吴十",
    time: "",
    netstate: "0",
    level: 7,
    branchIndex: 0,
    flowType: "serial",
    order: 7,
  },
  {
    netcode: "risk_assessment",
    netname: "风险评估",
    nickname: "郑十一",
    time: "",
    netstate: "0",
    level: 8,
    branchIndex: 0,
    flowType: "serial",
    order: 8,
  },
  {
    netcode: "director_review",
    netname: "总监审批",
    nickname: "王十二",
    time: "",
    netstate: "0",
    level: 9,
    branchIndex: 0,
    flowType: "serial",
    order: 9,
  },
  {
    netcode: "ceo_approve",
    netname: "总经理审批",
    nickname: "李十三",
    time: "",
    netstate: "0",
    level: 10,
    branchIndex: 0,
    flowType: "serial",
    order: 10,
  },
  {
    netcode: "final_confirm",
    netname: "最终确认",
    nickname: "张十四",
    time: "",
    netstate: "0",
    level: 11,
    branchIndex: 0,
    flowType: "serial",
    order: 11,
  },
];

// 并行流程演示数据
const parallelDemoData: WorkFlowChartVO[] = [
  {
    netcode: "start",
    netname: "发起申请",
    nickname: "张三",
    time: "2024-01-15 09:00:00",
    netstate: "1",
    level: 0,
    branchIndex: 0,
    flowType: "serial",
    order: 0,
  },
  // 第一层并行节点
  {
    netcode: "tech_review",
    netname: "技术评审",
    nickname: "李四",
    time: "2024-01-15 10:00:00",
    netstate: "1",
    level: 1,
    branchIndex: 0,
    flowType: "parallel",
    order: 1,
  },
  {
    netcode: "finance_review",
    netname: "财务评审",
    nickname: "王五",
    time: "2024-01-15 10:15:00",
    netstate: "1",
    level: 1,
    branchIndex: 1,
    flowType: "parallel",
    order: 2,
  },
  {
    netcode: "legal_review",
    netname: "法务评审",
    nickname: "赵六",
    time: "2024-01-15 10:30:00",
    netstate: "1",
    level: 1,
    branchIndex: 2,
    flowType: "parallel",
    order: 3,
  },
  {
    netcode: "hr_review",
    netname: "人事评审",
    nickname: "钱七",
    time: "2024-01-15 10:45:00",
    netstate: "1",
    level: 1,
    branchIndex: 3,
    flowType: "parallel",
    order: 4,
  },
  {
    netcode: "security_review",
    netname: "安全评审",
    nickname: "孙八",
    time: "",
    netstate: "2",
    level: 1,
    branchIndex: 4,
    flowType: "parallel",
    order: 5,
  },
  // 第一层汇总
  {
    netcode: "first_serial",
    netname: "初步汇总",
    nickname: "周九",
    time: "",
    netstate: "0",
    level: 2,
    branchIndex: 0,
    flowType: "serial",
    order: 6,
  },
  // 第二层并行节点
  {
    netcode: "dept_manager",
    netname: "部门经理审批",
    nickname: "吴十",
    time: "",
    netstate: "0",
    level: 3,
    branchIndex: 0,
    flowType: "parallel",
    order: 7,
  },
  {
    netcode: "finance_manager",
    netname: "财务经理审批",
    nickname: "郑十一",
    time: "",
    netstate: "0",
    level: 3,
    branchIndex: 1,
    flowType: "parallel",
    order: 8,
  },
  {
    netcode: "legal_manager",
    netname: "法务经理审批",
    nickname: "王十二",
    time: "",
    netstate: "0",
    level: 3,
    branchIndex: 2,
    flowType: "parallel",
    order: 9,
  },
  // 最终汇总
  {
    netcode: "final_serial",
    netname: "最终汇总审批",
    nickname: "李十三",
    time: "",
    netstate: "0",
    level: 4,
    branchIndex: 0,
    flowType: "serial",
    order: 10,
  },
  // 最终审批
  {
    netcode: "ceo_approve",
    netname: "总经理审批",
    nickname: "张十四",
    time: "",
    netstate: "0",
    level: 5,
    branchIndex: 0,
    flowType: "serial",
    order: 11,
  },
];

// 混合流程演示数据
const mixedDemoData: WorkFlowChartVO[] = [
  {
    netcode: "start",
    netname: "项目立项",
    nickname: "项目经理",
    time: "2024-01-15 09:00:00",
    netstate: "1",
    level: 0,
    branchIndex: 0,
    flowType: "serial",
    order: 0,
  },
  {
    netcode: "initial_review",
    netname: "初步评估",
    nickname: "部门主管",
    time: "2024-01-15 09:30:00",
    netstate: "1",
    level: 1,
    branchIndex: 0,
    flowType: "serial",
    order: 1,
  },
  {
    netcode: "dept_review",
    netname: "部门初审",
    nickname: "部门经理",
    time: "2024-01-15 10:00:00",
    netstate: "1",
    level: 2,
    branchIndex: 0,
    flowType: "serial",
    order: 2,
  },
  // 第一层并行评审
  {
    netcode: "tech_review",
    netname: "技术评审",
    nickname: "技术总监",
    time: "2024-01-15 11:00:00",
    netstate: "1",
    level: 3,
    branchIndex: 0,
    flowType: "parallel",
    order: 3,
  },
  {
    netcode: "finance_review",
    netname: "财务评审",
    nickname: "财务总监",
    time: "",
    netstate: "2",
    level: 3,
    branchIndex: 1,
    flowType: "parallel",
    order: 4,
  },
  {
    netcode: "legal_review",
    netname: "法务评审",
    nickname: "法务顾问",
    time: "2024-01-15 11:45:00",
    netstate: "1",
    level: 3,
    branchIndex: 2,
    flowType: "parallel",
    order: 5,
  },
  {
    netcode: "hr_review",
    netname: "人事评审",
    nickname: "人事总监",
    time: "",
    netstate: "2",
    level: 3,
    branchIndex: 3,
    flowType: "parallel",
    order: 6,
  },
  {
    netcode: "quality_review",
    netname: "质量评审",
    nickname: "质量总监",
    time: "2024-01-15 11:45:00",
    netstate: "1",
    level: 3,
    branchIndex: 4,
    flowType: "parallel",
    order: 7,
  },
  // 第一次汇总
  {
    netcode: "first_serial",
    netname: "初步汇总",
    nickname: "副总经理",
    time: "",
    netstate: "0",
    level: 4,
    branchIndex: 0,
    flowType: "serial",
    order: 8,
  },
  // 高级审批层
  {
    netcode: "senior_tech",
    netname: "高级技术审批",
    nickname: "CTO",
    time: "",
    netstate: "0",
    level: 5,
    branchIndex: 0,
    flowType: "parallel",
    order: 9,
  },
  {
    netcode: "senior_finance",
    netname: "高级财务审批",
    nickname: "CFO",
    time: "",
    netstate: "0",
    level: 5,
    branchIndex: 1,
    flowType: "parallel",
    order: 10,
  },
  {
    netcode: "senior_ops",
    netname: "高级运营审批",
    nickname: "COO",
    time: "",
    netstate: "0",
    level: 5,
    branchIndex: 2,
    flowType: "parallel",
    order: 11,
  },
  // 最终汇总
  {
    netcode: "final_serial",
    netname: "最终汇总",
    nickname: "执行副总",
    time: "",
    netstate: "0",
    level: 6,
    branchIndex: 0,
    flowType: "serial",
    order: 12,
  },
  // 最高级审批
  {
    netcode: "board_review",
    netname: "董事会审议",
    nickname: "董事长",
    time: "",
    netstate: "0",
    level: 7,
    branchIndex: 0,
    flowType: "serial",
    order: 13,
  },
  {
    netcode: "ceo_approve",
    netname: "CEO最终审批",
    nickname: "CEO",
    time: "",
    netstate: "0",
    level: 8,
    branchIndex: 0,
    flowType: "serial",
    order: 14,
  },
  {
    netcode: "final_confirm",
    netname: "最终确认",
    nickname: "项目总监",
    time: "",
    netstate: "0",
    level: 9,
    branchIndex: 0,
    flowType: "serial",
    order: 15,
  },
];

// 加载不同类型的演示
function loadSerialDemo() {
  demoType.value = "serial";
  demoSteps.value = [...serialDemoData];
  currentNetcode.value = "legal_review";
}

function loadParallelDemo() {
  demoType.value = "parallel";
  demoSteps.value = [...parallelDemoData];
  currentNetcode.value = "security_review";
}

function loadMixedDemo() {
  demoType.value = "mixed";
  demoSteps.value = [...mixedDemoData];
  currentNetcode.value = "hr_review";
}

// 模拟流程进度
async function simulateProgress() {
  isSimulating.value = true;

  const steps = demoSteps.value;
  const currentIndex = steps.findIndex((step) => step.netcode === currentNetcode.value);

  if (currentIndex < steps.length - 1) {
    // 完成当前节点
    steps[currentIndex].netstate = "1";
    steps[currentIndex].time = new Date().toLocaleString();

    // 移动到下一个节点
    setTimeout(() => {
      currentNetcode.value = steps[currentIndex + 1].netcode!;
      steps[currentIndex + 1].netstate = "2";
      isSimulating.value = false;
    }, 1000);
  } else {
    isSimulating.value = false;
  }
}

// 重置演示
function resetDemo() {
  switch (demoType.value) {
    case "serial":
      loadSerialDemo();
      break;
    case "parallel":
      loadParallelDemo();
      break;
    case "mixed":
      loadMixedDemo();
      break;
  }
}

// 初始化
loadSerialDemo();
</script>

<style lang="scss" scoped>
.flow-chart-demo {
  .demo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      color: var(--el-text-color-primary);
    }
  }

  .demo-description {
    margin: 20px 0;
  }

  .demo-controls {
    margin-top: 20px;
    text-align: center;

    .el-button {
      margin: 0 10px;
    }
  }
}
</style>
