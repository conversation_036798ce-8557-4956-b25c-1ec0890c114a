<template>
  <div class="app-container">
    <Title name="受理单信息">
      <div v-if="editable">
        <el-button type="primary" @click="handleSave()">保存</el-button>
        <el-button type="danger" @click="handleSubmit()">提交</el-button>
      </div>
    </Title>

    <el-form
      ref="dataFormRef"
      :model="formData"
      :rules="rules"
      label-width="auto"
      :inline="true"
      :disabled="!editable"
    >
      <el-form-item label="受理单号" prop="czbh">
        <el-input v-model="formData.czbh" :disabled="true" placeholder="系统自动生成" />
      </el-form-item>
      <el-form-item label="处置方式" prop="czlx">
        <el-input v-model="formData.czlxname" :disabled="true" />
      </el-form-item>

      <el-form-item label="受理人" prop="djrname">
        <el-input v-model="formData.djrname" :disabled="true" />
      </el-form-item>
      <el-form-item label="受理部门" prop="djbmname">
        <el-input v-model="formData.djbmname" :disabled="true" />
      </el-form-item>
      <el-form-item label="受理时间" prop="djsj">
        <el-input v-model="formData.djsj" :disabled="true" />
      </el-form-item>
      <div>
        <el-form-item label="处置说明" prop="czyy">
          <el-input v-model="formData.czyy" type="textarea" placeholder="请输入处置说明" />
        </el-form-item>
      </div>
    </el-form>
  </div>
  <div class="app-container">
    <Title name="受理的处置单">
      <div>
        <el-button v-if="editable" plain type="danger" icon="plus" @click="handleAddDetails">
          添加处置单
        </el-button>
      </div>
    </Title>

    <el-table v-loading="loading" :data="pageData" highlight-current-row :border="true" stripe>
      <el-table-column type="index" label="序号" width="55" />
      <el-table-column label="处置单号" prop="czbh" width="120" />
      <el-table-column label="处置方式" prop="czlxname" width="200" />
      <el-table-column label="数量" prop="czsl" width="100" />
      <el-table-column label="金额(元)" prop="czje" min-width="80" />
      <el-table-column label="申请人" prop="djrname" width="200" />
      <el-table-column label="申请部门" prop="djbmname" width="300" />
      <!-- ********************** 操作列 **********************
         按钮：查询View；编辑Edit；删除Delete -->
      <el-table-column label="操作" fixed="right" width="150">
        <template #default="scope">
          <el-button type="primary" icon="View" size="small" link @click="hancleRowView(scope.row)">
            查看
          </el-button>
          <el-button
            v-if="editable"
            type="danger"
            icon="Delete"
            size="small"
            link
            @click="hancleRowDelete(scope.row.guid)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- ********************** 翻页 ********************** -->
    <pagination
      v-if="total > 10"
      v-model:total="total"
      v-model:page="queryListParams.pageNum"
      v-model:limit="queryListParams.pageSize"
      @pagination="handleListQuery"
    />
  </div>
  <div class="app-container">
    <Title name="处置附件列表" />
    <!-- <FileView :key="itemGuid" :guid="itemGuid" /> -->
  </div>
  <div class="app-container">
    <Title name="受理附件上传" />
    <FileUpload v-if="editable" :key="props.guid" :guid="props.guid" code="" />
    <FileView v-else :guid="props.guid" code="" />
    <!-- 添加明细弹窗 -->
    <el-drawer
      v-model="chooseVisible"
      :with-header="false"
      append-to-body
      size="65%"
      @close="handleCloseDialog"
    >
      <ChooseClearance :key="clearGuid" :guid="clearGuid" type="edit" />
    </el-drawer>
    <!-- 查看处置单详情 -->
    <el-drawer v-model="itemVisible" :with-header="false" append-to-body size="75%">
      <DisposeView
        :guid="itemGuid"
        :editable="false"
        :netcode="itemNetcode"
        :checkstatus="1"
        :RefreshFatherDrawer="handleCloseDialog"
      />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import clearAPI from "@/api/properties/clearance";
import ChooseClearance from "@/views/properties/clearance/components/ClearanceChoose.vue";
import DisposeView from "@/views/properties/dispose/components/DisposeView.vue";
import { ElLoading } from "element-plus";
import FileView from "@/components/GY/FileView/index.vue";
const props = defineProps({
  //ID和guid
  guid: {
    type: String,
    required: true,
  },
  //保存成功后需要关闭抽屉并刷新数据
  RefreshFatherDrawer: {
    type: Function,
    required: true,
  },
  //是否可编辑
  editable: {
    type: Boolean,
    required: true,
  },
});
//初始化一些用户的信息
//表单

const dataFormRef = ref(ElForm);
const loading = ref(false);
const removeIds = ref<number[]>([]);
const total = ref(0);
const rules = reactive({
  // dcbm: [{ required: true, message: "不能为空", trigger: "blur" }],
  // dcbmgly: [{ required: true, message: "不能为空", trigger: "blur" }],
  // drbm: [{ required: true, message: "不能为空", trigger: "blur" }],
  // drbmgly: [{ required: true, message: "不能为空", trigger: "blur" }],
  // drr: [{ required: true, message: "不能为空", trigger: "blur" }],
});
let formData = reactive<any>({});
/** 初始化表单信息 */
function handleFormQuery() {
  clearAPI
    .getFormData(props.guid)
    .then((res) => {
      Object.assign(formData, res);
      console.log("formData", formData);
    })
    .finally(() => {});
}
const handleCloseDialog = () => {
  handleListQuery();
};
const queryListParams = reactive<any>({
  pageNum: 1,
  pageSize: 10,
  by5: "1", //单据类型，0申请单，1受理单
  by4: props.guid, //受理单guid
});
const pageData = ref<any[]>([]);
/** 初始化列表信息 */
const handleListQuery = () => {
  //guid为空是新增
  if (props.guid != "") {
    loading.value = true;
    clearAPI
      .getClearDetailPage(queryListParams)
      .then((data) => {
        pageData.value = data.list;
        total.value = data.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }
};

const handleSave = async () => {
  dataFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const submitData = { ...formData };
      const id = formData.id;
      const apiCall = id ? clearAPI.update(id, submitData) : clearAPI.add(submitData);
      await apiCall
        .then(async (res) => {
          ElMessage.success("受理单保存成功");
          props.RefreshFatherDrawer();
        })
        .catch((error) => {
          ElMessage.error("操作失败，失败原因：" + error.message);
        })
        .finally(() => {});
    }
  });
};

/** 提交表单 */
const handleSubmit = () => {
  ElMessageBox.confirm("确认提交审批?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      await handleSave().then(async () => {
        //第二层保存完成
        const submitData = { ...formData };
        const dialogloading = ElLoading.service({
          lock: true,
          text: "处理中",
        });

        await clearAPI
          .submit(submitData.guid || "")
          .then(() => {
            ElMessage.success("提交成功");
            props.RefreshFatherDrawer(true);
          })
          .finally(() => {
            dialogloading.close();
          })
          .catch((error) => {
            ElMessage.error(error.message);
          });
      });
    })
    .catch(() => {});
};

//删除项
const hancleRowDelete = (guid: string) => {
  ElMessageBox.confirm("确认删除数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      clearAPI
        .deleteDetailsByGuids(guid)
        .then(() => {
          ElMessage.success("删除成功");
          handleListQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
};

//—————————————————————————————————————————————打开弹窗选择资产

// 弹窗显隐
const chooseVisible = ref(false);
const clearGuid = ref("");
const handleAddDetails = () => {
  clearGuid.value = props.guid;
  chooseVisible.value = true;
};

// 获取子组件的返回,选择的资产编号,
// const handleReturnConfirm = (zcguid: string[]) => {
//   console.log(zcguid.join(","));
//   const param = { czGuid: props.guid || "", zcGuid: zcguid.join(",") };

//   console.log("添加调拨资产：", param);
//   disposeAPI
//     .addDetail(param)
//     .then(() => {
//       // 添加成功
//       ElMessage.success("添加成功");
//       handleQuery();
//       choosedialog.visible = false;
//     })
//     .finally(() => {});
// };

//------------------------------打开弹窗查看资产信息
const itemVisible = ref(false);
const itemGuid = ref("");
const itemNetcode = ref("");
const hancleRowView = (row: any) => {
  console.log("guid,netcode", row.guid, row.netcode);
  itemGuid.value = row.guid;
  itemNetcode.value = row.netcode;
  itemVisible.value = true;
};

onMounted(() => {
  handleFormQuery();
  handleListQuery();
});
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}
</style>
@/api/properties/clearance
