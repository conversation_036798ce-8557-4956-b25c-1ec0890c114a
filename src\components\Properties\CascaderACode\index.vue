<template>
  <el-cascader
    v-model="_modelValue"
    class="cascader"
    style="width: 100%"
    :options="options"
    :props="cascaderProps"
    filterable
    clearable
  />
</template>
<script setup lang="ts">
//教育财政分类的下拉框多选
import SysAcodeApi, { SysAcodePageQuery } from "@/api/system/sys-acode";
import type { CascaderProps, CascaderOption } from "element-plus";

const props = defineProps({
  modelValue: {
    type: String as PropType<string>,
    default: "",
  },
});

//子传父;
const emit = defineEmits<{
  "update:modelValue": [value: string];
}>();

const _modelValue = computed({
  get: () => props.modelValue,
  set: (val) => emit("update:modelValue", val),
});

const options = ref<CascaderOption[]>([]);

const cascaderProps = reactive<CascaderProps>({
  lazy: true,
  emitPath: false,
  lazyLoad(node, resolve) {
    const params = reactive<SysAcodePageQuery>({
      preCode: "0101",
      pageNum: 1,
      pageSize: 100,
    });

    const { value } = node;
    //第一次加载判断是否是根节点
    if (node.root) {
      params.preCode = "0101";
    } else {
      params.preCode = value as string;
    }

    SysAcodeApi.getAcodelist(params).then((res) => {
      const nodes: CascaderOption[] = res.list.map((item: any) => ({
        value: item.xcode,
        label: item.name,
        leaf: item.xcode.length === 12,
      }));
      resolve(nodes);
    });
  },
});
</script>
<style scoped></style>
