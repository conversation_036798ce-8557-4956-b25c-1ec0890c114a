<template>
  <!-- 支持搜索 -->
  <el-select v-model="_modelValue" filterable :multiple="props.multiple">
    <el-option v-for="item in pageData" :label="item.name" :value="item.xcode" />
  </el-select>
</template>
<script setup lang="ts">
import SysApi, { XCodeVO } from "@/api/system/sys";
//根据Xcode绑定的下拉框，
//调用方式如下：
//<DDLXcode xcode="020216" v-model="pageData.form.HBDW" />
const props = defineProps({
  xcode: {
    type: String,
    required: true,
  },

  modelValue: [Object, String],
  multiple: Boolean,
});
//下拉框内容
const pageData = ref<XCodeVO[]>([]);
//子传父
const emit = defineEmits(["update:modelValue"]);
const _modelValue = computed({
  get: () => {
    return props.modelValue;
  },
  set: (val) => {
    emit("update:modelValue", val);
  },
});

//请求参数
const params = {
  pageNum: 1,
  pageSize: 100,
  preXCode: props.xcode,
};
onMounted(() => {
  console.log("props.xcode", props.xcode);
  SysApi.getXcodeList(params).then((res: any) => {
    pageData.value = res.list;
  });
});
</script>
