<template>
  <div>
    <el-card>
      <template #header>
        <div class="flex-x-between">
          <div class="flex-y-center">{{ props.name }}</div>
          <!-- <el-button type="primary" size="small" plain @click="listDialogVisible = true">
            更多
          </el-button> -->
        </div>
      </template>
      <el-scrollbar :height="props.height">
        <div ref="chartRef" style="width: 400px; height: 300px"></div>
      </el-scrollbar>
    </el-card>
  </div>
</template>

<script setup lang="ts">
//组件参数
const props = defineProps({
  name: {
    type: String,
    default: "数据统计",
  },
  //展示高度，默认200
  height: {
    type: String,
    default: "300",
  },
});

import { onMounted, ref } from "vue";
import * as echarts from "echarts";

import dashboardAPI from "@/api/properties/dashboard";

const chartRef = ref(null);

/* const chartData = [
 { value: 108, name: "车辆" },
  { value: 735, name: "通用设备" },
  { value: 580, name: "专用设备" },
  { value: 484, name: "房屋" },
  { value: 300, name: "文物" },
];*/

onMounted(() => {
  dashboardAPI.getAssetSStatistics().then((res: any) => {
    //pageData.list.push(...res);
    let chartData = new Array();
    chartData.push(...res);

    console.log("统计");
    console.log(chartData);
    // console.log("propertywarning", res);
    if (chartRef.value) {
      const myChart = echarts.init(chartRef.value);
      const option = {
        tooltip: {
          trigger: "item",
        },
        legend: {
          top: "",
          left: "center",
        },
        series: [
          {
            name: "资产分类统计",
            type: "pie",
            radius: ["40%", "70%"],
            avoidLabelOverlap: false,
            padAngle: 5,
            itemStyle: {
              borderRadius: 10,
            },
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 20,
                // fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: chartData,
          },
        ],
      };

      myChart.setOption(option);
    }
  });
});
</script>
<style lang="scss" scoped></style>
