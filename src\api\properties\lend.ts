import request from "@/utils/request";

const LEND_BASE_URL = "/api/v1/lend";

const lendAPI = {
  //删除/api/v1/lend/{guid}
  deleteDetailsByGuids(guid: string) {
    return request({
      url: `${LEND_BASE_URL}/details/${guid}`,
      method: "get",
    });
  },

  /**
   * 批量删除领用单明细，多个以英文逗号(,)分割
   *
   * @param ids 领用单明细ID字符串，多个以英文逗号(,)分割
   */
  deleteDetailsByIds(ids: string) {
    return request({
      url: `${LEND_BASE_URL}/details/delete/${ids}`,
      method: "get",
    });
  },
  deleteByGuids(guid: string) {
    return request({
      url: `${LEND_BASE_URL}/${guid}`,
      method: "get",
    });
  },
  //表单信息接口包含明细/api/v1/lend/{guid}/form
  getFormData(guid: string) {
    return request<any, any>({
      url: `${LEND_BASE_URL}/${guid}/form`,
      method: "get",
    });
  },

  // 领用单明细查询
  getDetailsPage(queryParams?: any) {
    return request<any, PageResult<any[]>>({
      url: `${LEND_BASE_URL}/details/page`,
      method: "get",
      params: queryParams,
    });
  },

  //修改借用表单信息/api/v1/lend/{Id}
  update(Id: number, data: any) {
    return request({
      url: `${LEND_BASE_URL}/update/${Id}`,
      method: "post",
      data: data,
    });
  },

  // 添加/api/v1/lend/add/{guid}
  add(guid: any) {
    return request({
      url: `${LEND_BASE_URL}/add/${guid}`,
      method: "post",
    });
  },

  /** 添加领用明细*/
  addDetail(data: { syGuid: string; zcGuid: string }) {
    return request({
      url: `${LEND_BASE_URL}/details/add`,
      method: "post",
      data: data,
    });
  },

  /** 获取借用分页数据/api/v1/lend/page */
  getPage(param?: any) {
    return request<any, PageResult<any[]>>({
      url: `${LEND_BASE_URL}/page`,
      method: "get",
      params: param,
    });
  },

  /**
   * 提交借用
   *
   * @param id receiveID
   * @param data receive表单数据
   */
  submit(guid: string, data?: any) {
    return request({
      url: `${LEND_BASE_URL}/submit/${guid}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 批量删除领用，多个以英文逗号(,)分割
   *
   * @param ids 领用ID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${LEND_BASE_URL}/delete/${ids}`,
      method: "get",
    });
  },

  /**
   * 批量归还，多个以英文逗号(,)分割
   *
   * @param ids 领用ID字符串，多个以英文逗号(,)分割
   */
  submitByIds(ids: string) {
    return request({
      url: `${LEND_BASE_URL}/batch-return/${ids}`,
      method: "get",
    });
  },

  //回收/api/v1/receives/callback/{guid}
  callBack(guid: string) {
    return request({
      url: `${LEND_BASE_URL}/callback/${guid}`,
      method: "get",
    });
  },
};

export default lendAPI;
