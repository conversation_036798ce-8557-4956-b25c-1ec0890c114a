import request from "@/utils/request";

const WORKLZSQB_BASE_URL = "/api/v1/workLzSqbs";

const WorkLzSqbAPI = {
  /** 获取采购论证分页数据 */
  getPage(queryParams?: WorkLzSqbPageQuery) {
    return request<any, PageResult<WorkLzSqbPageVO[]>>({
      url: `${WORKLZSQB_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },
  /** 获取采购论证分页数据,给采购申请选择论证用的 */
  SqSelgetPage(queryParams?: WorkLzSqbPageQuery) {
    return request<any, PageResult<WorkLzSqbPageVO[]>>({
      url: `${WORKLZSQB_BASE_URL}/sqsel/page`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取采购论证表单数据
   *
   * @param id WorkLzSqbID
   * @returns WorkLzSqb表单数据
   */
  getFormData(id: number) {
    return request<any, WorkLzSqbForm>({
      url: `${WORKLZSQB_BASE_URL}/${id}/form`,
      method: "get",
    });
  },

  /** 添加采购论证*/
  add(data: WorkLzSqbForm) {
    return request({
      url: `${WORKLZSQB_BASE_URL}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新采购论证
   *
   * @param id WorkLzSqbID
   * @param data WorkLzSqb表单数据
   */
  update(id: number, data: WorkLzSqbForm) {
    return request({
      url: `${WORKLZSQB_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 批量删除采购论证，多个以英文逗号(,)分割
   *
   * @param ids 采购论证ID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${WORKLZSQB_BASE_URL}/${ids}`,
      method: "delete",
    });
  },
};

export default WorkLzSqbAPI;

/** 采购论证分页查询参数 */
export interface WorkLzSqbPageQuery extends PageQuery {
  /** 项目编号 */
  xmbh?: string;
  /** 项目名称 */
  xmmc?: string;
  /** 项目负责人 */
  xmfzr?: string;
  /** 项目负责人名称 */
  xmfzrname?: string;

  /** 部门名称 */
  djbmname?: string;
  /** 登记部门 */
  djbm?: string;
  /** 评审类型 */
  pslx?: string;
  /** 采购目录 */
  cgml?: string;
}

/** 采购论证表单对象 */
export interface WorkLzSqbForm {
  /** id */
  id?: number;
  /** guid */
  guid?: string;
  /** 评审guid */
  psguid?: string;
  /** 原为评审编号,现改为调整理由 */
  psbh?: string;
  /** 默认=项目名称，可修改 */
  pstm?: string;
  /** 集合类型=论证类别 */
  pslx?: string;
  /** 评审组别 */
  psbm?: string;
  /** 采购目录 */
  cgml?: string;
  /** 主管部门 */
  zgbm?: string;
  /** 下一审批人 */
  nextspr?: string;
  /** 使用部门 */
  szbm?: string;
  /** 公管处部门编码 */
  xmguid?: string;
  /** 项目编号 */
  xmbh?: string;
  /** 项目名称 */
  xmmc?: string;
  /** 项目负责人 */
  xmfzr?: string;
  /** 项目负责人 */
  xmfzrname?: string;
  ysje?: number;
  /** 计划评审日期 */
  jhpsrq?: Date;
  /** 登记时间 */
  djsj?: Date;
  /** 登记人 */
  djr?: string;
  /** 登记人 */
  djrname?: string;
  /** 组织机构，集合类型 */
  djbm?: string;
  /** 部门名称 */
  djbmname?: string;
  /** 节点PK：流程编码+2位，支持多节点 */
  netcode?: string;
  /** 配置当前节点初始数据状态 */
  sjzt?: string;
  /** 评审发起人 */
  psfqr?: string;
  /** 备注 */
  notes?: string;
  /** 明细总金额 */
  by1?: number;
  /** 涉辐、高能耗 */
  by2?: string;
  /** 分管校领导审批人 */
  by3?: string;
  /** 是否已经经过专家评审  0:否 1：是  */
  by4?: string;
  /** 是否直接论证结束   0:否  1:是 */
  by5?: string;
  /** 项目内容 */
  xmnr?: string;
  /** 项目调研情况 */
  xmdyqk?: string;
  nexter?: string;
  nextbm?: string;
  /** 0：线下  1：线上 */
  sfxs?: string;
  /** 采购计划id */
  jhid?: number;
  /** 业务归口部门审核人 */
  zgbmspr?: string;
  /** 分管校领导 */
  xldspr?: string;
  /** 开发平台大仪论证ID */
  dylzid?: string;
  /** 论证初稿 */
  psnr?: string;
  /** 论证终稿 */
  pszg?: string;
  /** 专家组长 */
  pszz?: string;
  /** 二维码Guid用于防止扫码重复登录 */
  ewmguid?: string;
}

/** 采购论证分页对象 */
export interface WorkLzSqbPageVO {
  /** id */
  id?: number;
  /** guid */
  guid?: string;
  /** 评审guid */
  psguid?: string;
  /** 原为评审编号,现改为调整理由 */
  psbh?: string;
  /** 默认=项目名称，可修改 */
  pstm?: string;
  /** 集合类型=论证类别 */
  pslx?: string;
  /** 评审组别 */
  psbm?: string;
  /** 采购目录 */
  cgml?: string;
  /** 主管部门 */
  zgbm?: string;
  /** 下一审批人 */
  nextspr?: string;
  /** 使用部门 */
  szbm?: string;
  /** 公管处部门编码 */
  xmguid?: string;
  /** 项目编号 */
  xmbh?: string;
  /** 项目名称 */
  xmmc?: string;
  /** 项目负责人 */
  xmfzr?: string;
  /** 项目负责人 */
  xmfzrname?: string;
  ysje?: number;
  /** 计划评审日期 */
  jhpsrq?: Date;
  /** 登记时间 */
  djsj?: Date;
  /** 登记人 */
  djr?: string;
  /** 登记人 */
  djrname?: string;
  /** 组织机构，集合类型 */
  djbm?: string;
  /** 部门名称 */
  djbmname?: string;
  /** 节点PK：流程编码+2位，支持多节点 */
  netcode?: string;
  /** 配置当前节点初始数据状态 */
  sjzt?: string;
  /** 评审发起人 */
  psfqr?: string;
  /** 备注 */
  notes?: string;
  /** 明细总金额 */
  by1?: number;
  /** 涉辐、高能耗 */
  by2?: string;
  /** 分管校领导审批人 */
  by3?: string;
  /** 是否已经经过专家评审  0:否 1：是  */
  by4?: string;
  /** 是否直接论证结束   0:否  1:是 */
  by5?: string;
  /** 项目内容 */
  xmnr?: string;
  /** 项目调研情况 */
  xmdyqk?: string;
  nexter?: string;
  nextbm?: string;
  /** 0：线下  1：线上 */
  sfxs?: string;
  /** 采购计划id */
  jhid?: number;
  /** 业务归口部门审核人 */
  zgbmspr?: string;
  /** 分管校领导 */
  xldspr?: string;
  /** 开发平台大仪论证ID */
  dylzid?: string;
  /** 论证初稿 */
  psnr?: string;
  /** 论证终稿 */
  pszg?: string;
  /** 专家组长 */
  pszz?: string;
  /** 二维码Guid用于防止扫码重复登录 */
  ewmguid?: string;
}
