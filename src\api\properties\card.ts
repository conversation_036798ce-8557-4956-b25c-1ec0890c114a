import request from "@/utils/request";

const CARD_BASE_URL = "/api/v1/zcCards";

const cardAPI = {
  /**
   * 根据入库单号获取卡片信息
   */
  getPageByStore(params?: any) {
    return request<any, PageResult<any[]>>({
      url: `${CARD_BASE_URL}/page`,
      method: "get",
      params: params,
    });
  },
  ///api/v1/zcCards/{zcrkdh}/deleteZcCardsByZcrkdh
  deleteByStore(guid: string) {
    return request({
      url: `${CARD_BASE_URL}/delete-zccards-by-guid/${guid}`,
      method: "get",
    });
  },
  ///api/v1/zcCards/{ids}
  delete(ids: string) {
    return request({
      url: `${CARD_BASE_URL}/delete/${ids}`,
      method: "get",
    });
  },

  /**
   * 更新卡片
   *
   * @param id transferID
   * @param data transfer表单数据
   */
  update(id: number, data: any) {
    return request({
      url: `${CARD_BASE_URL}/update/${id}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 批量更新卡片
   *
   * @param guid transferID
   * @param data transfer表单数据
   */
  batchUpdate(ids: string, data: batchUpdateParam) {
    return request({
      url: `${CARD_BASE_URL}/batch/${ids}`,
      method: "post",
      data: data,
    });
  },

  //新增卡片/api/v1/zcCards/{zcrkdh}/addZcCardsByZcrkdh
  addCard(guid: string) {
    return request({
      url: `${CARD_BASE_URL}/add-zccards-by-guid/${guid}`,
      method: "post",
      // data: guid,
    });
  },

  //拆分卡片/api/v1/zcCards/{zcrkdh}/splitZcCardsByguid
  splitCard(guid: string) {
    return request({
      url: `${CARD_BASE_URL}/split-zccards-by-guid/${guid}`,
      method: "post",
    });
  },
};

export default cardAPI;

export interface batchUpdateParam {
  syfx?: string;
  syr?: string;
  sybm?: string;
  cfdd?: string;
  cfddname?: string; // 添加存放地点名称字段
  notes?: string;
  gly?: string;
  sl?: number;
  sbyt?: string;
}
