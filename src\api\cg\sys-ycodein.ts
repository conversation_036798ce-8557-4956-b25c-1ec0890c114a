import request from "@/utils/request";

const SYSYCODEIN_BASE_URL = "/api/v1/sysYcodeins";
export interface YCodeListQuery extends PageQuery {
  /** 父部门编码	 */
  preXCode?: string;
}
const SysYcodeinAPI = {
  /** 获取sys_ycodein配置表分页数据 */
  getPage(queryParams?: SysYcodeinPageQuery) {
    return request<any, PageResult<SysYcodeinPageVO[]>>({
      url: `${SYSYCODEIN_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 取Ycode
   *
   * @param queryParams 查询参数
   */
  getYcodeList(queryParams: YCodeListQuery) {
    return request<any, YCodeinVO[]>({
      url: `${SYSYCODEIN_BASE_URL}/get-ycodelist`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取sys_ycodein配置表表单数据
   *
   * @param id SysYcodeinID
   * @returns SysYcodein表单数据
   */
  getFormData(id: number) {
    return request<any, SysYcodeinForm>({
      url: `${SYSYCODEIN_BASE_URL}/${id}/form`,
      method: "get",
    });
  },

  /** 添加sys_ycodein配置表*/
  add(data: SysYcodeinForm) {
    return request({
      url: `${SYSYCODEIN_BASE_URL}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新sys_ycodein配置表
   *
   * @param id SysYcodeinID
   * @param data SysYcodein表单数据
   */
  update(id: number, data: SysYcodeinForm) {
    return request({
      url: `${SYSYCODEIN_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 批量删除sys_ycodein配置表，多个以英文逗号(,)分割
   *
   * @param ids sys_ycodein配置表ID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${SYSYCODEIN_BASE_URL}/${ids}`,
      method: "delete",
    });
  },
};

export default SysYcodeinAPI;

/** sys_ycodein配置表分页查询参数 */
export interface SysYcodeinPageQuery extends PageQuery {
  id?: number;
  /** 序号PK */
  guid?: string;
  /** 我方编码 */
  xcode?: string;
  /** 对方编码 */
  ycode?: string;
  /**  类别  1：采购目录
 2：采购方式
  3：采购类型
  4：资金类型
   5：付款方式
  6：法律条款
  8：附件类型
  9：资金来源
   10:费用项数据 */
  lb?: number;
  /** 状态，’1’ 正常  ‘0’停用 */
  enabled?: string;
}

/** sys_ycodein配置表表单对象 */
export interface SysYcodeinForm {
  id?: number;
  /** 年度 */
  year?: number;
  /** 序号PK */
  guid?: string;
  /** 我方编码 */
  xcode?: string;
  /** 对方编码 */
  ycode?: string;
  /**  类别  1：采购目录
 2：采购方式
  3：采购类型
  4：资金类型
   5：付款方式
  6：法律条款
  8：附件类型
  9：资金来源
   10:费用项数据 */
  lb?: number;
  /** 标准编码 */
  isbncode?: string;
  /** 名称 */
  name?: string;
  /** 级数 */
  levelnum?: number;
  /** 是否底级 */
  isleaf?: string;
  /** 上级序号 */
  parentGuid?: number;
  /** 状态，’1’ 正常  ‘0’停用 */
  enabled?: string;
  /** 支付编码 */
  zfcode?: string;
  /** 支付标识 1表示资金发送给银行 0表示资金不发送银行 */
  payflag?: string;
  /** 更新时间 */
  updatetime?: Date;
}

/** sys_ycodein配置表分页对象 */
export interface SysYcodeinPageVO {
  id?: number;
  /** 年度 */
  year?: number;
  /** 序号PK */
  guid?: string;
  /** 我方编码 */
  xcode?: string;
  /** 对方编码 */
  ycode?: string;
  /**  类别  1：采购目录
 2：采购方式
  3：采购类型
  4：资金类型
   5：付款方式
  6：法律条款
  8：附件类型
  9：资金来源
   10:费用项数据 */
  lb?: number;
  /** 标准编码 */
  isbncode?: string;
  /** 名称 */
  name?: string;
  /** 级数 */
  levelnum?: number;
  /** 是否底级 */
  isleaf?: string;
  /** 上级序号 */
  parentGuid?: number;
  /** 状态，’1’ 正常  ‘0’停用 */
  enabled?: string;
  /** 支付编码 */
  zfcode?: string;
  /** 支付标识 1表示资金发送给银行 0表示资金不发送银行 */
  payflag?: string;
  /** 更新时间 */
  updatetime?: Date;
}

/** sys_ycodein配置表分页对象 */
export interface YCodeinVO {
  id?: number;
  /** 年度 */
  year?: number;
  /** 序号PK */
  guid?: string;
  /** 我方编码 */
  xcode?: string;
  /** 对方编码 */
  ycode?: string;
  /**  类别  1：采购目录
 2：采购方式
  3：采购类型
  4：资金类型
   5：付款方式
  6：法律条款
  8：附件类型
  9：资金来源
   10:费用项数据 */
  lb?: number;
  /** 标准编码 */
  isbncode?: string;
  /** 名称 */
  name?: string;
  /** 级数 */
  levelnum?: number;
  /** 是否底级 */
  isleaf?: string;
  /** 上级序号 */
  parentGuid?: number;
  /** 状态，’1’ 正常  ‘0’停用 */
  enabled?: string;
  /** 支付编码 */
  zfcode?: string;
  /** 支付标识 1表示资金发送给银行 0表示资金不发送银行 */
  payflag?: string;
  /** 更新时间 */
  updatetime?: Date;
}
