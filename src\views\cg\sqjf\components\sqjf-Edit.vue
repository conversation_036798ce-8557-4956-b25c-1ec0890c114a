<!-- 申请经费 -->
<template>
  <!--经费卡添加弹窗-->
  <el-form :model="ysForm" style="margin: 30px" ref="dataFormRef_Ys" :rules="ysrules">
    <el-form-item label="经费卡编号" prop="jfbh">
      <el-input
        v-model="ysForm.jfbh"
        placeholder="经费卡编号"
        :disabled="ysForm?.jfid ? true : false"
      />
    </el-form-item>
    <el-form-item label="申请预算(元)" prop="ysje">
      <el-input v-model="ysForm.ysje" placeholder="申请预算" />
    </el-form-item>
  </el-form>
  <div style="text-align: right">
    <el-button @click="props.RefreshFatherDrawer(true)">取消</el-button>
    <el-button type="primary" @click="handleAddJf">确定</el-button>
  </div>
  <!--经费卡添加弹窗-->
</template>

<script setup lang="ts">
import ZxcgSqjfAPI, { ZxcgSqjfForm, ZxcgSqjfPageQuery, ZxcgSqjfPageVO } from "@/api/cg/zxcg-sqjf";
import { clearFormData } from "@/utils/page";
//————————————————————————————————————————————暴露的方法,和请求参数
//查询类型，新增【add】，编辑【edit】，查询【view】
const formType = reactive({
  add: false,
  edit: false,
  view: false,
});

//const emit = defineEmits(["handlecheck"]);
// 清理表单
const handleClearForm = () => {
  dataFormRef_Ys.value.resetFields();
  dataFormRef_Ys.value.clearValidate();
  ysForm.jfid = undefined;

  //formData.id = undefined;
  // formData.status = 1;
};

const ysForm = reactive<ZxcgSqjfForm>({});
const dataFormRef_Ys = ref(ElForm);
/** 初始化调拨信息 */
function handleFormQuery(id?: number) {
  console.log("jfid:", id);
  if (id) {
    ZxcgSqjfAPI.getFormData(id)
      .then((data) => {
        Object.assign(ysForm, data);
      })
      .catch((error) => {
        console.error("获取采购申请经费数据失败:", error);
        ElMessage.error("获取采购申请经费数据失败");
      });
  } else {
    // 进去前给个新的GUID
    //新增
  }
}
const ysrules = reactive({
  jfbh: [{ required: true, message: "经费卡编号不能为空", trigger: "blur" }],
  ysje: [
    { required: true, message: "预算金额不能为空", trigger: "blur" },
    /* { type: "number", message: "必须为数字值" },*/
    {
      pattern: /^(?=.*[1-9])\d*(?:\.\d{1,2})?$/,
      message: "必须为大于0的数字",
      trigger: "blur",
    },
  ], //金额要大于0
});
//新增预算
const handleAddJf = async () => {
  dataFormRef_Ys.value.validate(async (valid: any) => {
    if (valid) {
      ysForm.sqid = props.sqid || 0;
      const id = ysForm.jfid;
      const apiCall = id
        ? ZxcgSqjfAPI.update(id, ysForm.ysje)
        : ZxcgSqjfAPI.add(ysForm.jfbh, ysForm.sqid, ysForm.ysje);

      await apiCall
        .then(() => {
          ElMessage.success("添加经费卡成功");
          handleClearForm();
          // 重新加载经费列表
          props.RefreshFatherDrawer(true);
        })
        .catch((error) => {
          console.error("添加经费卡失败:", error);
          ElMessage.error("添加经费卡失败");
        })
        .finally(() => {
          //.close();
        });
    }
  });
};

const props = defineProps({
  RefreshFatherDrawer: {
    type: Function,
    required: true,
  },
  id: {
    type: Number,
  },
  sqid: {
    type: Number,
  },
  //是否可编辑
  editable: {
    type: Boolean,
  },
});
//——————————————————————————————————————————————————查询相关
const loading = ref(false);

//——————————————————————————————————————————————————操作相关

//——————————————————————————————————————————————————弹窗相关参数
onMounted(() => {
  //console.log("jfid:", props.id);
  handleFormQuery(props.id);
});

/*
watch(
   () => props.id,
   (newValue) => {
     handleFormQuery(newValue);
   }
 );*/

watch(
  () => props.sqid,
  (newValue) => {
    //jfqueryParams.sqid = newValue;
  }
);
// 明确暴露给父组件的方法
defineExpose({
  handleAddJf,
  handleClearForm,
});
</script>
<style lang="scss" scoped></style>
<style lang="scss" scoped>
/* 自定义对话框样式 */
.custom-dialog {
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  background-color: #ffffff;

  .el-dialog__header {
    background-color: #409eff;
    color: #ffffff;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;

    .el-dialog__title {
      color: #ffffff;
    }
  }

  .el-dialog__body {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.9);
  }
}
</style>
