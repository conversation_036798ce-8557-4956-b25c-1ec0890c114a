<!-- cursor测试:1 -->
<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item label="选择年份" prop="nf" v-if="view">
            <el-date-picker
              v-model="queryParams.nf"
              type="year"
              placeholder="选择年份"
              value-format="YYYY"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon>
                <Search />
              </template>
              搜索
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon>
                <Refresh />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="盘点计划列表">
        <div>
          <el-button v-if="add" type="success" plain size="small" @click="handleOpenDialog(true)">
            <template #icon>
              <Plus />
            </template>
            新增
          </el-button>
          <el-button
            v-if="add"
            type="danger"
            plain
            size="small"
            :disabled="removeIds.length === 0"
            @click="handleDelete()"
          >
            <template #icon>
              <Delete />
            </template>
            删除
          </el-button>
          <el-radio-group
            v-if="check"
            v-model="queryParams.checkstatus"
            @change="handleChangeRadio"
          >
            <el-radio :value="0" border>待审</el-radio>
            <el-radio :value="1" border>已审</el-radio>
          </el-radio-group>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        stripe
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column v-if="add" type="selection" width="55" align="center" fixed />
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column key="jhbh" label="计划编号" prop="jhbh" min-width="100" align="center" />
        <el-table-column key="jhmc" label="计划名称" prop="jhmc" min-width="120" align="center" />
        <el-table-column
          key="zclxbhsname"
          label="盘点资产分类"
          prop="zclxbhsname"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="jhztname"
          label="盘点状态"
          prop="jhztname"
          min-width="80"
          align="center"
        />
        <el-table-column key="zczs" label="资产总数" prop="zczs" min-width="100" align="center" />
        <el-table-column key="ypsl" label="已盘点数量" prop="ypsl" min-width="120" align="center" />
        <el-table-column key="wpsl" label="未盘点数量" prop="wpsl" min-width="120" align="center" />
        <el-table-column key="wcl" label="完成率" prop="wcl" min-width="100" align="center" />
        <el-table-column fixed="right" label="操作" width="120">
          <template #default="scope">
            <el-button
              v-if="add"
              type="primary"
              icon="edit"
              size="small"
              @click="handleOpenDialog(true, scope.row)"
            >
              编辑
            </el-button>
            <el-button v-if="add" type="danger" size="small" @click="handleDelete(scope.row.id)">
              <template #icon>
                <Delete />
              </template>
              删除
            </el-button>
            <el-button
              v-if="check && queryParams.checkstatus == 0"
              type="primary"
              icon="DocumentChecked"
              size="small"
              @click="handleOpenDialog(false, scope.row)"
            >
              审核
            </el-button>
            <el-button
              v-if="view"
              type="success"
              icon="Document"
              size="small"
              @click="handleOpenDialog(false, scope.row)"
            >
              查看
            </el-button>

            <el-button
              v-if="view && scope.row.isCallBack == '1'"
              type="danger"
              icon="Remove"
              size="small"
              @click="handleCallBack(scope.row.guid)"
            >
              收回
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 10"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 任务表单弹窗 -->
    <el-drawer v-model="dialog.visible" :title="dialog.title" append-to-body size="80%">
      <el-card>
        <!-- 引入 task 组件 -->
        <Task
          :id="itemId"
          :key="itemGuid"
          :guid="itemGuid"
          :editable="itemEditable"
          :dcbm="itemDcbm"
          :netcode="itemNetcode"
          :RefreshFatherDrawer="RefreshFatherDrawer"
        />
      </el-card>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import planAPI, { planPageVO, planPageQuery } from "@/api/properties/plan";
import Task from "./components/Task.vue"; // 引入组件
import { getGuid } from "@/utils/guid";
//————————————————————————————————————————————暴露的方法,和请求参数

function RefreshFatherDrawer(close?: boolean) {
  //是否关闭弹窗由参数决定，默认不关闭
  if (close) {
    dialog.visible = false;
  }
  handleQuery();
}
// 获取路由参数,view/add/check
const route = useRoute();
// 获取路由实例（可导航）
const router = useRouter();
const type = ref(route.query.type?.toString() || "view");
//显示隐藏权限,用于v-if
const add = type.value == "add";
const view = type.value == "view";
const check = type.value == "check";

//————————————————————————————————————————————查询相关
const queryFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);
let nf = "";
if (type.value == "view") {
  nf = String(new Date().getFullYear());
}
const queryParams = reactive<planPageQuery>({
  pageNum: 1,
  pageSize: 10,
  type: type.value,
  nf: nf,
});
// 计划表格数据
const pageData = ref<any[]>([]);

function handleYearChange() {}

/** 查询计划 */
function handleQuery() {
  loading.value = true;
  console.log("刷新列表数据");
  planAPI
    .getAllPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .catch((error) => {
      console.error("获取计划数据失败:", error);
      ElMessage.error("获取计划数据失败");
      pageData.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置计划查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  queryParams.type = type.value;
  handleQuery();
}
const handleChangeRadio = () => {
  handleResetQuery();
};

//————————————————————————————————————————————操作相关
const removeIds = ref<number[]>([]);
/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: planPageVO[]) {
  removeIds.value = selection.map((item) => item.id).filter((id) => id !== undefined) as number[];
}

/** 删除计划 */
function handleDelete(id?: string) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      planAPI
        .deleteByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .catch((error) => {
          console.error("删除计划数据失败:", error);
          ElMessage.error("删除计划数据失败");
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

const handleCallBack = (guid: string) => {
  ElMessageBox.confirm("确认收回吗?收回后可再次提交该单据", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      loading.value = true;
      planAPI
        .callBack(guid)
        .then(() => {
          ElMessage.success("操作成功");
          handleResetQuery();
        })
        .catch((error) => {
          ElMessage.error("操作失败，失败原因：" + error.message);
        })
        .finally(() => (loading.value = false));
    })
    .catch(() => {
      ElMessage.info("已取消收回");
    });
};

//————————————————————————————————————————————弹窗相关
// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});
/** 打开计划弹窗 */
const itemGuid = ref<string>("");
const itemId = ref<number | undefined>();
const itemDcbm = ref<string>("");
const itemEditable = ref<boolean>(false);
const itemNetcode = ref<string>("");

function handleOpenDialog(editable: boolean, row?: planPageVO) {
  itemGuid.value = row?.guid || getGuid();
  itemId.value = row?.id;
  itemNetcode.value = row?.netcode || "";
  itemEditable.value = editable;
  dialog.title = editable ? (row?.id ? "编辑计划单" : "新增计划单") : "盘点任务查看";
  dialog.visible = true;
  console.log("type.value", type.value);
}

function handleOpen(editable: boolean) {
  router.replace("/pub/adjustMarket?type=ly");
}

onMounted(() => {
  handleQuery();
});
</script>

<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}

.flex {
  display: flex;
  flex-direction: row;
}
</style>
<style lang="scss">
.el-table__row {
  .el-checkbox {
    margin-right: 10px;
  }
}
</style>
