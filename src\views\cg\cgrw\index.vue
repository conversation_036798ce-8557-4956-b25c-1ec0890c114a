<template>
  <div class="app-container">
   <el-card>
    <!-- ********************** 用插槽显示筛选框 ********************** -->
    <template #header>
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
                <el-form-item label="任务名称" prop="rwmc">
                      <el-input
                          v-model="queryParams.rwmc"
                          placeholder="任务名称"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="采购形式" prop="cgxs">
                      <el-input
                          v-model="queryParams.cgxs"
                          placeholder="采购形式"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="采购方式" prop="cgfs">
                      <el-input
                          v-model="queryParams.cgfs"
                          placeholder="采购方式"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="确认书编号" prop="qrsbh">
                      <el-input
                          v-model="queryParams.qrsbh"
                          placeholder="确认书编号"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon><Search /></template>
            搜索
          </el-button>
          <el-button @click="handleResetQuery">
            <template #icon><Refresh /></template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="列表">
        <div>
          <el-button
              v-hasPerm="['cg:zxcgCgrw:add']"
              type="success"
              plain
              size="small"
              @click="formType.view = false; handleEdit()"
          >
            <template #icon><Plus /></template>
            新增
          </el-button>
          <el-button
              v-hasPerm="['cg:zxcgCgrw:delete']"
              type="danger"
              plain
              size="small"
              :disabled="removeIds.length === 0"
              @click="handleDelete()"
          >
            <template #icon><Delete /></template>
            删除
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
          ref="dataTableRef"
          v-loading="loading"
          :data="pageData"
          highlight-current-row
          border
          @selection-change="handleSelectionChange"
          @expand-change="handleExpandChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed />
        <el-table-column label="展开" type="expand" width="55">
          <template #default="props">
            <div style="padding: 0px 70px; background-color: #ffffff">
              <!-- <TransferPropertiesList
                :dcbm="props.row.dcbm"
                :guid="props.row.guid || ''"
              ></TransferPropertiesList> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
                <el-table-column
                    key="rwid"
                    label="任务ID"
                    prop="rwid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sqid"
                    label="申请ID"
                    prop="sqid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="rwmc"
                    label="任务名称"
                    prop="rwmc"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="rwms"
                    label="任务描述"
                    prop="rwms"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cgxs"
                    label="采购形式"
                    prop="cgxs"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cgfs"
                    label="采购方式"
                    prop="cgfs"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="spr"
                    label="采购审批人"
                    prop="spr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sprname"
                    label="审批人姓名"
                    prop="sprname"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="djr"
                    label="登记人"
                    prop="djr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="djsj"
                    label="登记时间"
                    prop="djsj"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="notes"
                    label="备注"
                    prop="notes"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="guid"
                    label="Guid"
                    prop="guid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="netcode"
                    label="Netcode"
                    prop="netcode"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sjzt"
                    label="Sjzt"
                    prop="sjzt"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="nexter"
                    label="下一审批人|hidden"
                    prop="nexter"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="nextbm"
                    label="下一审批部门|hidden"
                    prop="nextbm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zxr"
                    label="执行人|hidden"
                    prop="zxr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="wtid"
                    label="委托ID"
                    prop="wtid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="wtlx"
                    label="委托类型"
                    prop="wtlx"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="wtdw"
                    label="委托单位"
                    prop="wtdw"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="qrsbh"
                    label="确认书编号"
                    prop="qrsbh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="qrsj"
                    label="确认时间"
                    prop="qrsj"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="isselgys"
                    label="是否选择供应商比价 0:未选择供应商比价 1:选择供应商比价"
                    prop="isselgys"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sqids"
                    label="申请ID集"
                    prop="sqids"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zxbm"
                    label="Zxbm"
                    prop="zxbm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="iswtrdecrypt"
                    label="委托人是否解密"
                    prop="iswtrdecrypt"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="wtr"
                    label="委托人工号"
                    prop="wtr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="isdjrdecrypt"
                    label="登记人是否解密"
                    prop="isdjrdecrypt"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cppriceendtime"
                    label="比价结束时间"
                    prop="cppriceendtime"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="isclinch"
                    label="比价是否成交 1:成交 0:未成交"
                    prop="isclinch"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xqqrzt"
                    label="需求确认状态  0待受理   1待确认  2已确认 3已完成 4完成后提交委托"
                    prop="xqqrzt"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cgnr"
                    label="采购内容"
                    prop="cgnr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="jscs"
                    label="技术参数"
                    prop="jscs"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="jldw"
                    label="计量单位"
                    prop="jldw"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zjlx"
                    label="资金类型"
                    prop="zjlx"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="fkfs"
                    label="付款方式"
                    prop="fkfs"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sbjg"
                    label="浙江省财建议书上报结果 0失败 1成功"
                    prop="sbjg"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sbjgsm"
                    label="上报结果说明"
                    prop="sbjgsm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by1"
                    label="备用1（判断新获取的确认书号申请受理人 是否已阅，0未阅，1已阅）"
                    prop="by1"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by2"
                    label="备用2（判断任务审核人是否已阅，0未阅，1已阅）"
                    prop="by2"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by3"
                    label="备用3（判断新获取的确认书号是否已阅，0未阅，1已阅）"
                    prop="by3"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by4"
                    label="任务的数量（受理人可以修改）"
                    prop="by4"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by5"
                    label="备用5 （判断自行验收步骤是否结束 0未结束，1已结束）"
                    prop="by5"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zxbj"
                    label="执行标记  A、(11)分散国内/单一10w+    B、(12)分散自行进口  C、(21) 零星/单一10w-   (22)询价  (23)网上竞价   D 、定点(31)电子卖场(32)  E 、(4)政府/集中"
                    prop="zxbj"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xnqrs"
                    label="判断是否为校内确认书 1是 0否"
                    prop="xnqrs"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="qrsjsr"
                    label="确认书接收人"
                    prop="qrsjsr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zxjsr"
                    label="执行接收人"
                    prop="zxjsr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="wtdljg"
                    label="委托代理机构"
                    prop="wtdljg"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="wtdlsj"
                    label="委托代理时间"
                    prop="wtdlsj"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="kbsj"
                    label="开标时间"
                    prop="kbsj"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="kbxx"
                    label="开标详细信息"
                    prop="kbxx"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zbdw"
                    label="中标单位"
                    prop="zbdw"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zbje"
                    label="中标金额"
                    prop="zbje"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="dzmcXdsj"
                    label="电子卖场-下单时间"
                    prop="dzmcXdsj"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="dzmcXx"
                    label="电子卖场-详细信息"
                    prop="dzmcXx"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="dzmcJe"
                    label="电子卖场-下单金额"
                    prop="dzmcJe"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="dzmcGys"
                    label="电子卖场-供应商"
                    prop="dzmcGys"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="bafs"
                    label="备案方式"
                    prop="bafs"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="bid"
                    label="建议书号（数据标识，15位字符串，4位年+6位单位代码+5位顺序码）"
                    prop="bid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="rwzt"
                    label="1待办、2提交成功未确认、4提交失败、50、冻结中，无法操作、5、已确认待审核、6已退回已撤销、7已退回未撤销、8已确认、9支付完成"
                    prop="rwzt"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysje"
                    label="预算金额"
                    prop="ysje"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cgml"
                    label="采购目录"
                    prop="cgml"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="iswtsl"
                    label="是否委托机构受理，1：受理，其他：未受理"
                    prop="iswtsl"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sfdd"
                    label="是否50万及以下校内定点货物、省级定点服务，1是，0否"
                    prop="sfdd"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="gcpgdw"
                    label="工程派工单位"
                    prop="gcpgdw"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="yjcgsj"
                    label="预计采购时间"
                    prop="yjcgsj"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sqrsh"
                    label="工程类申请确认人"
                    prop="sqrsh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="jsje"
                    label="工程类结算金额由申请人填写"
                    prop="jsje"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zxfs"
                    label="0校采通，1政采云"
                    prop="zxfs"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="gcrwpd"
                    label="工程任务派单员"
                    prop="gcrwpd"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="autopushxcy"
                    label="1未推送校采云，2已推送校采云"
                    prop="autopushxcy"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="wmwtid"
                    label="外贸委托表id"
                    prop="wmwtid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="wmwtdw"
                    label="外贸委托单位"
                    prop="wmwtdw"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by6"
                    label="原确认书号"
                    prop="by6"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by7"
                    label="原rwguid"
                    prop="by7"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by8"
                    label="经费负责人审核"
                    prop="by8"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by9"
                    label="部门负责人审核"
                    prop="by9"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zxqytype"
                    label="$fieldConfig.fieldComment"
                    prop="zxqytype"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zxqyratio"
                    label="$fieldConfig.fieldComment"
                    prop="zxqyratio"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="isdownloadqrs"
                    label="是否已经完成金财系统确认书下载"
                    prop="isdownloadqrs"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="iskscg"
                    label="是否快采"
                    prop="iskscg"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="dqrsbh"
                    label="大确认书号"
                    prop="dqrsbh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="isfscg"
                    label="是否分散采购且非公开招标"
                    prop="isfscg"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="isdyjk"
                    label="是否单一来源或进口"
                    prop="isdyjk"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zxwz"
                    label="1 采购执行分配  2申请人提交项目采购的技术需求"
                    prop="zxwz"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sqr"
                    label="申请人(多个申请时，用逗号间隔)"
                    prop="sqr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sqfzr"
                    label="申请项目负责人(多个申请时，用逗号间隔)"
                    prop="sqfzr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sqbmfzr"
                    label="申请部门负责人(多个申请时，用逗号间隔)"
                    prop="sqbmfzr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="qrsfpzt"
                    label="确认书分配状态"
                    prop="qrsfpzt"
                    min-width="150"
                    align="center"
                />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button
                v-hasPerm="['cg:zxcgCgrw:edit']"
                type="primary"
                size="small"
                icon="Edit"
                link
                @click="formType.view = false;handleRowEdit(scope.row.id)"
            >
              编辑
            </el-button>
            <el-button
                v-hasPerm="['cg:zxcgCgrw:delete']"
                type="danger"
                size="small"
                icon="Delete"
                link
                @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
            <el-button
              v-if="check"
              type="primary"
              icon="DocumentChecked"
              size="small"
              link
              @click="hancleRowCheck"
            >
              审核
            </el-button>
            <el-button
              v-if="view"
              type="success"
              icon="Document"
              size="small"
              link
              @click="hancleRowView"
            >
              查看
            </el-button>
            <el-button
              v-if="view"
              type="primary"
              icon="Printer"
              size="small"
              link
              @click="formType.view = true;hancleRowPrint"
            >
              打印
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="handleQuery()"
      />
    </el-card>

    <!-- 采购任务表单弹窗 -->   
    <el-drawer 
      v-model="dialog.visible" 
      :title="dialog.title" 
      append-to-body 
      size="75%"
      :before-close="handleCloseDialog"

    >
<el-card>
        <template #header>
          <Title name="XX信息">
            <div>
              <el-button
                v-if="!formType.view"
                type="primary"
                @click="handleSubmit()"
              >
                保存
              </el-button>
              <el-button v-if="!formType.view" type="danger" @click="handleSubmit()">
                提交
              </el-button>
            </div>
          </Title>
        </template>



      <el-form ref="dataFormRef" :model="formData" :rules="rules" label-width="100px"  :inline="true"
          :disabled="formType.view">
                <el-form-item label="任务ID" prop="rwid">
                      <el-input
                          v-model="formData.rwid"
                          placeholder="任务ID"
                      />
                </el-form-item>
                <el-form-item label="申请ID" prop="sqid">
                      <el-input
                          v-model="formData.sqid"
                          placeholder="申请ID"
                      />
                </el-form-item>
                <el-form-item label="任务名称" prop="rwmc">
                      <el-input
                          v-model="formData.rwmc"
                          placeholder="任务名称"
                      />
                </el-form-item>
                <el-form-item label="任务描述" prop="rwms">
                      <el-input
                          v-model="formData.rwms"
                          placeholder="任务描述"
                      />
                </el-form-item>
                <el-form-item label="采购形式" prop="cgxs">
                      <el-input
                          v-model="formData.cgxs"
                          placeholder="采购形式"
                      />
                </el-form-item>
                <el-form-item label="采购方式" prop="cgfs">
                      <el-input
                          v-model="formData.cgfs"
                          placeholder="采购方式"
                      />
                </el-form-item>
                <el-form-item label="采购审批人" prop="spr">
                      <el-input
                          v-model="formData.spr"
                          placeholder="采购审批人"
                      />
                </el-form-item>
                <el-form-item label="审批人姓名" prop="sprname">
                      <el-input
                          v-model="formData.sprname"
                          placeholder="审批人姓名"
                      />
                </el-form-item>
                <el-form-item label="登记人" prop="djr">
                      <el-input
                          v-model="formData.djr"
                          placeholder="登记人"
                      />
                </el-form-item>
                <el-form-item label="登记时间" prop="djsj">
                      <el-input
                          v-model="formData.djsj"
                          placeholder="登记时间"
                      />
                </el-form-item>
                <el-form-item label="备注" prop="notes">
                      <el-input
                          v-model="formData.notes"
                          placeholder="备注"
                      />
                </el-form-item>
                <el-form-item label="Guid" prop="guid">
                      <el-input
                          v-model="formData.guid"
                          placeholder="Guid"
                      />
                </el-form-item>
                <el-form-item label="Netcode" prop="netcode">
                      <el-input
                          v-model="formData.netcode"
                          placeholder="Netcode"
                      />
                </el-form-item>
                <el-form-item label="Sjzt" prop="sjzt">
                      <el-input
                          v-model="formData.sjzt"
                          placeholder="Sjzt"
                      />
                </el-form-item>
                <el-form-item label="下一审批人|hidden" prop="nexter">
                      <el-input
                          v-model="formData.nexter"
                          placeholder="下一审批人|hidden"
                      />
                </el-form-item>
                <el-form-item label="下一审批部门|hidden" prop="nextbm">
                      <el-input
                          v-model="formData.nextbm"
                          placeholder="下一审批部门|hidden"
                      />
                </el-form-item>
                <el-form-item label="执行人|hidden" prop="zxr">
                      <el-input
                          v-model="formData.zxr"
                          placeholder="执行人|hidden"
                      />
                </el-form-item>
                <el-form-item label="委托ID" prop="wtid">
                      <el-input
                          v-model="formData.wtid"
                          placeholder="委托ID"
                      />
                </el-form-item>
                <el-form-item label="委托类型" prop="wtlx">
                      <el-input
                          v-model="formData.wtlx"
                          placeholder="委托类型"
                      />
                </el-form-item>
                <el-form-item label="委托单位" prop="wtdw">
                      <el-input
                          v-model="formData.wtdw"
                          placeholder="委托单位"
                      />
                </el-form-item>
                <el-form-item label="确认书编号" prop="qrsbh">
                      <el-input
                          v-model="formData.qrsbh"
                          placeholder="确认书编号"
                      />
                </el-form-item>
                <el-form-item label="确认时间" prop="qrsj">
                      <el-input
                          v-model="formData.qrsj"
                          placeholder="确认时间"
                      />
                </el-form-item>
                <el-form-item label="是否选择供应商比价 0:未选择供应商比价 1:选择供应商比价" prop="isselgys">
                      <el-input
                          v-model="formData.isselgys"
                          placeholder="是否选择供应商比价 0:未选择供应商比价 1:选择供应商比价"
                      />
                </el-form-item>
                <el-form-item label="申请ID集" prop="sqids">
                      <el-input
                          v-model="formData.sqids"
                          placeholder="申请ID集"
                      />
                </el-form-item>
                <el-form-item label="Zxbm" prop="zxbm">
                      <el-input
                          v-model="formData.zxbm"
                          placeholder="Zxbm"
                      />
                </el-form-item>
                <el-form-item label="委托人是否解密" prop="iswtrdecrypt">
                      <el-input
                          v-model="formData.iswtrdecrypt"
                          placeholder="委托人是否解密"
                      />
                </el-form-item>
                <el-form-item label="委托人工号" prop="wtr">
                      <el-input
                          v-model="formData.wtr"
                          placeholder="委托人工号"
                      />
                </el-form-item>
                <el-form-item label="登记人是否解密" prop="isdjrdecrypt">
                      <el-input
                          v-model="formData.isdjrdecrypt"
                          placeholder="登记人是否解密"
                      />
                </el-form-item>
                <el-form-item label="比价结束时间" prop="cppriceendtime">
                      <el-input
                          v-model="formData.cppriceendtime"
                          placeholder="比价结束时间"
                      />
                </el-form-item>
                <el-form-item label="比价是否成交 1:成交 0:未成交" prop="isclinch">
                      <el-input
                          v-model="formData.isclinch"
                          placeholder="比价是否成交 1:成交 0:未成交"
                      />
                </el-form-item>
                <el-form-item label="需求确认状态  0待受理   1待确认  2已确认 3已完成 4完成后提交委托" prop="xqqrzt">
                      <el-input
                          v-model="formData.xqqrzt"
                          placeholder="需求确认状态  0待受理   1待确认  2已确认 3已完成 4完成后提交委托"
                      />
                </el-form-item>
                <el-form-item label="采购内容" prop="cgnr">
                      <el-input
                          v-model="formData.cgnr"
                          placeholder="采购内容"
                      />
                </el-form-item>
                <el-form-item label="技术参数" prop="jscs">
                      <el-input
                          v-model="formData.jscs"
                          placeholder="技术参数"
                      />
                </el-form-item>
                <el-form-item label="计量单位" prop="jldw">
                      <el-input
                          v-model="formData.jldw"
                          placeholder="计量单位"
                      />
                </el-form-item>
                <el-form-item label="资金类型" prop="zjlx">
                      <el-input
                          v-model="formData.zjlx"
                          placeholder="资金类型"
                      />
                </el-form-item>
                <el-form-item label="付款方式" prop="fkfs">
                      <el-input
                          v-model="formData.fkfs"
                          placeholder="付款方式"
                      />
                </el-form-item>
                <el-form-item label="浙江省财建议书上报结果 0失败 1成功" prop="sbjg">
                      <el-input
                          v-model="formData.sbjg"
                          placeholder="浙江省财建议书上报结果 0失败 1成功"
                      />
                </el-form-item>
                <el-form-item label="上报结果说明" prop="sbjgsm">
                      <el-input
                          v-model="formData.sbjgsm"
                          placeholder="上报结果说明"
                      />
                </el-form-item>
                <el-form-item label="备用1（判断新获取的确认书号申请受理人 是否已阅，0未阅，1已阅）" prop="by1">
                      <el-input
                          v-model="formData.by1"
                          placeholder="备用1（判断新获取的确认书号申请受理人 是否已阅，0未阅，1已阅）"
                      />
                </el-form-item>
                <el-form-item label="备用2（判断任务审核人是否已阅，0未阅，1已阅）" prop="by2">
                      <el-input
                          v-model="formData.by2"
                          placeholder="备用2（判断任务审核人是否已阅，0未阅，1已阅）"
                      />
                </el-form-item>
                <el-form-item label="备用3（判断新获取的确认书号是否已阅，0未阅，1已阅）" prop="by3">
                      <el-input
                          v-model="formData.by3"
                          placeholder="备用3（判断新获取的确认书号是否已阅，0未阅，1已阅）"
                      />
                </el-form-item>
                <el-form-item label="任务的数量（受理人可以修改）" prop="by4">
                      <el-input
                          v-model="formData.by4"
                          placeholder="任务的数量（受理人可以修改）"
                      />
                </el-form-item>
                <el-form-item label="备用5 （判断自行验收步骤是否结束 0未结束，1已结束）" prop="by5">
                      <el-input
                          v-model="formData.by5"
                          placeholder="备用5 （判断自行验收步骤是否结束 0未结束，1已结束）"
                      />
                </el-form-item>
                <el-form-item label="执行标记  A、(11)分散国内/单一10w+    B、(12)分散自行进口  C、(21) 零星/单一10w-   (22)询价  (23)网上竞价   D 、定点(31)电子卖场(32)  E 、(4)政府/集中" prop="zxbj">
                      <el-input
                          v-model="formData.zxbj"
                          placeholder="执行标记  A、(11)分散国内/单一10w+    B、(12)分散自行进口  C、(21) 零星/单一10w-   (22)询价  (23)网上竞价   D 、定点(31)电子卖场(32)  E 、(4)政府/集中"
                      />
                </el-form-item>
                <el-form-item label="判断是否为校内确认书 1是 0否" prop="xnqrs">
                      <el-input
                          v-model="formData.xnqrs"
                          placeholder="判断是否为校内确认书 1是 0否"
                      />
                </el-form-item>
                <el-form-item label="确认书接收人" prop="qrsjsr">
                      <el-input
                          v-model="formData.qrsjsr"
                          placeholder="确认书接收人"
                      />
                </el-form-item>
                <el-form-item label="执行接收人" prop="zxjsr">
                      <el-input
                          v-model="formData.zxjsr"
                          placeholder="执行接收人"
                      />
                </el-form-item>
                <el-form-item label="委托代理机构" prop="wtdljg">
                      <el-input
                          v-model="formData.wtdljg"
                          placeholder="委托代理机构"
                      />
                </el-form-item>
                <el-form-item label="委托代理时间" prop="wtdlsj">
                      <el-input
                          v-model="formData.wtdlsj"
                          placeholder="委托代理时间"
                      />
                </el-form-item>
                <el-form-item label="开标时间" prop="kbsj">
                      <el-input
                          v-model="formData.kbsj"
                          placeholder="开标时间"
                      />
                </el-form-item>
                <el-form-item label="开标详细信息" prop="kbxx">
                      <el-input
                          v-model="formData.kbxx"
                          placeholder="开标详细信息"
                      />
                </el-form-item>
                <el-form-item label="中标单位" prop="zbdw">
                      <el-input
                          v-model="formData.zbdw"
                          placeholder="中标单位"
                      />
                </el-form-item>
                <el-form-item label="中标金额" prop="zbje">
                      <el-input
                          v-model="formData.zbje"
                          placeholder="中标金额"
                      />
                </el-form-item>
                <el-form-item label="电子卖场-下单时间" prop="dzmcXdsj">
                      <el-input
                          v-model="formData.dzmcXdsj"
                          placeholder="电子卖场-下单时间"
                      />
                </el-form-item>
                <el-form-item label="电子卖场-详细信息" prop="dzmcXx">
                      <el-input
                          v-model="formData.dzmcXx"
                          placeholder="电子卖场-详细信息"
                      />
                </el-form-item>
                <el-form-item label="电子卖场-下单金额" prop="dzmcJe">
                      <el-input
                          v-model="formData.dzmcJe"
                          placeholder="电子卖场-下单金额"
                      />
                </el-form-item>
                <el-form-item label="电子卖场-供应商" prop="dzmcGys">
                      <el-input
                          v-model="formData.dzmcGys"
                          placeholder="电子卖场-供应商"
                      />
                </el-form-item>
                <el-form-item label="备案方式" prop="bafs">
                      <el-input
                          v-model="formData.bafs"
                          placeholder="备案方式"
                      />
                </el-form-item>
                <el-form-item label="建议书号（数据标识，15位字符串，4位年+6位单位代码+5位顺序码）" prop="bid">
                      <el-input
                          v-model="formData.bid"
                          placeholder="建议书号（数据标识，15位字符串，4位年+6位单位代码+5位顺序码）"
                      />
                </el-form-item>
                <el-form-item label="1待办、2提交成功未确认、4提交失败、50、冻结中，无法操作、5、已确认待审核、6已退回已撤销、7已退回未撤销、8已确认、9支付完成" prop="rwzt">
                      <el-input
                          v-model="formData.rwzt"
                          placeholder="1待办、2提交成功未确认、4提交失败、50、冻结中，无法操作、5、已确认待审核、6已退回已撤销、7已退回未撤销、8已确认、9支付完成"
                      />
                </el-form-item>
                <el-form-item label="预算金额" prop="ysje">
                      <el-input
                          v-model="formData.ysje"
                          placeholder="预算金额"
                      />
                </el-form-item>
                <el-form-item label="采购目录" prop="cgml">
                      <el-input
                          v-model="formData.cgml"
                          placeholder="采购目录"
                      />
                </el-form-item>
                <el-form-item label="是否委托机构受理，1：受理，其他：未受理" prop="iswtsl">
                      <el-input
                          v-model="formData.iswtsl"
                          placeholder="是否委托机构受理，1：受理，其他：未受理"
                      />
                </el-form-item>
                <el-form-item label="是否50万及以下校内定点货物、省级定点服务，1是，0否" prop="sfdd">
                      <el-input
                          v-model="formData.sfdd"
                          placeholder="是否50万及以下校内定点货物、省级定点服务，1是，0否"
                      />
                </el-form-item>
                <el-form-item label="工程派工单位" prop="gcpgdw">
                      <el-input
                          v-model="formData.gcpgdw"
                          placeholder="工程派工单位"
                      />
                </el-form-item>
                <el-form-item label="预计采购时间" prop="yjcgsj">
                      <el-input
                          v-model="formData.yjcgsj"
                          placeholder="预计采购时间"
                      />
                </el-form-item>
                <el-form-item label="工程类申请确认人" prop="sqrsh">
                      <el-input
                          v-model="formData.sqrsh"
                          placeholder="工程类申请确认人"
                      />
                </el-form-item>
                <el-form-item label="工程类结算金额由申请人填写" prop="jsje">
                      <el-input
                          v-model="formData.jsje"
                          placeholder="工程类结算金额由申请人填写"
                      />
                </el-form-item>
                <el-form-item label="0校采通，1政采云" prop="zxfs">
                      <el-input
                          v-model="formData.zxfs"
                          placeholder="0校采通，1政采云"
                      />
                </el-form-item>
                <el-form-item label="工程任务派单员" prop="gcrwpd">
                      <el-input
                          v-model="formData.gcrwpd"
                          placeholder="工程任务派单员"
                      />
                </el-form-item>
                <el-form-item label="1未推送校采云，2已推送校采云" prop="autopushxcy">
                      <el-input
                          v-model="formData.autopushxcy"
                          placeholder="1未推送校采云，2已推送校采云"
                      />
                </el-form-item>
                <el-form-item label="外贸委托表id" prop="wmwtid">
                      <el-input
                          v-model="formData.wmwtid"
                          placeholder="外贸委托表id"
                      />
                </el-form-item>
                <el-form-item label="外贸委托单位" prop="wmwtdw">
                      <el-input
                          v-model="formData.wmwtdw"
                          placeholder="外贸委托单位"
                      />
                </el-form-item>
                <el-form-item label="原确认书号" prop="by6">
                      <el-input
                          v-model="formData.by6"
                          placeholder="原确认书号"
                      />
                </el-form-item>
                <el-form-item label="原rwguid" prop="by7">
                      <el-input
                          v-model="formData.by7"
                          placeholder="原rwguid"
                      />
                </el-form-item>
                <el-form-item label="经费负责人审核" prop="by8">
                      <el-input
                          v-model="formData.by8"
                          placeholder="经费负责人审核"
                      />
                </el-form-item>
                <el-form-item label="部门负责人审核" prop="by9">
                      <el-input
                          v-model="formData.by9"
                          placeholder="部门负责人审核"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="zxqytype">
                      <el-input
                          v-model="formData.zxqytype"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="zxqyratio">
                      <el-input
                          v-model="formData.zxqyratio"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="是否已经完成金财系统确认书下载" prop="isdownloadqrs">
                      <el-input
                          v-model="formData.isdownloadqrs"
                          placeholder="是否已经完成金财系统确认书下载"
                      />
                </el-form-item>
                <el-form-item label="是否快采" prop="iskscg">
                      <el-input
                          v-model="formData.iskscg"
                          placeholder="是否快采"
                      />
                </el-form-item>
                <el-form-item label="大确认书号" prop="dqrsbh">
                      <el-input
                          v-model="formData.dqrsbh"
                          placeholder="大确认书号"
                      />
                </el-form-item>
                <el-form-item label="是否分散采购且非公开招标" prop="isfscg">
                      <el-input
                          v-model="formData.isfscg"
                          placeholder="是否分散采购且非公开招标"
                      />
                </el-form-item>
                <el-form-item label="是否单一来源或进口" prop="isdyjk">
                      <el-input
                          v-model="formData.isdyjk"
                          placeholder="是否单一来源或进口"
                      />
                </el-form-item>
                <el-form-item label="1 采购执行分配  2申请人提交项目采购的技术需求" prop="zxwz">
                      <el-input
                          v-model="formData.zxwz"
                          placeholder="1 采购执行分配  2申请人提交项目采购的技术需求"
                      />
                </el-form-item>
                <el-form-item label="申请人(多个申请时，用逗号间隔)" prop="sqr">
                      <el-input
                          v-model="formData.sqr"
                          placeholder="申请人(多个申请时，用逗号间隔)"
                      />
                </el-form-item>
                <el-form-item label="申请项目负责人(多个申请时，用逗号间隔)" prop="sqfzr">
                      <el-input
                          v-model="formData.sqfzr"
                          placeholder="申请项目负责人(多个申请时，用逗号间隔)"
                      />
                </el-form-item>
                <el-form-item label="申请部门负责人(多个申请时，用逗号间隔)" prop="sqbmfzr">
                      <el-input
                          v-model="formData.sqbmfzr"
                          placeholder="申请部门负责人(多个申请时，用逗号间隔)"
                      />
                </el-form-item>
                <el-form-item label="确认书分配状态" prop="qrsfpzt">
                      <el-input
                          v-model="formData.qrsfpzt"
                          placeholder="确认书分配状态"
                      />
                </el-form-item>
      </el-form>
      <template #footer></template>
     </el-card>
    </ el-drawer>
  </div>
</template>

<script setup lang="ts">
  defineOptions({
    name: "ZxcgCgrw",
    inheritAttrs: false,
  });
  import { useUserStore } from "@/store";
  import ZxcgCgrwAPI, { ZxcgCgrwPageVO, ZxcgCgrwForm, ZxcgCgrwPageQuery } from "@/api/cg/zxcg-cgrw";

  // 获取路由参数,view/add/check
  const route = useRoute();
  const type = ref(route.query.type?.toString() || "view");
  //显示隐藏权限,用于v-if
  const add = type.value == "add";
  const view = type.value == "view";
  const check = type.value == "check";
  //页面参数
  const queryFormRef = ref(ElForm);
  const loading = ref(false);
  const removeIds = ref<number[]>([]);
  const total = ref(0);

  const queryParams = reactive<ZxcgCgrwPageQuery>({
    pageNum: 1,
    pageSize: 10,
  });

  // 采购任务表格数据
  const pageData = ref<ZxcgCgrwPageVO[]>([]);

  // 弹窗
  const dialog = reactive({
    title: "",
    visible: false,
  });



  /** 查询采购任务 */
  function handleQuery() {
    loading.value = true;
          ZxcgCgrwAPI.getPage(queryParams)
        .then((data) => {
          pageData.value = data.list;
          total.value = data.total;
        })
        .finally(() => {
          loading.value = false;
        });
  }

  /** 重置采购任务查询 */
  function handleResetQuery() {
    queryFormRef.value!.resetFields();
    queryParams.pageNum = 1;
    handleQuery();
  }

  /** 行复选框选中记录选中ID集合 */
  function handleSelectionChange(selection: any) {
    removeIds.value = selection.map((item: any) => item.id);
  }

  /** 打开采购任务弹窗 */
  function handleEdit(id?: number) {
    dialog.visible = true;
    if (id) {
      dialog.title = "修改采购任务";
            ZxcgCgrwAPI.getFormData(id).then((data) => {
        Object.assign(formData, data);
      });
    } else {
      dialog.title = "新增采购任务";
    }
  }

 

  /** 关闭采购任务弹窗 */
  function handleCloseDialog() {
    dialog.visible = false;
    dataFormRef.value.resetFields();
    dataFormRef.value.clearValidate();
    Object.assign(formData, {});

  }

  /** 删除采购任务 */
  function handleDelete(id?: number) {
    const ids = [id || removeIds.value].join(",");
    if (!ids) {
      ElMessage.warning("请勾选删除项");
      return;
    }

    ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(
        () => {
          loading.value = true;
                ZxcgCgrwAPI.deleteByIds(ids)
              .then(() => {
                ElMessage.success("删除成功");
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        },
        () => {
          ElMessage.info("已取消删除");
        }
    );
  }

  onMounted(() => {
    handleQuery();
  });

  //--------------以下是form表单相关
const dataFormRef = ref(ElForm);

  // 采购任务表单数据
  const formData = reactive<ZxcgCgrwForm>({});

const formType = reactive({
  add: false,
  edit: false,
  view: false,
});

  // 采购任务表单校验规则
  const rules = reactive({
                      sqid: [{ required: true, message: "请输入申请ID", trigger: "blur" }],
                      rwmc: [{ required: true, message: "请输入任务名称", trigger: "blur" }],
                      guid: [{ required: true, message: "请输入Guid", trigger: "blur" }],
                      netcode: [{ required: true, message: "请输入Netcode", trigger: "blur" }],
                      sjzt: [{ required: true, message: "请输入Sjzt", trigger: "blur" }],
  });

   /** 提交采购任务表单 */
  function handleSubmit() {
    dataFormRef.value.validate((valid: any) => {
      if (valid) {
        loading.value = true;
        const id = formData.id;
        if (id) {
                ZxcgCgrwAPI.update(id, formData)
              .then(() => {
                ElMessage.success("修改成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        } else {
                ZxcgCgrwAPI.add(formData)
              .then(() => {
                ElMessage.success("新增成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        }
      }
    });
  }
</script>
<style lang="scss" scoped>

</style>
