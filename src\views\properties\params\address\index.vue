<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="存放地点编码" prop="ycodename">
          <el-input
            v-model="queryParams.preCode"
            placeholder="存放地点编码"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="存放地点名称" prop="ycodename">
          <el-input
            v-model="queryParams.name"
            placeholder="存放地点名称"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item>
          <el-button class="filter-item" type="primary" icon="search" @click="handleQuery">
            搜索
          </el-button>
          <el-button icon="refresh" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <div class="mb-10px">
        <el-button
          v-hasPerm="['sys:dept:add']"
          type="success"
          icon="plus"
          @click="handleOpenDialog()"
        >
          新增
        </el-button>
        <el-button
          v-hasPerm="['sys:dept:delete']"
          type="danger"
          :disabled="selectIds.length === 0"
          icon="delete"
          @click="handleDelete()"
        >
          禁用
        </el-button>
      </div>
      <el-table
        v-loading="loading"
        :data="AcodeList"
        row-key="id"
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="80" align="center" />

        <el-table-column prop="name" label="存放地点名称" min-width="200" />
        <el-table-column prop="xcode" label="存放地点编码" min-width="200" />
        <el-table-column prop="flag" label="状态" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.flag == 1" type="success">正常</el-tag>
            <el-tag v-else type="info">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="left" width="200">
          <template #default="scope">
            <el-button
              type="primary"
              link
              size="small"
              icon="plus"
              @click.stop="handleOpenDialog(scope.row.id, undefined)"
            >
              新增
            </el-button>
            <el-button
              type="primary"
              link
              size="small"
              icon="edit"
              @click.stop="handleOpenDialog(scope.row.parentId, scope.row.id)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              link
              size="small"
              icon="delete"
              @click.stop="handleDelete(scope.row.id)"
            >
              禁用
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      width="600px"
      @closed="handleCloseDialog"
    >
      <el-form ref="AcodeFormRef" :model="formData" :rules="rules" label-width="80px">
        <el-form-item label="上级存放地点" prop="parentid">
          <el-tree-select
            v-model="formData.parentid"
            placeholder="选择上级存放地点"
            :data="AcodeOptions"
            filterable
            check-strictly
            :render-after-expand="false"
          />
        </el-form-item>
        <el-form-item label="存放地点名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入存放地点名称" />
        </el-form-item>
        <el-form-item label="存放地点编号" prop="xcode">
          <el-input v-model="formData.xcode" placeholder="请输入存放地点编号" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="formData.flag">
            <el-radio :value="1">正常</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
          <el-button @click="handleCloseDialog">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Dept",
  inheritAttrs: false,
});

import SysAcodeAPI, {
  SysAcodeForm,
  SysAcodePageVO,
  SysAcodePageQuery,
} from "@/api/system/sys-acode";

const queryFormRef = ref(ElForm);
const queryParams = reactive<SysAcodePageQuery>({
  pageNum: 0,
  pageSize: 10,
});
const loading = ref(false);
const selectIds = ref<number[]>([]);
const AcodeList = ref<SysAcodePageVO[]>();
const AcodeOptions = ref<OptionType[]>();
const AcodeFormRef = ref(ElForm);
const dialog = reactive({
  title: "",
  visible: false,
});
const formData = reactive<SysAcodeForm>({});
const rules = reactive({
  parentId: [{ required: true, message: "上级存放地点不能为空", trigger: "change" }],
  xcode: [{ required: true, message: "存放地点编号不能为空", trigger: "blur" }],
  name: [{ required: true, message: "存放地点名称不能为空", trigger: "blur" }],
});

// 处理选中项变化
function handleSelectionChange(selection: any) {
  selectIds.value = selection.map((item: any) => item.id);
}

// 重置查询
function handleResetQuery() {
  queryFormRef.value.resetFields();
  handleQuery();
}

/**
 * 打开弹窗
 */
async function handleOpenDialog(parentId?: number, id?: number) {
  console.log("handleOpenDialog");
  // 加载下拉数据
  await SysAcodeAPI.getOptions().then((data) => {
    console.log("获取到数据");
    AcodeOptions.value = [
      {
        value: "0",
        label: "顶级存放地点",
        children: data,
      },
    ];
    console.log("渲染数据");
  });
  console.log("绑定数据");
  dialog.visible = true;
  if (id) {
    dialog.title = "修改存放地点";
    SysAcodeAPI.getFormData(id).then((data) => {
      Object.assign(formData, data);
      console.log("parentId," + formData.parentid + "," + data.parentid);
    });
  } else {
    dialog.title = "新增存放地点";
    formData.parentid = parentId || 0;
    formData.flag = 1;
    console.log("绑定数据完成");
  }
}

// 删除存放地点
function handleDelete(AcodeID?: number) {
  const AcodeIDs = [AcodeID || selectIds.value].join(",");

  if (!AcodeIDs) {
    ElMessage.warning("请勾选禁用项");
    return;
  }

  ElMessageBox.confirm("确认禁用已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      SysAcodeAPI.deleteByIds(AcodeIDs)
        .then(() => {
          ElMessage.success("禁用成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消操作");
    }
  );
}

// 关闭弹窗
function handleCloseDialog() {
  dialog.visible = false;
  queryFormRef.value.resetFields();
  queryFormRef.value.clearValidate();

  formData.id = undefined;
  formData.parentid = 0;
  formData.flag = 1;
  //handleQuery();
}

// 提交表单
function handleSubmit() {
  console.log("提交");
  console.log(formData);
  AcodeFormRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      const deptId = formData.id;
      if (deptId) {
        SysAcodeAPI.update(deptId, formData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
            handleQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        SysAcodeAPI.add(formData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
            handleQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}

// 查询
function handleQuery() {
  loading.value = true;
  SysAcodeAPI.getList(queryParams).then((data) => {
    AcodeList.value = data;
    loading.value = false;
  });
}

onMounted(() => {
  handleQuery();
});
</script>
