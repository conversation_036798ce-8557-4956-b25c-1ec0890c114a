<template>
  <div class="digital-asset-platform-container">
    <!-- 头部区域：标题、导航 -->
    <div class="header-wrapper">
      <div class="header">
        <div class="header-left">
          <img :src="gykjLogo" alt="中国美术学院 logo" class="gykj-logo" />
          <h1 class="title">数字化资产管理平台</h1>
        </div>
        <div class="header-right">
          <div class="navbar__right">
            <span class="platform-link" @click="router.push('/warehouse')">
              <el-icon class="platform-icon"><HomeFilled /></el-icon>
              <span>首页</span>
            </span>

            <span class="platform-link" @click="router.push('/warehouse/analysis/Warehousebook')">
              <el-icon class="platform-icon"><Bell /></el-icon>
              <span>通知公告</span>
            </span>

            <span class="platform-link" @click="router.push('/dashboard')">
              <el-icon class="platform-icon"><Setting /></el-icon>
              <span>后台管理</span>
            </span>

            <!-- 用户头像（个人中心、注销登录等） -->
            <div class="user-profile-container">
              <UserProfile v-if="isLogin" />
              <span
                style="font-size: 14px; color: #888888; cursor: pointer"
                v-else
                @click="router.push('/login')"
              >
                登录
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 功能模块区域 -->
      <div class="modules-container">
        <!-- 资产管理系统 -->
        <div class="module-card">
          <div class="module-icon">
            <img :src="assetManagementIcon" alt="资产管理系统" class="icon-image" />
          </div>
          <div class="module-content">
            <h3 class="module-title">资产管理系统</h3>
            <p class="module-description">资产登记、资产台账、资产使用、资产调拨、资产处置等</p>
          </div>
          <el-button
            type="primary"
            class="module-button"
            @click="navigateToModule('asset-management')"
          >
            点击进入 >>
          </el-button>
        </div>

        <!-- 物品管理系统 -->
        <div class="module-card">
          <div class="module-icon">
            <img :src="goodsManagementIcon" alt="物品管理系统" class="icon-image" />
          </div>
          <div class="module-content">
            <h3 class="module-title">物品管理系统</h3>
            <p class="module-description">低值品、易耗品、材料的入库和领用管理等</p>
          </div>
          <el-button
            type="primary"
            class="module-button"
            @click="navigateToModule('goods-management')"
          >
            点击进入 >>
          </el-button>
        </div>

        <!-- 资产公物仓 -->
        <div class="module-card">
          <div class="module-icon">
            <img :src="assetWarehouseIcon" alt="资产公物仓" class="icon-image" />
          </div>
          <div class="module-content">
            <h3 class="module-title">资产公物仓</h3>
            <p class="module-description">
              资产入仓、资产申请（出仓）、资产盘点、公物仓数据分析和统计等
            </p>
          </div>
          <el-button
            type="primary"
            class="module-button"
            @click="navigateToModule('asset-warehouse')"
          >
            点击进入 >>
          </el-button>
        </div>

        <!-- 资产驾驶舱 -->
        <div class="module-card">
          <div class="module-icon">
            <img :src="assetDashboardIcon" alt="资产驾驶舱" class="icon-image" />
          </div>
          <div class="module-content">
            <h3 class="module-title">资产驾驶舱</h3>
            <p class="module-description">
              资产总览、资产分类统计、资产变动统计、资产警示、资产排行等
            </p>
          </div>
          <el-button
            type="primary"
            class="module-button"
            @click="navigateToModule('asset-dashboard')"
          >
            点击进入 >>
          </el-button>
        </div>
      </div>

      <!-- 侧边栏信息区域 -->
      <div class="sidebar-container">
        <!-- 通知公告与公物仓列表 -->
        <el-card class="sidebar-card" shadow="never">
          <el-tabs v-model="activeTab" class="info-tabs">
            <!-- 公物仓列表标签页 -->
            <el-tab-pane label="公物仓列表" name="warehouse">
              <div class="warehouse-list">
                <div
                  v-for="(item, index) in warehouseList"
                  :key="index"
                  class="warehouse-item"
                  @click="viewWarehouseDetail(item)"
                >
                  <div class="warehouse-status" :class="item.status">
                    {{ getWarehouseStatusLabel(item.status) }}
                  </div>
                  <div class="warehouse-content">
                    <div class="warehouse-title">{{ item.name }}</div>
                    <div class="warehouse-info">
                      <span>库存: {{ item.inventory }}</span>
                      <span class="warehouse-date">{{ item.updateTime }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <!-- 通知公告标签页 -->
            <el-tab-pane label="通知公告" name="notices">
              <div class="notice-list">
                <div
                  v-for="(notice, index) in noticeList"
                  :key="index"
                  class="notice-item"
                  @click="viewNoticeDetail(notice)"
                >
                  <div class="notice-tag" :class="notice.type">
                    {{ getNoticeTypeLabel(notice.type) }}
                  </div>
                  <div class="notice-content">
                    <div class="notice-title">{{ notice.title }}</div>
                    <div class="notice-date">{{ notice.date }}</div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>

        <!-- 系统信息 -->
        <el-card class="sidebar-card" shadow="never">
          <el-tabs v-model="infoActiveTab" class="info-tabs">
            <!-- 规章制度标签页 -->
            <el-tab-pane label="规章制度" name="regulations">
              <div class="regulations-list">
                <div
                  v-for="(item, index) in regulationsList"
                  :key="index"
                  class="regulation-item"
                  @click="viewRegulationDetail(item)"
                >
                  <div class="regulation-tag" :class="item.category">
                    {{ getRegulationCategoryLabel(item.category) }}
                  </div>
                  <div class="regulation-content">
                    <div class="regulation-title">{{ item.title }}</div>
                    <div class="regulation-info">
                      <span>发布部门: {{ item.department }}</span>
                      <span class="regulation-date">{{ item.publishDate }}</span>
                    </div>
                  </div>
                  <el-icon class="regulation-icon"><ArrowRight /></el-icon>
                </div>
              </div>
            </el-tab-pane>
            <!-- 资料下载标签页 -->
            <el-tab-pane label="资料下载" name="downloads">
              <div class="downloads-list">
                <div
                  v-for="(file, index) in downloadsList"
                  :key="index"
                  class="download-item"
                  @click="downloadFile(file)"
                >
                  <div class="file-icon" :class="getFileIconClass(file.type)">
                    <el-icon v-if="file.type === 'pdf'"><Document /></el-icon>
                    <el-icon v-else-if="file.type === 'excel'"><Document /></el-icon>
                    <el-icon v-else-if="file.type === 'word'"><Document /></el-icon>
                    <el-icon v-else><Document /></el-icon>
                  </div>
                  <div class="download-content">
                    <div class="download-title">{{ file.name }}</div>
                    <div class="download-info">
                      <span>{{ file.size }}</span>
                      <span class="download-date">{{ file.uploadDate }}</span>
                    </div>
                  </div>
                  <el-button type="primary" size="small" class="download-btn">
                    <el-icon><Download /></el-icon>
                    下载
                  </el-button>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </div>
    </div>

    <!-- 底部版权信息 -->
    <div class="footer-copyright">
      <div class="footer-container">
        <img :src="gykjLogo" alt="XXXXXXXXXXX" class="footer-logo" />
        <div class="divider"></div>
        <div class="copyright-content">
          <div class="copyright-text">
            <el-icon><Location /></el-icon>
            版权所有：XXXXXXXXXX
          </div>
          <div class="copyright-text">
            <el-icon><MapLocation /></el-icon>
            地址：XXXXXXXXXXXXXXXX
          </div>
          <div class="copyright-text">
            <el-icon><Service /></el-icon>
            技术支持：浙江工越信息科技有限公司
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import {
  HomeFilled,
  Bell,
  Setting,
  User,
  SwitchButton,
  Monitor,
  Operation,
  Plus,
  Search,
  Document,
  Download,
  Location,
  MapLocation,
  Service,
  ArrowRight,
} from "@element-plus/icons-vue";
import router from "@/router";
import { getToken } from "@/utils/auth";
import gykjLogo from "@/assets/GYKJlogo.png";
import assetManagementIconSrc from "@/assets/images/sys1.png";
import goodsManagementIconSrc from "@/assets/images/sys2.png";
import assetWarehouseIconSrc from "@/assets/images/sys3.png";
import assetDashboardIconSrc from "@/assets/images/sys4.png";
import UserProfile from "@/layout/components/NavBar/components/UserProfile.vue";
// 当前激活的菜单和标签页
const activeMenu = ref("1");
const activeTab = ref("warehouse");
const infoActiveTab = ref("regulations");

// 模块图标
const assetManagementIcon = ref(assetManagementIconSrc);
const goodsManagementIcon = ref(goodsManagementIconSrc);
const assetWarehouseIcon = ref(assetWarehouseIconSrc);
const assetDashboardIcon = ref(assetDashboardIconSrc);

// 通知公告列表
const noticeList = reactive([
  {
    id: 1,
    type: "urgent",
    title: "【202001172】资产处置申报",
    date: "2024-01-15",
  },
  {
    id: 2,
    type: "normal",
    title: "【202001171】资产处置申报",
    date: "2024-01-14",
  },
  {
    id: 3,
    type: "normal",
    title: "【202001168】资产处置申报",
    date: "2024-01-13",
  },
  {
    id: 4,
    type: "normal",
    title: "【202001165】资产处置申报",
    date: "2024-01-12",
  },
  {
    id: 5,
    type: "normal",
    title: "【202001164】资产处置申报",
    date: "2024-01-11",
  },
  {
    id: 6,
    type: "normal",
    title: "【202001163】资产处置申报",
    date: "2024-01-10",
  },
  {
    id: 7,
    type: "normal",
    title: "【202001162】资产处置申报",
    date: "2024-01-09",
  },
  {
    id: 8,
    type: "normal",
    title: "【202001159】资产处置申报",
    date: "2024-01-08",
  },
  {
    id: 9,
    type: "normal",
    title: "【202001158】资产处置申报",
    date: "2024-01-07",
  },
  {
    id: 10,
    type: "normal",
    title: "【202001157】资产处置申报",
    date: "2024-01-06",
  },
]);

// 公物仓列表
const warehouseList = reactive([
  {
    id: 1,
    name: "美术学院公物仓A区",
    status: "available",
    inventory: "324件",
    updateTime: "2024-06-20",
  },
  {
    id: 2,
    name: "设计学院公物仓",
    status: "full",
    inventory: "512件",
    updateTime: "2024-06-19",
  },
  {
    id: 3,
    name: "建筑学院公物仓",
    status: "maintenance",
    inventory: "198件",
    updateTime: "2024-06-18",
  },
  {
    id: 4,
    name: "雕塑系公物仓",
    status: "available",
    inventory: "156件",
    updateTime: "2024-06-17",
  },
  {
    id: 5,
    name: "国画系公物仓",
    status: "available",
    inventory: "243件",
    updateTime: "2024-06-16",
  },
  {
    id: 6,
    name: "油画系公物仓",
    status: "available",
    inventory: "187件",
    updateTime: "2024-06-15",
  },
  {
    id: 7,
    name: "版画系公物仓",
    status: "low",
    inventory: "89件",
    updateTime: "2024-06-14",
  },
  {
    id: 8,
    name: "综合材料工作室公物仓",
    status: "available",
    inventory: "276件",
    updateTime: "2024-06-13",
  },
]);

// 规章制度列表
const regulationsList = reactive([
  {
    id: 1,
    title: "资产管理办法（2024年修订版）",
    category: "policy",
    department: "资产管理处",
    publishDate: "2024-05-15",
    content: "关于学校资产管理的总体规定和要求...",
  },
  {
    id: 2,
    title: "固定资产报废处置流程",
    category: "procedure",
    department: "资产管理处",
    publishDate: "2024-04-20",
    content: "固定资产报废处置的申请流程和审批要求...",
  },
  {
    id: 3,
    title: "公物仓管理规定",
    category: "policy",
    department: "资产管理处",
    publishDate: "2024-03-10",
    content: "关于公物仓日常管理和使用的规定...",
  },
  {
    id: 4,
    title: "资产盘点工作指南",
    category: "guide",
    department: "资产管理处",
    publishDate: "2024-02-25",
    content: "年度资产盘点工作的具体实施方法...",
  },
  {
    id: 5,
    title: "低值易耗品管理办法",
    category: "policy",
    department: "资产管理处",
    publishDate: "2024-01-18",
    content: "关于低值易耗品的采购、使用和管理规定...",
  },
  {
    id: 6,
    title: "资产调拨操作指南",
    category: "guide",
    department: "资产管理处",
    publishDate: "2023-12-05",
    content: "资产在不同部门间调拨的操作流程...",
  },
]);

// 资料下载列表
const downloadsList = reactive([
  {
    id: 1,
    name: "资产登记表.xlsx",
    type: "excel",
    size: "1.2MB",
    uploadDate: "2024-06-10",
    url: "#",
  },
  {
    id: 2,
    name: "资产处置申请表.docx",
    type: "word",
    size: "0.8MB",
    uploadDate: "2024-05-22",
    url: "#",
  },
  {
    id: 3,
    name: "公物仓物品入库单.pdf",
    type: "pdf",
    size: "0.5MB",
    uploadDate: "2024-05-15",
    url: "#",
  },
  {
    id: 4,
    name: "资产管理系统使用手册.pdf",
    type: "pdf",
    size: "3.5MB",
    uploadDate: "2024-04-30",
    url: "#",
  },
  {
    id: 5,
    name: "资产盘点表模板.xlsx",
    type: "excel",
    size: "1.0MB",
    uploadDate: "2024-04-12",
    url: "#",
  },
  {
    id: 6,
    name: "物品领用申请表.docx",
    type: "word",
    size: "0.7MB",
    uploadDate: "2024-03-25",
    url: "#",
  },
]);

// 系统信息
const systemInfo = reactive({
  userCount: "1,234",
  assetCount: "56,789",
  todayNew: "23",
  version: "v2.1.0",
});

/**
 * 菜单选择处理
 */
const handleMenuSelect = (index: string) => {
  activeMenu.value = index;
};

/**
 * 导航到模块
 */
const navigateToModule = (module: string) => {
  switch (module) {
    case "asset-management":
      ElMessage.info("跳转到资产管理系统");
      // router.push("/asset-management");
      break;
    case "goods-management":
      ElMessage.info("跳转到物品管理系统");
      // router.push("/goods-management");
      break;
    case "asset-warehouse":
      router.push("/warehouse");
      break;
    case "asset-dashboard":
      ElMessage.info("跳转到资产驾驶舱");
      // router.push("/asset-dashboard");
      break;
    default:
      ElMessage.warning("功能开发中...");
  }
};

/**
 * 获取通知类型标签
 */
const getNoticeTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    urgent: "紧急",
    normal: "通知",
    info: "信息",
  };
  return typeMap[type] || "通知";
};

/**
 * 查看通知详情
 */
const viewNoticeDetail = (notice: any) => {
  ElMessage.info(`查看通知：${notice.title}`);
};

/**
 * 获取公物仓状态标签
 */
const getWarehouseStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    available: "可用",
    full: "已满",
    maintenance: "维护中",
    low: "低库存",
  };
  return statusMap[status] || "未知";
};

/**
 * 查看公物仓详情
 */
const viewWarehouseDetail = (warehouse: any) => {
  ElMessage.info(`查看公物仓：${warehouse.name}`);
};

/**
 * 获取规章制度类别标签
 */
const getRegulationCategoryLabel = (category: string) => {
  const categoryMap: Record<string, string> = {
    policy: "政策",
    procedure: "流程",
    guide: "指南",
    notice: "通知",
  };
  return categoryMap[category] || "其他";
};

/**
 * 查看规章制度详情
 */
const viewRegulationDetail = (regulation: any) => {
  ElMessage.info(`查看规章制度：${regulation.title}`);
  // 这里可以添加打开规章制度详情的逻辑，如弹窗或跳转
};

/**
 * 获取文件图标类名
 */
const getFileIconClass = (fileType: string) => {
  const typeMap: Record<string, string> = {
    pdf: "file-pdf",
    excel: "file-excel",
    word: "file-word",
    image: "file-image",
  };
  return typeMap[fileType] || "file-unknown";
};

/**
 * 下载文件
 */
const downloadFile = (file: any) => {
  ElMessage.success(`正在下载：${file.name}`);
  // 这里可以添加实际的文件下载逻辑
};

/**
 * 快捷操作
 */
const quickAction = (action: string) => {
  const actionMap: Record<string, string> = {
    "asset-register": "资产登记",
    "asset-search": "资产查询",
    "asset-report": "报表导出",
    "system-backup": "数据备份",
  };
  ElMessage.info(`执行操作：${actionMap[action]}`);
};
const isLogin = ref(false);
// 页面加载时初始化数据
onMounted(() => {
  isLogin.value = !!getToken();
  // 可以在这里加载一些初始数据
});
</script>

<style lang="scss" scoped>
// 导航栏优化样式
.navbar__right {
  display: flex;
  align-items: center;
  height: 64px;

  .platform-link {
    position: relative;
    display: inline-flex;
    align-items: center;
    min-width: 40px;
    height: 64px;
    padding: 0 16px;
    color: var(--el-text-color);
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0;

    &:hover {
      background-color: rgba(64, 158, 255, 0.1);
      color: var(--el-color-primary);
      transform: translateY(-1px);

      .platform-icon {
        transform: scale(1.1);
      }
    }

    // 选中状态样式
    &.active {
      color: var(--el-color-primary);
      background-color: rgba(64, 158, 255, 0.08);
      font-weight: 600;

      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background-color: var(--el-color-primary);
      }
    }

    .platform-icon {
      margin-right: 6px;
      font-size: 16px;
      transition: transform 0.2s ease;
    }

    // 徽章样式
    .receive-badge {
      position: absolute;
      top: 8px;
      right: 8px;
      z-index: 1;

      :deep(.el-badge__content) {
        font-size: 10px;
        padding: 0 4px;
        height: 16px;
        line-height: 16px;
        border-radius: 8px;
      }
    }
  }

  // 用户头像区域与导航项分隔
  .user-profile-container {
    margin-left: 12px;
    padding-left: 12px;
    border-left: 1px solid var(--el-border-color-light);
    height: 64px;
    display: flex;
    align-items: center;
  }
}

// 响应式调整
@media (max-width: 1200px) {
  .header {
    max-width: 100%;
    padding: 0 16px;
  }

  .main-content {
    max-width: 100%;
    padding: 20px 16px 40px;
  }
}

@media (max-width: 992px) {
  .navbar__right {
    .platform-link {
      padding: 0 12px;
      font-size: 13px;

      .platform-icon {
        font-size: 15px;
      }
    }
  }

  .title {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 0 12px;
  }

  .main-content {
    padding: 16px 12px 32px;
  }

  .navbar__right {
    .platform-link span {
      display: none; // 小屏幕隐藏文字，只保留图标
    }

    .platform-link {
      padding: 0 8px;
      min-width: 32px;

      .platform-icon {
        margin-right: 0;
        font-size: 16px;
      }
    }

    .user-profile-container {
      margin-left: 8px;
      padding-left: 8px;
    }
  }

  .gykj-logo {
    height: 32px;
    margin-right: 12px;
  }

  .title {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .header-left {
    .title {
      display: none; // 超小屏幕隐藏标题
    }
  }

  .navbar__right {
    .platform-link {
      padding: 0 6px;
      min-width: 28px;
    }
  }
}

.digital-asset-platform-container {
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
}

// 头部样式
.header-wrapper {
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.header {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
}

.gykj-logo {
  height: 40px;
  margin-right: 15px;
}

.title {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin: 0;
}

.el-menu-demo {
  border-bottom: none;
  background-color: transparent;
}

// 主内容区域
.main-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px 20px;
  display: flex;
  gap: 30px;
  min-height: auto;
}

// 模块容器
.modules-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

// 模块卡片
.module-card {
  background: #ffffff;
  height: 180px;
  border-radius: 16px;
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #ebeef5;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

  .module-icon {
    width: 120px;
    height: 120px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.3s ease;
    margin-right: 10px;

    .icon-image {
      width: 120px;
      height: 120px;
      object-fit: contain;
      transition: all 0.3s ease;
    }
  }

  .module-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .module-title {
      font-size: 24px;
      font-weight: 600;
      color: #2c5aa0;
      margin: 0;
    }

    .module-description {
      font-size: 16px;
      color: #666;
      margin: 0;
      line-height: 1.5;
    }

    .module-button {
      align-self: flex-start;
      margin-top: 10px;
      background: #409eff;
      border: none;
      border-radius: 8px;
      padding: 10px 20px;
      font-weight: 500;
      transition: all 0.3s ease;
      box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);

      &:hover {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        transform: translateY(-2px);
      }
    }
  }
}

// 侧边栏容器
.sidebar-container {
  width: 350px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex-shrink: 0;
}

// 侧边栏卡片
.sidebar-card {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  :deep(.el-card__header) {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  :deep(.el-card__body) {
    padding: 20px;
  }

  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #303133;

    .el-button {
      margin-left: auto;
      color: #409eff;
    }
  }
}

// 标签页样式
.info-tabs {
  :deep(.el-tabs__header) {
    margin-bottom: 15px;
  }

  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
  }

  :deep(.el-tabs__item) {
    font-size: 14px;
    height: 36px;
    line-height: 36px;
  }
}

// 通知列表
.notice-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;

  .notice-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    cursor: pointer;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(64, 158, 255, 0.05);
      border-radius: 6px;
      padding: 8px 10px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
    }

    &:last-child {
      border-bottom: none;
    }

    .notice-tag {
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      color: white;
      flex-shrink: 0;

      &.urgent {
        background-color: #f56c6c;
      }

      &.normal {
        background-color: #409eff;
      }

      &.info {
        background-color: #909399;
      }
    }

    .notice-content {
      flex: 1;
      min-width: 0;

      .notice-title {
        font-size: 14px;
        color: #303133;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .notice-date {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }
  }
}

// 规章制度列表
.regulations-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;

  .regulation-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    border-radius: 6px;

    &:hover {
      background-color: rgba(64, 158, 255, 0.05);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
    }

    &:last-child {
      border-bottom: none;
    }

    .regulation-tag {
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      color: white;
      flex-shrink: 0;

      &.policy {
        background-color: #409eff;
      }

      &.procedure {
        background-color: #67c23a;
      }

      &.guide {
        background-color: #e6a23c;
      }

      &.notice {
        background-color: #909399;
      }
    }

    .regulation-content {
      flex: 1;
      min-width: 0;

      .regulation-title {
        font-size: 14px;
        color: #303133;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 500;
      }

      .regulation-info {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
      }
    }

    .regulation-icon {
      color: #c0c4cc;
      font-size: 16px;
      margin-left: 5px;
    }
  }
}

// 资料下载列表
.downloads-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;

  .download-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    border-radius: 6px;

    &:hover {
      background-color: rgba(64, 158, 255, 0.05);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
    }

    &:last-child {
      border-bottom: none;
    }

    .file-icon {
      width: 36px;
      height: 36px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      &.file-pdf {
        background-color: rgba(245, 108, 108, 0.1);
        color: #f56c6c;
      }

      &.file-excel {
        background-color: rgba(103, 194, 58, 0.1);
        color: #67c23a;
      }

      &.file-word {
        background-color: rgba(64, 158, 255, 0.1);
        color: #409eff;
      }

      &.file-unknown {
        background-color: rgba(144, 147, 153, 0.1);
        color: #909399;
      }

      .el-icon {
        font-size: 20px;
      }
    }

    .download-content {
      flex: 1;
      min-width: 0;

      .download-title {
        font-size: 14px;
        color: #303133;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 500;
      }

      .download-info {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
      }
    }

    .download-btn {
      flex-shrink: 0;
      padding: 6px 12px;
      font-size: 12px;

      .el-icon {
        margin-right: 4px;
      }
    }
  }
}

// 公物仓列表
.warehouse-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;

  .warehouse-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    cursor: pointer;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(64, 158, 255, 0.05);
      border-radius: 6px;
      padding: 8px 10px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
    }

    &:last-child {
      border-bottom: none;
    }

    .warehouse-status {
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      color: white;
      flex-shrink: 0;

      &.available {
        background-color: #67c23a;
      }

      &.full {
        background-color: #e6a23c;
      }

      &.maintenance {
        background-color: #909399;
      }

      &.low {
        background-color: #f56c6c;
      }
    }

    .warehouse-content {
      flex: 1;
      min-width: 0;

      .warehouse-title {
        font-size: 14px;
        color: #303133;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .warehouse-info {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }
  }
}

// 系统信息
.system-info {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    &:last-child {
      border-bottom: none;
    }

    .info-label {
      font-size: 14px;
      color: #606266;
    }

    .info-value {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }
  }
}

// 快捷操作
.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;

  .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px;
    background-color: rgba(64, 158, 255, 0.05);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);

    &:hover {
      background-color: rgba(64, 158, 255, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);
    }

    .el-icon {
      font-size: 20px;
      color: #409eff;
    }

    span {
      font-size: 12px;
      color: #606266;
      text-align: center;
    }
  }
}

// 底部版权信息
.footer-copyright {
  background-color: #0078d4;
  color: white;
  padding: 15px 0;
  margin-top: 30px;
  width: 100%;
  height: 200px;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
}

.footer-logo {
  height: 40px;
  margin-right: 20px;
}

.divider {
  width: 1px;
  height: 100px;
  background-color: #ffffff;
  margin: 0 20px;
}

.copyright-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.copyright-text {
  font-size: 14px;
  color: #ffffff;
  display: flex;
  align-items: center;

  .el-icon {
    margin-right: 5px;
    color: #ffffff;
  }
}

// 响应式布局
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    gap: 20px;
  }

  .sidebar-container {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 20px 10px;
  }

  .module-card {
    flex-direction: column;
    text-align: center;
    padding: 20px;

    .module-icon {
      width: 60px;
      height: 60px;
    }

    .module-content {
      .module-title {
        font-size: 20px;
      }

      .module-description {
        font-size: 14px;
      }
    }
  }

  .sidebar-container {
    grid-template-columns: 1fr;
  }
}
</style>
