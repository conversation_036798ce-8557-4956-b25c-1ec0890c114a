import request from "@/utils/request";

const SYSBRANCHINFO_BASE_URL = "/api/v1/sysBranchinfos";

const SysBranchinfoAPI = {
  /** 获取sysbranch分页数据 */
  getPage(queryParams?: SysBranchinfoPageQuery) {
    return request<any, PageResult<SysBranchinfoPageVO[]>>({
      url: `${SYSBRANCHINFO_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取sysbranch表单数据
   *
   * @param id SysBranchinfoID
   * @returns SysBranchinfo表单数据
   */
  getFormData(id: number) {
    return request<any, SysBranchinfoForm>({
      url: `${SYSBRANCHINFO_BASE_URL}/${id}/form`,
      method: "get",
    });
  },

  /** 添加sysbranch*/
  add(data: SysBranchinfoForm) {
    return request({
      url: `${SYSBRANCHINFO_BASE_URL}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新sysbranch
   *
   * @param id SysBranchinfoID
   * @param data SysBranchinfo表单数据
   */
  update(id: number, data: SysBranchinfoForm) {
    return request({
      url: `${SYSBRANCHINFO_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 批量删除sysbranch，多个以英文逗号(,)分割
   *
   * @param ids sysbranchID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${SYSBRANCHINFO_BASE_URL}/${ids}`,
      method: "delete",
    });
  },
};

export default SysBranchinfoAPI;

/** sysbranch分页查询参数 */
export interface SysBranchinfoPageQuery extends PageQuery {
  /** 银行名称 */
  branchname?: string;
}

/** sysbranch表单对象 */
export interface SysBranchinfoForm {
  id?: number;
  /** 联行号 */
  branchcode?: string;
  /** 银行名称 */
  branchname?: string;
}

/** sysbranch分页对象 */
export interface SysBranchinfoPageVO {
  id?: number;
  /** 联行号 */
  branchcode?: string;
  /** 银行名称 */
  branchname?: string;
}
