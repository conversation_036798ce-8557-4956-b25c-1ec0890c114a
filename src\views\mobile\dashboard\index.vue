<template>
  <div class="mobile-dashboard">
    <!-- 用户欢迎区域 -->
    <div class="welcome-section">
      <div class="user-info">
        <div class="avatar">
          <img :src="userStore.userInfo.avatar || defaultAvatar" alt="头像" />
        </div>
        <div class="greeting">
          <div class="welcome-text">您好，{{ userStore.userInfo.nickname }}老师</div>
          <div class="time-text">{{ currentTimeText }}</div>
        </div>
      </div>
    </div>

    <!-- 资产统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-number">{{ tj_data[0]?.SL || 0 }}</div>
          <div class="stat-label">保管资产(件)</div>
          <div class="stat-amount">{{ formatAmount(tj_data[0]?.JE || 0) }}</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ tj_data[1]?.SL || 0 }}</div>
          <div class="stat-label">管辖资产(件)</div>
          <div class="stat-amount">{{ formatAmount(tj_data[1]?.JE || 0) }}</div>
        </div>
        <div class="stat-card warning">
          <div class="stat-number">{{ tj_data[2]?.SL || 0 }}</div>
          <div class="stat-label">变动中资产(件)</div>
          <div class="stat-desc">需要关注</div>
        </div>
      </div>
    </div>

    <!-- 快捷功能 -->
    <div class="quick-actions">
      <div class="section-title">快捷功能</div>
      <div class="actions-grid">
        <div class="action-item" @click="handleQuickAction('scan')">
          <div class="action-icon">
            <svg-icon icon-class="scan" size="24px" />
          </div>
          <div class="action-text">扫码查询</div>
        </div>
        <div class="action-item" @click="handleQuickAction('search')">
          <div class="action-icon">
            <svg-icon icon-class="search" size="24px" />
          </div>
          <div class="action-text">查询功能</div>
        </div>
        <div class="action-item" @click="handleQuickAction('report')">
          <div class="action-icon">
            <svg-icon icon-class="document" size="24px" />
          </div>
          <div class="action-text">报表查看</div>
        </div>
        <div class="action-item" @click="handleQuickAction('notice')">
          <div class="action-icon">
            <svg-icon icon-class="bell" size="24px" />
          </div>
          <div class="action-text">通知公告</div>
        </div>
      </div>
    </div>

    <!-- 待办事项 -->
    <div class="todo-section">
      <div class="section-title">
        <span>待办事项</span>
        <span class="more-link" @click="handleViewMore('todo')">查看更多</span>
      </div>
      <div class="todo-list">
        <div v-if="todoList.length === 0" class="empty-state">
          <svg-icon icon-class="empty" size="48px" />
          <div class="empty-text">暂无待办事项</div>
        </div>
        <div v-else v-for="item in todoList.slice(0, 3)" :key="item.id" class="todo-item">
          <div class="todo-content">
            <div class="todo-title">{{ item.title }}</div>
            <div class="todo-time">{{ formatTime(item.createTime) }}</div>
          </div>
          <div class="todo-action">
            <div class="todo-status" :class="item.status">{{ getStatusText(item.status) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 通知公告 -->
    <div class="notice-section">
      <div class="section-title">
        <span>通知公告</span>
        <span class="more-link" @click="handleViewMore('notice')">查看更多</span>
      </div>
      <div class="notice-list">
        <div v-if="noticeList.length === 0" class="empty-state">
          <svg-icon icon-class="empty" size="48px" />
          <div class="empty-text">暂无通知公告</div>
        </div>
        <div v-else v-for="item in noticeList.slice(0, 3)" :key="item.id" class="notice-item">
          <div class="notice-content">
            <div class="notice-title">{{ item.title }}</div>
            <div class="notice-time">{{ formatTime(item.createTime) }}</div>
          </div>
          <div class="notice-badge" v-if="item.isNew">
            <span>新</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";

import { useUserStore } from "@/store";
import dashboardAPI, { tjType } from "@/api/properties/dashboard";

defineOptions({
  name: "MobileDashboard",
});

const router = useRouter();
const userStore = useUserStore();

const defaultAvatar = ref(new URL("../../../assets/logo.png", import.meta.url).href);
const tj_data = ref<tjType[]>([]);
const todoList = ref<any[]>([]);
const noticeList = ref<any[]>([]);

// 当前时间文本
const currentTimeText = computed(() => {
  const hour = new Date().getHours();
  if (hour < 6) return "凌晨好";
  if (hour < 12) return "上午好";
  if (hour < 18) return "下午好";
  return "晚上好";
});

// 格式化金额
const formatAmount = (amount: number) => {
  if (amount >= 10000) {
    return (amount / 10000).toFixed(1) + "万元";
  }
  return amount.toLocaleString() + "元";
};

// 格式化时间
const formatTime = (time: string) => {
  const date = new Date(time);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (days === 0) return "今天";
  if (days === 1) return "昨天";
  if (days < 7) return `${days}天前`;
  return date.toLocaleDateString();
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: "待处理",
    processing: "处理中",
    completed: "已完成",
  };
  return statusMap[status] || status;
};

// 处理快捷功能点击
const handleQuickAction = (action: string) => {
  switch (action) {
    case "scan":
      ElMessage.info("扫码功能开发中");
      break;
    case "search":
      router.push("/mobile/search");
      break;
    case "report":
      ElMessage.info("报表功能开发中");
      break;
    case "notice":
      handleViewMore("notice");
      break;
  }
};

// 查看更多
const handleViewMore = (type: string) => {
  ElMessage.info(`${type === "todo" ? "待办事项" : "通知公告"}详情页面开发中`);
};

// 获取统计数据
const getTjData = async () => {
  try {
    const data = await dashboardAPI.getTjData();
    tj_data.value = data || [];
  } catch (error) {
    console.error("获取统计数据失败:", error);
  }
};

// 模拟获取待办事项
const getTodoList = () => {
  // 这里应该调用实际的API
  todoList.value = [
    {
      id: 1,
      title: "资产盘点任务",
      createTime: new Date().toISOString(),
      status: "pending",
    },
    {
      id: 2,
      title: "设备维护申请审批",
      createTime: new Date(Date.now() - 86400000).toISOString(),
      status: "processing",
    },
  ];
};

// 模拟获取通知公告
const getNoticeList = () => {
  // 这里应该调用实际的API
  noticeList.value = [
    {
      id: 1,
      title: "关于开展2024年度资产盘点工作的通知",
      createTime: new Date().toISOString(),
      isNew: true,
    },
    {
      id: 2,
      title: "系统维护通知",
      createTime: new Date(Date.now() - 172800000).toISOString(),
      isNew: false,
    },
  ];
};

onMounted(() => {
  getTjData();
  getTodoList();
  getNoticeList();
});
</script>

<style lang="scss" scoped>
.mobile-dashboard {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.welcome-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  color: white;

  .user-info {
    display: flex;
    align-items: center;

    .avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 12px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .greeting {
      .welcome-text {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 4px;
      }

      .time-text {
        font-size: 12px;
        opacity: 0.8;
      }
    }
  }
}

.stats-section {
  margin-bottom: 20px;

  .stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .stat-card {
    background: white;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &.warning {
      border-left: 4px solid #ff6b6b;
    }

    .stat-number {
      font-size: 24px;
      font-weight: bold;
      color: #333;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
    }

    .stat-amount {
      font-size: 12px;
      color: var(--el-color-primary);
      font-weight: 500;
    }

    .stat-desc {
      font-size: 12px;
      color: #ff6b6b;
    }
  }
}

.quick-actions {
  margin-bottom: 20px;

  .actions-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    margin-top: 12px;
  }

  .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 8px;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &:active {
      transform: scale(0.95);
    }

    .action-icon {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f0f7ff;
      border-radius: 50%;
      margin-bottom: 8px;
      color: var(--el-color-primary);
    }

    .action-text {
      font-size: 12px;
      color: #333;
      text-align: center;
    }
  }
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;

  .more-link {
    font-size: 12px;
    color: var(--el-color-primary);
    cursor: pointer;
  }
}

.todo-section,
.notice-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.todo-list,
.notice-list {
  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;

    .empty-text {
      margin-top: 8px;
      font-size: 14px;
    }
  }
}

.todo-item,
.notice-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .todo-content,
  .notice-content {
    flex: 1;

    .todo-title,
    .notice-title {
      font-size: 14px;
      color: #333;
      margin-bottom: 4px;
    }

    .todo-time,
    .notice-time {
      font-size: 12px;
      color: #999;
    }
  }

  .todo-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;

    &.pending {
      background: #fff7e6;
      color: #fa8c16;
    }

    &.processing {
      background: #e6f7ff;
      color: #1890ff;
    }

    &.completed {
      background: #f6ffed;
      color: #52c41a;
    }
  }

  .notice-badge {
    span {
      background: #ff4d4f;
      color: white;
      padding: 2px 6px;
      border-radius: 8px;
      font-size: 10px;
    }
  }
}
</style>
