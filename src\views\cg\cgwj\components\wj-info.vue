<template>
  <Title name="项目信息">
    <div>
      <el-button v-if="editable" type="primary" @click="handleSave()">保存</el-button>
      <el-button v-if="editable" type="danger" @click="handleNext()">下一步</el-button>
    </div>
  </Title>

  <el-form
    ref="dataFormRef"
    :model="formData"
    :rules="rules"
    label-width="80px"
    :inline="false"
    :disabled="!editable"
  >
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="文件编号" prop="wjbh">
          <span>{{ formData.wjbh }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="文件名称" prop="wjmc">
          <el-input v-if="false" v-model="formData.guid" />
          <el-input v-model="formData.wjmc" placeholder="文件名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="采购形式" prop="cgxs">
          <span>{{ formData.cgxsname }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="采购方式" prop="cgfs">
          <span>{{ formData.cgfsname }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="预算经费" prop="ysjf">
          <span>{{ formData.ysjf }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="联系人" prop="lxr">
          <el-input v-model="formData.lxr" placeholder="联系人" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="联系方式" prop="lxfs">
          <el-input v-model="formData.lxfs" placeholder="联系方式" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注" prop="notes">
          <el-input v-model="formData.notes" placeholder="备注" type="textarea" :rows="2" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import ZxcgCgwjAPI, { ZxcgCgwjForm } from "@/api/cg/zxcg-cgwj";

const props = defineProps({
  //guid
  guid: {
    type: String,
    required: true,
  },
  //是否可编辑
  editable: {
    type: Boolean,
    required: true,
  },
});
const dataFormRef = ref(ElForm);
const loading = ref(false);
// 采购文件表单数据
const formData = reactive<ZxcgCgwjForm>({});

const emits = defineEmits(["nowstep"]);

// 采购文件表单校验规则
const rules = reactive({});
/** 初始化表单信息 */
function handleFormQuery() {
  ZxcgCgwjAPI.getFormData(props.guid)
    .then((res) => {
      Object.assign(formData, res);
      console.log("formData", formData);
    })
    .finally(() => {});
}

const handleSave = async () => {
  dataFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      await ZxcgCgwjAPI.save(formData)
        .then(async (res) => {
          ElMessage.success("保存成功");
        })
        .catch((error) => {
          ElMessage.error("操作失败，失败原因：" + error.message);
        })
        .finally(() => {});
    }
  });
};

const handleNext = async () => {
  dataFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      await ZxcgCgwjAPI.save(formData)
        .then(async (res) => {
          //ElMessage.success("保存成功");
          emits("nowstep", 2);
        })
        .catch((error) => {
          ElMessage.error("操作失败，失败原因：" + error.message);
        })
        .finally(() => {});
    }
  });
};

onMounted(() => {
  handleFormQuery();
});
</script>
<style scoped></style>
