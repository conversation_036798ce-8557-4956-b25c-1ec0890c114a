<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-card class="search-card" :body-style="{ padding: '24px' }">
      <el-form :model="searchForm" inline class="search-form">
        <div class="form-item-group">
          <el-form-item label="标题" class="ml-10">
            <el-input v-model="listQuery.title" placeholder="请输入标题" clearable />
          </el-form-item>
          <el-form-item label="创建时间" class="ml-10">
            <el-date-picker
              v-model="listQuery.dateRange"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="listQuery.status" placeholder="状态" clearable>
              <el-option label="待编辑" value="draft" />
              <el-option label="已发布" value="published" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
            <el-button type="success" :icon="Plus" @click="handleCreate">新增</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-card>

    <el-card class="table-card">
      <el-table v-loading="listLoading" :data="list" style="width: 100%">
        <el-table-column prop="title" label="标题" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === 'published' ? 'success' : 'info'">
              {{ row.status === "published" ? "已发布" : "待编辑" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <!-- <el-table-column label="图片" width="120">
          <template #default="{ row }">
            <el-image
              v-for="(img, index) in row.images"
              :key="index"
              :src="img"
              :preview-src-list="row.images"
              preview-teleported
              class="w-20 h-20 mr-5"
            />
          </template>
        </el-table-column> -->
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="text" @click="handleEdit(row)">编辑</el-button>
            <el-button type="text" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="page.current"
          v-model:page-size="page.size"
          :total="page.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="60%">
      <el-form :model="temp" label-width="80px">
        <el-form-item label="标题">
          <el-input v-model="temp.title" />
        </el-form-item>
        <el-form-item label="内容">
          <WangEditor
            v-model="temp.content"
            style="min-height: 480px; max-height: 500px; border-radius: 10px"
            border
          />
        </el-form-item>
        <el-form-item label="图片">
          <image-upload v-model="temp.images" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="temp.status" placeholder="请选择">
            <el-option label="待编辑" value="draft" />
            <el-option label="已发布" value="published" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search, Refresh, Plus, View, Edit } from "@element-plus/icons-vue";
// 搜索表单
const searchForm = reactive({
  name: "",
  minPrice: undefined as undefined | number,
  maxPrice: undefined as undefined | number,
});

// 接口定义
interface MessageItem {
  id: number;
  title: string;
  content: string;
  images: string[];
  status: "draft" | "published";
  createTime: string;
}

// 数据
const list = ref<MessageItem[]>([]);
const listLoading = ref(false);
const total = ref(0);
const dialogVisible = ref(false);
const dialogTitle = ref("");
const temp = reactive<Partial<MessageItem>>({});
// 分页配置
const page = reactive({
  current: 1,
  size: 10,
  total: 0,
});
// 查询参数
const listQuery = reactive({
  page: 1,
  limit: 10,
  status: "",
  title: "",
  dateRange: [],
});

// 获取列表
const fetchData = async () => {
  listLoading.value = true;
  // // 模拟数据
  // const { data } = await fetchMessages({
  //   ...listQuery,
  //   startTime: listQuery.dateRange?.[0],
  //   endTime: listQuery.dateRange?.[1],
  // });
  // page.total = data.total;
  list.value = [
    {
      id: 1,
      title: "景区春节营业通知",
      content: "<p>2024年春节假期开放时间：除夕至初六正常营业</p>",
      images: ["/src/assets/demo/notice1.jpg"],
      status: "published",
      createTime: "2024-02-05 09:00:00",
    },
    {
      id: 2,
      title: "清明祭扫温馨提示",
      content: "<p>清明节期间请文明祭扫，注意防火安全</p>",
      images: [],
      status: "draft",
      createTime: "2024-03-25 14:30:00",
    },
    // 补充18条测试数据
    {
      id: 3,
      title: "五一假期限流公告",
      content: "<p>五一期间每日限流5万人次，请提前预约购票</p>",
      images: ["/src/assets/demo/notice2.jpg", "/src/assets/demo/map.jpg"],
      status: "published",
      createTime: "2024-04-15 08:45:00",
    },
    {
      id: 4,
      title: "索道维护通知",
      content: "<p>4月20日-25日东线索道停运维护，请合理安排行程</p>",
      images: ["/src/assets/demo/maintenance.jpg"],
      status: "published",
      createTime: "2024-04-10 14:00:00",
    },
    {
      id: 5,
      title: "夏季开放时间调整",
      content: "<p>5月1日起实行夏季作息，开放时间延长至20:00</p>",
      images: [],
      status: "draft",
      createTime: "2024-04-28 09:30:00",
    },
    {
      id: 6,
      title: "停车场扩建公告",
      content: "<p>南门停车场将于6月1日起封闭施工，请使用北门停车场</p>",
      images: ["/src/assets/demo/parking.jpg"],
      status: "published",
      createTime: "2024-04-20 10:15:00",
    },
    {
      id: 7,
      title: "野生动物出没提醒",
      content: "<p>近期景区内发现野生猕猴群，请勿投喂食物</p>",
      images: ["/src/assets/demo/monkey.jpg"],
      status: "published",
      createTime: "2024-03-30 15:20:00",
    },
    {
      id: 8,
      title: "观光车路线调整",
      content: "<p>4月25日起观光车B线临时调整，详见站牌通知</p>",
      images: [],
      status: "draft",
      createTime: "2024-04-22 11:00:00",
    },
    {
      id: 9,
      title: "门票优惠政策更新",
      content: "<p>5月1日起学生凭有效证件享7折优惠</p>",
      images: ["/src/assets/demo/discount.jpg"],
      status: "published",
      createTime: "2024-04-18 09:45:00",
    },
    {
      id: 10,
      title: "紧急天气预警",
      content: "<p>4月28日将有强降雨，部分户外项目暂停开放</p>",
      images: ["/src/assets/demo/weather.jpg"],
      status: "published",
      createTime: "2024-04-27 16:30:00",
    },
    {
      id: 11,
      title: "文创商店上新通知",
      content: "<p>景区限定文创产品已上架，欢迎选购</p>",
      images: ["/src/assets/demo/product1.jpg", "/src/assets/demo/product2.jpg"],
      status: "draft",
      createTime: "2024-04-25 14:15:00",
    },
    {
      id: 12,
      title: "夜游项目试运营",
      content: "<p>4月30日起开放夜景灯光秀，每晚19:30开始</p>",
      images: ["/src/assets/demo/night_view.jpg"],
      status: "published",
      createTime: "2024-04-29 10:00:00",
    },
    {
      id: 13,
      title: "索道票价调整公示",
      content: "<p>5月10日起东西线索道票价统一调整为120元/人次</p>",
      images: [],
      status: "draft",
      createTime: "2024-04-30 09:20:00",
    },
    {
      id: 14,
      title: "摄影大赛征稿",
      content: '<p>"最美春景"摄影大赛火热征稿中，截稿日期5月15日</p>',
      images: ["/src/assets/demo/contest.jpg"],
      status: "published",
      createTime: "2024-04-05 08:30:00",
    },
    {
      id: 15,
      title: "志愿者招募公告",
      content: "<p>五一假期景区志愿者招募，报名截止4月25日</p>",
      images: ["/src/assets/demo/volunteer.jpg"],
      status: "published",
      createTime: "2024-04-12 13:45:00",
    },
    {
      id: 16,
      title: "生态保护倡议书",
      content: "<p>保护景区生态环境，请勿乱扔垃圾</p>",
      images: ["/src/assets/demo/environment1.jpg", "/src/assets/demo/environment2.jpg"],
      status: "draft",
      createTime: "2024-04-17 15:00:00",
    },
    {
      id: 17,
      title: "自动售票机使用指南",
      content: "<p>新增10台自助售票机，支持微信/支付宝付款</p>",
      images: ["/src/assets/demo/kiosk.jpg"],
      status: "published",
      createTime: "2024-03-28 11:30:00",
    },
    {
      id: 18,
      title: "景区WiFi覆盖公告",
      content: '<p>核心区域已实现免费WiFi全覆盖，搜索"SCENIC-FREE"连接</p>',
      images: [],
      status: "published",
      createTime: "2024-04-02 10:00:00",
    },
    {
      id: 19,
      title: "观景台临时封闭通知",
      content: "<p>4月23日08:00-12:00东峰观景台封闭维护</p>",
      images: ["/src/assets/demo/viewpoint.jpg"],
      status: "draft",
      createTime: "2024-04-22 16:45:00",
    },
    {
      id: 20,
      title: "年卡续费优惠",
      content: "<p>即日起5月31日续费年卡享9折优惠</p>",
      images: [
        "/src/assets/demo/member.jpg",
        "/src/assets/demo/member2.jpg",
        "/src/assets/demo/member3.jpg",
      ],
      status: "published",
      createTime: "2024-04-19 09:00:00",
    },
  ];
  total.value = 1;
  listLoading.value = false;
};

// 筛选
const handleFilter = () => {
  listQuery.page = 1;
  fetchData();
};
// 搜索
const handleSearch = () => {};
// 新建
const handleCreate = () => {
  resetTemp();
  dialogTitle.value = "新建消息";
  dialogVisible.value = true;
};

// 编辑
const handleEdit = (row: MessageItem) => {
  Object.assign(temp, row);
  dialogTitle.value = "编辑消息";
  dialogVisible.value = true;
};

// 删除
const handleDelete = (row: MessageItem) => {
  ElMessageBox.confirm("确定删除该消息吗？", "提示", {
    type: "warning",
  }).then(() => {
    // 模拟删除
    const index = list.value.findIndex((item) => item.id === row.id);
    list.value.splice(index, 1);
    total.value -= 1;
    ElMessage.success("删除成功");
    fetchData();
  });
};

// 确认
const handleConfirm = async () => {
  // 模拟保存
  if (temp.id) {
    const index = list.value.findIndex((item) => item.id === temp.id);
    list.value.splice(index, 1, { ...temp } as MessageItem);
  } else {
    list.value.push({
      ...temp,
      id: Date.now(),
      createTime: new Date().toLocaleString(),
    } as MessageItem);
  }
  dialogVisible.value = false;
  ElMessage.success("保存成功");
  fetchData();
};

// 重置表单
const resetTemp = () => {
  temp.id = undefined;
  temp.title = "";
  temp.content = "";
  temp.images = [];
  temp.status = "draft";
};

onMounted(() => {
  fetchData();
});
// 分页处理
const handleSizeChange = (val: number) => {
  page.size = val;
  page.current = 1;
};

const handleCurrentChange = (val: number) => {
  page.current = val;
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);

  .search-card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .search-form {
      .form-item-group {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: flex-start;

        .separator {
          margin: 0 8px;
          color: #909399;
        }

        :deep(.el-form-item) {
          margin: 0;

          .el-input,
          .el-select {
            width: 240px;
          }
        }

        .search-buttons {
          margin-left: auto;

          .el-button {
            margin: 0;

            &:not(:first-child) {
              margin-left: 8px;
            }
          }
        }
      }
    }
  }

  .table-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .table-container {
      :deep(.el-table) {
        .price-info {
          .current-price {
            color: #f56c6c;
            font-weight: bold;
          }

          .original-price {
            margin-left: 8px;
            color: #909399;
            text-decoration: line-through;
            font-size: 12px;
          }
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      padding: 10px 20px;
      text-align: right;
    }
  }
}

.ticket-form {
  :deep(.el-upload--picture-card) {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }

  .image-preview {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    .preview-image {
      width: 100px;
      height: 100px;
      object-fit: cover;
      border-radius: 4px;
    }
  }
}
</style>
