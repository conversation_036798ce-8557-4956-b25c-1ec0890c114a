<template>
  <!-- 房屋信息 -->
  <el-form :model="formData" :rules="rules" ref="formRef" label-width="120px" :inline="true"
    :disabled="!props.editable">
    <el-form-item label="坐落地点">
      <el-input v-model="formData.zldd" placeholder="请输入坐落地点" />
    </el-form-item>

    <el-form-item label="建筑结构" prop="jzjg">
      <DDLXcode v-model="formData.jzjg" xcode="020205" clearable />
    </el-form-item>

    <el-form-item label="房屋总面积(㎡)">
      <el-input v-model="formData.fwzmj" placeholder="请输入房屋总面积" />
    </el-form-item>

    <el-form-item label="出租出借面积(㎡)">
      <el-input v-model="formData.czcjmj" placeholder="请输入出租出借面积" />
    </el-form-item>

    <el-form-item label="自用面积(㎡)">
      <el-input v-model="formData.zymj" placeholder="请输入自用面积" />
    </el-form-item>

    <el-form-item label="损毁待报废面积(㎡)">
      <el-input v-model="formData.dbfmj" placeholder="请输入损毁待报废面积" />
    </el-form-item>

    <el-form-item label="闲置面积(㎡)">
      <el-input v-model="formData.xzmj" placeholder="请输入闲置面积" />
    </el-form-item>

    <el-form-item label="其他面积(㎡)">
      <el-input v-model="formData.qtmj" placeholder="请输入其他面积" />
    </el-form-item>

    <el-form-item label="其中:危房面积(㎡)">
      <el-input v-model="formData.wfmj" placeholder="请输入危房面积" />
    </el-form-item>

    <el-form-item label="产权形式" prop="cqxs">
      <DDLXcode v-model="formData.cqxs" xcode="020206" clearable />
    </el-form-item>

    <el-form-item v-show="formData.cqxs === '02020601'" label="有产权情况">
      <DDLXcode v-model="formData.ycqqk" xcode="02020601" clearable />
    </el-form-item>

    <el-form-item v-show="formData.cqxs === '02020603'" label="无产权情况">
      <DDLXcode v-model="formData.wcqqk" xcode="02020603" clearable />
    </el-form-item>

    <el-form-item label="权属性质" prop="qsxz">
      <DDLXcode v-model="formData.qsxz" xcode="020207" clearable />
    </el-form-item>

    <el-form-item label="权属证明">
      <el-input v-model="formData.qszm" placeholder="请输入权属证明" />
    </el-form-item>

    <el-form-item label="权属证号">
      <el-input v-model="formData.qszh" placeholder="请输入权属证号" />
    </el-form-item>

    <el-form-item label="产权单位">
      <el-input v-model="formData.cqdw" placeholder="请输入产权单位" />
    </el-form-item>

    <el-form-item label="发证时间">
      <el-date-picker v-model="formData.fzsj" type="date" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择发证时间" />
    </el-form-item>

    <el-form-item label="所有权人">
      <el-input v-model="formData.syqr" placeholder="请输入所有权人" />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ElLoading } from 'element-plus';
import { ref, reactive } from 'vue'
import storeAPI, { ZcinfoDifferentForm } from "@/api/properties/store";


// 组件属性定义
const props = defineProps({
  guid: {
    type: String
  },
  zclxbh: {
    type: String
  },
  editable: {
    type: Boolean,
    required: true
  }
})
// 表单数据
const formData = reactive<ZcinfoDifferentForm>({
  zclxbh: props.zclxbh || '',
})

// 表单验证规则
const rules = reactive({
  jzjg: [
    { required: true, message: '请选择建筑结构', trigger: 'change' }
  ],
  cqxs: [
    { required: true, message: '请选择产权形式', trigger: 'change' }
  ],
  qsxz: [
    { required: true, message: '请选择权属性质', trigger: 'change' }
  ]
})

// 表单引用
const formRef = ref(ElForm);

// 下拉框选项
const buildingStructureOptions = ref([
  { value: '02020501', label: '钢结构' },
  { value: '02020502', label: '钢混结构' },
  { value: '02020503', label: '砖混结构' },
  { value: '02020504', label: '砖木结构' },
  { value: '02020504', label: '其他结构' }
])

const propertyFormOptions = ref([
  { value: '02020601', label: '有产权' },
  { value: '02020602', label: '产权待界定' },
  { value: '02020603', label: '无产权' }
])

const ycqkOptions = ref([
  { value: '02022101', label: '有权证' },
  { value: '02022102', label: '无权证' }
])

const wcqkOptions = ref([
  { value: '02021401', label: '无偿占用' },
  { value: '02021402', label: '租用' },
  { value: '02021403', label: '规划许可的临时建筑' }
])

const propertyNatureOptions = ref([
  { value: '02020701', label: '国有' },
  { value: '02020702', label: '集体' }
])

// 处理产权形式变更
const handleCqxsChange = (value) => {
  // 清空相关字段
  if (value !== '02020601') {
    formData.ycqqk = ''
  }
  if (value !== '02020603') {
    formData.wcqqk = ''
  }
}

// 监听值变化
watch(
  () => [props.guid, props.zclxbh],
  (val) => {
    console.log('props:', val)
    if (val && val[0] != '' && val[1] != undefined) {
      console.log('props:', val, val[0], val[1])
      //获取表单数据
      storeAPI.getFormDataDiff({ guid: val[0], zclxbh: val[1] }).then((res) => {
        Object.assign(formData, res)
      })

      formData.zclxbh = val[1]
      formData.guid = val[0]
    }
  },
  { deep: true, immediate: true }
)

// 重置表单
const resetForm = () => {
  formRef.value.resetFields()
}

// 提交表单
const submitForm = () => {
  return new Promise((resolve, reject) => {
    formRef.value?.validate((valid) => {
      if (valid) {
        // 显示加载状态
        const loading = ElLoading.service({
          lock: true,
          text: '正在保存...',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        storeAPI.updateDiff(formData).then((res) => {
          // 关闭加载状态
          loading.close()
          // 显示成功消息
          ElMessage({
            type: 'success',
            message: '保存成功'
          })
          console.log('表单提交成功:', res)
          resolve(res)
        }).catch(error => {
          // 关闭加载状态
          loading.close()
          // 显示错误消息
          ElMessage({
            type: 'error',
            message: '保存失败: ' + (error.message || '未知错误')
          })
          console.error('表单提交失败:', error)
          reject(error)
        })
      } else {
        ElMessage({
          type: 'warning',
          message: '请填写必填项'
        })
        reject(new Error('表单验证失败'))
        return false
      }
    })
  })
}


// 表单验证方法
const validateForm = () => {
  return new Promise((resolve, reject) => {
    formRef.value?.validate((valid) => {
      if (valid) {
        resolve(true)
      } else {
        reject(new Error('表单验证失败'))
      }
    })
  })
}


// 导出方法供父组件使用
defineExpose({
  submitForm,
  resetForm,
  validate: validateForm
})

</script>

<style scoped>
/* 自定义样式 */
.el-form-item {
  width: 330px;
}
</style>