<template>
  <!-- 支持搜索 -->
  <el-select
    v-model="_modelValue"
    filterable
    clearable
    :loading="loading"
    :placeholder="placeholder"
    :multiple="props.multiple"
    @visible-change="handleDropdownVisibility"
  >
    <el-option
      v-for="item in pageData.list"
      :key="item.username"
      :label="item.nickname"
      :value="item.username"
    />
  </el-select>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import SysAPI, { UserListQuery } from "@/api/system/sys";
import type { UserPageVO } from "@/api/system/user";

interface Xcode {
  XCODE: string;
  NAME: string;
}

interface Props {
  modelValue?: string;
  dcode?: string;
  rcode?: string;
  placeholder?: string;
  multiple?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  dcode: "",
  rcode: "",
  placeholder: "请选择用户",
  multiple: false,
});

const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
  (e: "error", error: Error): void;
}>();

// Reactive state
const loading = ref(false);
const pageData = reactive<any>({
  list: [],
});

// Computed v-model implementation
const _modelValue = computed({
  get: () => props.modelValue,
  set: (val) => emit("update:modelValue", val || ""),
});

// Query parameters
const params = reactive<UserListQuery>({
  rcode: props.rcode,
  username: "",
  ucode: "",
  dcode: props.dcode,
  pageNum: 1,
  pageSize: 1000,
});

// Methods
const getList = async () => {
  if (loading.value) return;

  params.dcode = props.dcode;
  params.rcode = props.rcode;
  pageData.list = [];
  loading.value = true;

  try {
    const res = await SysAPI.getUserList(params);
    if (!res?.list) {
      throw new Error("Invalid response format");
    }

    pageData.list = res.list;
    // .map((item: UserPageVO) => ({
    //   XCODE: item.username || "",
    //   NAME: item.nickname || "",
    // }));
    let isExist = false;
    res.list.forEach((item) => {
      if (item.username == props.modelValue) {
        isExist = true;
      }
    });
    if (!isExist) {
      emit("update:modelValue", "");
    }
  } catch (error) {
    console.error("Failed to fetch user list:", error);
    emit("error", error instanceof Error ? error : new Error("Unknown error"));
    ElMessage.error("获取用户列表失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// Handlers
const handleDropdownVisibility = (visible: boolean) => {
  if (visible && pageData.list.length === 0) {
    getList();
  }
};

// Watchers
watch(
  () => props.dcode,
  (newValue, oldValue) => {
    params.dcode = newValue;
    pageData.list = [];

    // Only clear selection if we had a previous value
    // if (oldValue) {
    //   emit("update:modelValue", "");
    // }

    if (newValue) {
      getList();
    }
  }
);

watch(
  () => props.rcode,
  (newValue) => {
    params.rcode = newValue;
    if (newValue) {
      getList();
    }
  }
);

// Initial load if we have either dcode or rcode
onMounted(() => {
  if (props.dcode || props.rcode) {
    getList();
  }
});
</script>
