<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item v-if="view || check || add || back" label="借用单号" prop="sqdh">
            <el-input
              v-model="queryParams.sqdh"
              placeholder="借用单号"
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item v-if="view" label="申请部门" prop="sqbm">
            <DDLDeptList v-model="queryParams.sqbm" />
          </el-form-item>
          <el-form-item v-if="view" label="审核环节" prop="" />
          <el-form-item v-if="view" label="审核状态" prop="sjzt">
            <DDLCheckStatus v-model="queryParams.sjzt" />
          </el-form-item>
          <el-form-item v-if="view" label="申请日期" prop="sqrq">
            <el-date-picker
              v-model="queryParams.sqrq"
              :editable="false"
              class="filter-item"
              type="daterange"
              range-separator="~"
              start-placeholder="开始时间"
              end-placeholder="截止时间"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon>
                <Search />
              </template>
              搜索
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon>
                <Refresh />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <Title name="借用单列表">
        <div>
          <el-button
            v-if="back"
            plain
            type="primary"
            icon="Pointer"
            :disabled="removeIds.length === 0"
            @click="handleConfirm()"
          >
            批量归还
          </el-button>
          <el-button type="success" plain @click="handleOpen(true)" v-if="add">
            <template #icon><Plus /></template>
            新增
          </el-button>
          <el-button
            type="danger"
            plain
            :disabled="removeIds.length === 0"
            @click="handleDelete()"
            v-if="add"
          >
            <template #icon><Delete /></template>
            删除
          </el-button>
          <el-radio-group
            v-if="check"
            v-model="queryParams.checkstatus"
            @change="handleChangeRadio"
          >
            <el-radio :value="0" border>待审</el-radio>
            <el-radio :value="1" border>已审</el-radio>
          </el-radio-group>
        </div>
      </Title>
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column v-if="add || back" type="selection" width="55" align="center" fixed />
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column key="sqdh" label="申请单号" prop="sqdh" min-width="150" align="center" />
        <el-table-column
          key="djrname"
          label="申请人"
          prop="djrname"
          min-width="100"
          align="center"
        />
        <el-table-column key="djsj" label="申请时间" prop="djsj" min-width="150" align="center" />
        <el-table-column
          key="bgrname"
          label="出借人"
          prop="bgrname"
          min-width="100"
          align="center"
        />
        <el-table-column
          key="sybmname"
          label="出借部门"
          prop="sybmname"
          min-width="150"
          align="center"
        />
        <el-table-column key="by2" label="预计借用天数" prop="by2" min-width="150" align="center" />

        <el-table-column label="预计归还日期" prop="ghsj" min-width="150" align="center">
          <template #default="scope">
            <span>{{ calculateReturnDate(scope.row.sxsj, scope.row.by2) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="isback" width="150" align="center">
          <template #default="scope">
            <el-tag :type="isOverdue(scope.row.sxsj, scope.row.by2)">
              {{ getReturnStatus(scope.row.sxsj, scope.row.by2) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-if="view"
          key="nodemc"
          label="状态"
          prop="nodemc"
          min-width="120"
          align="center"
        />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button
              v-if="add"
              type="primary"
              icon="edit"
              size="small"
              @click="handleOpenDialog(true, scope.row)"
            >
              编辑
            </el-button>
            <el-button v-if="add" type="danger" size="small" @click="handleDelete(scope.row.guid)">
              <template #icon>
                <Delete />
              </template>
              删除
            </el-button>

            <el-button
              v-if="(check || back) && queryParams.checkstatus == 0"
              type="primary"
              icon="DocumentChecked"
              size="small"
              @click="handleOpenDialog(false, scope.row)"
            >
              审核
            </el-button>
            <el-button
              v-if="view || ((check || back) && queryParams.checkstatus == 1)"
              type="success"
              icon="Document"
              size="small"
              @click="handleOpenDialog(false, scope.row)"
            >
              查看
            </el-button>

            <el-button
              v-if="view && scope.row.isCallBack == '1'"
              type="danger"
              icon="Remove"
              size="small"
              @click="handleCallBack(scope.row.guid)"
            >
              收回
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <el-drawer v-model="dialog.visible" :title="dialog.title" append-to-body size="80%">
      <el-card>
        <!-- 
        id、guid常规参数
        key用于刷新组件
        editable用于区分是否可编辑
        dcbm调出部门特殊字段
        netcode新增编辑时为空,审核时需要 
        RefreshFatherDrawer关闭抽屉并刷新数据  -->
        <lendView
          :id="itemId"
          :key="itemGuid"
          :guid="itemGuid"
          :editable="itemEditable"
          :dcbm="itemDcbm"
          :netcode="itemNetcode"
          :checkstatus="queryParams.checkstatus"
          :RefreshFatherDrawer="RefreshFatherDrawer"
        />
      </el-card>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import lendAPI from "@/api/properties/lend";
import { getGuid } from "@/utils/guid";
import lendView from "./components/LendView.vue";
function RefreshFatherDrawer(close?: boolean) {
  //是否关闭弹窗由参数决定，默认不关闭
  if (close) {
    dialog.visible = false;
  }
  handleQuery();
}
// 获取路由参数,view/add/check
const route = useRoute();
const type = ref(route.query.type?.toString() || "view");
//显示隐藏权限,用于v-if
const add = type.value == "add";
const view = type.value == "view";
const check = type.value == "check";
const back = type.value == "return";
//页面参数
const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);

const loading = ref(false);
const removeIds = ref<string[]>([]);
const total = ref(0);

const queryParams = reactive<any>({
  pageNum: 1,
  pageSize: 10,
  type: type.value,
  checkstatus: 0, //用于查询已审和待审
});

const handleChangeRadio = () => {
  handleResetQuery();
};

// transfer表格数据
const pageData = ref<any[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});
const netcode = ref("");
//初始化一些用户的信息
// transfer表单数据
const formData = reactive<any>({});

// transfer表单校验规则
const rules = reactive({});

/** 查询transfer */
function handleQuery() {
  loading.value = true;
  console.log("queryParams", queryParams);
  lendAPI
    .getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .catch((error) => {
      console.error("获取借用数据失败:", error);
      ElMessage.error("获取借用数据失败");
      pageData.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置transfer查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  removeIds.value = selection.map((item: any) => item.guid);
}

/** 删除借用 */
function handleDelete(id?: string) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      lendAPI
        .deleteByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .catch((error) => {
          console.error("删除借用数据失败:", error);
          ElMessage.error("删除借用数据失败");
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

/** 批量提交 */
function handleConfirm(id?: string) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选待归还项");
    return;
  }

  ElMessageBox.confirm("确认批量归还已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      lendAPI
        .submitByIds(ids)
        .then(() => {
          ElMessage.success("批量归还成功");
          handleResetQuery();
        })
        .catch((error) => {
          console.error("批量归还失败:", error);
          ElMessage.error("批量归还失败");
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消批量归还");
    }
  );
}

//————————————————————————————————————————————弹窗相关
/** 打开借用弹窗 */
const itemGuid = ref<string>("");
const itemId = ref<number | undefined>();
const itemDcbm = ref<string>("");
const itemEditable = ref<boolean>(false);
const itemNetcode = ref<string>("");
function handleOpenDialog(editable: boolean, row?: any) {
  itemGuid.value = row?.guid || getGuid();
  itemId.value = row?.id;
  itemNetcode.value = row?.netcode || "";
  itemEditable.value = editable;
  dialog.title = editable ? (row?.id ? "编辑借用单" : "新增借用单") : "查看借用单";
  dialog.visible = true;
  console.log("type.value", type.value);
}

// 获取路由实例（可导航）
const router = useRouter();

function handleOpen(editable: boolean) {
  router.replace("/pub/adjustMarket?type=jy");
}

/** 关闭调拨弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  handleQuery();
}

const handleCallBack = (guid: string) => {
  ElMessageBox.confirm("确认收回吗?收回后可再次提交该单据", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      loading.value = true;
      lendAPI
        .callBack(guid)
        .then(() => {
          ElMessage.success("操作成功");
          handleResetQuery();
        })
        .catch((error) => {
          ElMessage.error("操作失败，失败原因：" + error.message);
        })
        .finally(() => (loading.value = false));
    })
    .catch(() => {
      ElMessage.info("已取消收回");
    });
};

// 计算预计归还日期
function calculateReturnDate(startDate: string, days: string): string {
  if (!startDate || !days) return "-";

  try {
    const date = new Date(startDate);
    date.setDate(date.getDate() + parseInt(days));
    return date.toISOString().split("T")[0]; // 返回YYYY-MM-DD格式
  } catch (error) {
    console.error("日期计算错误:", error);
    return "-";
  }
}

// 判断是否已超期
function isOverdue(startDate: string, days: string): "success" | "danger" | "info" {
  if (!startDate || !days) return "info";

  try {
    const date = new Date(startDate);
    date.setDate(date.getDate() + parseInt(days));
    return date < new Date() ? "danger" : "success";
  } catch (error) {
    console.error("日期比较错误:", error);
    return "info";
  }
}

// 获取归还状态
function getReturnStatus(startDate: string, days: string): string {
  if (!startDate || !days) return "-";

  try {
    const date = new Date(startDate);
    date.setDate(date.getDate() + parseInt(days));

    if (date > new Date()) {
      return "待归还";
    } else {
      return "待归还（已超期）";
    }
  } catch (error) {
    console.error("状态计算错误:", error);
    return "-";
  }
}

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}

.flex {
  display: flex;
  flex-direction: row;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}
</style>
