import request from "@/utils/request";

const Accept_BASE_URL = "/api/v1/store";

const flowtrackAPI = {
  /** 获取分页数据 */
  getPage(queryParams?: flowtrackPageQuery) {
    return request<any, PageResult<flowtrackPageVO[]>>({
      url: `${Accept_BASE_URL}/page-flowtracking`,
      method: "get",
      params: queryParams,
    });
  },
}

export default flowtrackAPI;

/** 流程跟踪分页查询参数 */
export interface flowtrackPageQuery extends PageQuery {
  /** 资产名称或编号 */
  zcmc?: string;
  syr?: string;
  djr?: string;
  lb?: string;
  cfdd?: string;
  sybm?: string;
  djbm?: string;
}


/** 变动单分页对象 */
export interface flowtrackPageVO {
  /** 资产编号 */
  zcbh?: string;
  zcmc?: string;
  guid?: string;
  zcguid?: string;
  syr?: string;
  syrname?: string;
  sybm?: string;
  sybmname?: string;
  djr?: string;
  djrname?: string;
  djbm?: string;
  djbmname?: string;
  lb?: string;
  sjzt?: string;  
  cfdd?: string;
  cfddname?: string;
  nodemc?: string;
  djsj?: Date;
}


