<template>
  <div class="app-container">
    <!-- <el-card>
      <template #header>
        <div class="flex-x-between">
          <div class="flex-y-center">流程图</div>
        </div>
      </template>

      <el-steps :active="-1" :space="300" align-center>
        <el-step
          v-for="(item, index) in flow.lcjd"
          :key="index"
          :title="item.netName"
          :description="item.userName"
          :icon="getIcon(item.netState)"
          :status="getStatus(item.netState)"
        ></el-step>
      </el-steps>
    </el-card> -->
  </div>
</template>

<script setup lang="ts">
import { Edit, Picture, Upload } from "@element-plus/icons-vue";
import { get } from "sortablejs";

//根据guid获取节点信息
const props = defineProps({
  guid: {
    type: String,
    required: true,
  },
});

// const activities = ref<ProcessLogInfo[]>([]);

// 获取节点信息
onMounted(() => {});

const getIcon = (s: string) => {
  switch (s) {
    case "0":
      return "Clock";
    case "1":
      return "CircleCheck";
    case "2":
      return "Loading";
  }
};
const getStatus = (s: string) => {
  switch (s) {
    case "0":
      return "wait";
    case "1":
      return "finish";
    case "2":
      return "process";
  }
};
</script>
<style lang="scss" scoped></style>
