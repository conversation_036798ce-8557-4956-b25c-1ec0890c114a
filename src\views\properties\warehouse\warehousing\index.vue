<!-- cursor测试:1 -->
<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item v-if="add || view || check" label="入仓申请单号" prop="dbdh">
            <el-input
              v-model="queryParams.sqdh"
              placeholder="入仓申请单号"
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item v-if="view" label="申请人" prop="dbdh">
            <el-input v-model="queryParams.djrname" placeholder="申请人" />
          </el-form-item>

          <el-form-item v-if="view" label="申请部门" prop="djbm">
            <DDLDeptList v-model="queryParams.djbm" />
          </el-form-item>
          <el-form-item v-if="view" label="申请时间" prop="djsj">
            <el-date-picker
              v-model="queryParams.djsj"
              :editable="false"
              class="filter-item"
              type="daterange"
              range-separator="~"
              start-placeholder="开始时间"
              end-placeholder="截止时间"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item v-if="view" label="入仓状态" prop="sjzt">
            <DDLNetCodeList v-model="queryParams.sjzt" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon>
                <Search />
              </template>
              搜索
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon>
                <Refresh />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="入仓申请列表">
        <div>
          <el-radio-group
            v-if="check"
            v-model="queryParams.checkstatus"
            @change="handleChangeRadio"
          >
            <el-radio :value="0" border>待审</el-radio>
            <el-radio :value="1" border>已审</el-radio>
          </el-radio-group>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        stripe
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column v-if="add" type="selection" width="55" align="center" fixed />

        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column key="sqdh" label="入仓单号" prop="sqdh" min-width="120" align="center" />

        <el-table-column key="zcmc" label="资产名称" prop="zcmc" min-width="200" align="center" />

        <el-table-column key="syje" label="金额（元）" prop="syje" min-width="150" align="center" />
        <el-table-column
          key="djbmbmname"
          label="申请部门"
          prop="djbmbmname"
          min-width="200"
          align="center"
        />
        <el-table-column key="djr" label="申请人" prop="djrname" min-width="100" align="center" />
        <el-table-column key="djsj" label="登记时间" prop="djsj" min-width="100" align="center" />
        <el-table-column
          key="nodemc"
          label="数据状态"
          prop="nodemc"
          min-width="100"
          align="center"
        />
        <el-table-column fixed="right" label="操作" width="260" align="center">
          <template #default="scope">
            <el-button
              v-if="view && scope.row.sjzt?.startsWith('2')"
              type="primary"
              icon="edit"
              size="small"
              @click="handleOpenDialog(true, scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="view && scope.row.sjzt?.startsWith('2')"
              type="danger"
              size="small"
              @click="handleDelete(scope.row.xh)"
            >
              <template #icon>
                <Delete />
              </template>
              删除
            </el-button>

            <el-button
              v-if="check && queryParams.checkstatus == 0"
              type="primary"
              icon="DocumentChecked"
              size="small"
              @click="handleOpenDialog(false, scope.row)"
            >
              审核
            </el-button>
            <el-button
              v-if="view || (check && queryParams.checkstatus == 1)"
              type="success"
              icon="Document"
              size="small"
              @click="handleOpenDialog(false, scope.row)"
            >
              查看
            </el-button>

            <el-button
              v-if="view && scope.row.isCallBack == '1'"
              type="danger"
              icon="Remove"
              size="small"
              @click="handleCallBack(scope.row.guid)"
            >
              收回
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 10"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 表单弹窗 -->
    <el-drawer v-model="dialog.visible" :title="dialog.title" append-to-body size="80%">
      <el-card>
        <!-- 
        id、guid常规参数
        key用于刷新组件
        editable用于区分是否可编辑
        dcbm调出部门特殊字段
        netcode新增编辑时为空,审核时需要 
        RefreshFatherDrawer关闭抽屉并刷新数据  -->
        <WarehousingView
          :id="itemId"
          :key="itemGuid + '1'"
          :guid="itemGuid"
          :editable="itemEditable"
          :dcbm="itemDcbm"
          :netcode="itemNetcode"
          :checkstatus="queryParams.checkstatus"
          :RefreshFatherDrawer="RefreshFatherDrawer"
        />
      </el-card>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import gwcAPI, { gwcPageQuery, gwcPageVO } from "@/api/properties/gwc";
import WarehousingView from "./components/WarehousingView.vue";
import { getGuid } from "@/utils/guid";
//————————————————————————————————————————————暴露的方法,和请求参数
function RefreshFatherDrawer(close?: boolean) {
  //是否关闭弹窗由参数决定，默认不关闭
  if (close) {
    dialog.visible = false;
  }
  // itemId.value = undefined;
  handleQuery();
}
// 获取路由参数,view/add/check
const route = useRoute();
const type = ref(route.query.type?.toString() || "view");
//显示隐藏权限,用于v-if
const add = type.value == "add";
const view = type.value == "view";
const check = type.value == "check";

//————————————————————————————————————————————查询相关
const queryFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);
const queryParams = reactive<gwcPageQuery>({
  pageNum: 1,
  pageSize: 10,
  type: type.value,
  checkstatus: 0, //用于查询已审和待审
  sqlx: "1",
});
// 表格数据
const pageData = ref<gwcPageVO[]>([]);

/** 查询单据列表 */
function handleQuery() {
  loading.value = true;
  console.log("刷新列表数据");
  gwcAPI
    .getSybPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .catch((error) => {
      console.error("获取数据失败:", error);
      ElMessage.error("获取数据失败");
      pageData.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  queryParams.type = type.value;
  handleQuery();
}
const handleChangeRadio = () => {
  handleResetQuery();
};

//————————————————————————————————————————————操作相关
const removeIds = ref<number[]>([]);
/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: gwcPageVO[]) {
  removeIds.value = selection.map((item) => item.xh).filter((id) => id !== undefined) as number[];
}

/** 删除调拨 */
function handleDelete(id?: number) {
  const ids = [id || removeIds.value].join(",");
  console.log(id);
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      gwcAPI
        .deleteByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .catch((error) => {
          console.error("删除数据失败:", error);
          ElMessage.error("删除数据失败");
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

const handleCallBack = (guid: string) => {
  ElMessageBox.confirm("确认收回吗?收回后可再次提交该单据", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      loading.value = true;
      // transferAPI
      //   .callBack(guid)
      //   .then(() => {
      //     ElMessage.success("操作成功");
      //     handleResetQuery();
      //   })
      //   .catch((error) => {
      //     ElMessage.error("操作失败，失败原因：" + error.message);
      //   })
      //   .finally(() => (loading.value = false));
    })
    .catch(() => {
      ElMessage.info("已取消收回");
    });
};

//————————————————————————————————————————————弹窗相关
// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});
/** 打开弹窗 */
const itemGuid = ref<string>("");
const itemId = ref<number | undefined>();
const itemDcbm = ref<string>("");
const itemEditable = ref<boolean>(false);
const itemNetcode = ref<string>("");
function handleOpenDialog(editable: boolean, row?: gwcPageVO) {
  itemGuid.value = row?.guid || getGuid();
  itemId.value = row?.xh;
  itemDcbm.value = row?.djbmbm || "";
  itemNetcode.value = row?.netcode || "";
  itemEditable.value = editable;
  dialog.title = editable ? (row?.xh ? "编辑入仓单" : "新增入仓单") : "查看入仓单";
  dialog.visible = true;
  console.log("type.value", type.value);
}

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}

.flex {
  display: flex;
  flex-direction: row;
}
</style>
