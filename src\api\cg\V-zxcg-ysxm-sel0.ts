import request from "@/utils/request";

const VZXCGYSXMSEL0_BASE_URL = "/api/v1/vZxcgYsxmSel0s";

const VZxcgYsxmSel0API = {
    /** 获取预算经费分页数据 */
    getPage(queryParams?: VZxcgYsxmSel0PageQuery) {
        return request<any, PageResult<VZxcgYsxmSel0PageVO[]>>({
            url: `${VZXCGYSXMSEL0_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    },
    /**
     * 获取预算经费表单数据
     *
     * @param id VZxcgYsxmSel0ID
     * @returns VZxcgYsxmSel0表单数据
     */
    getFormData(id: number) {
        return request<any, VZxcgYsxmSel0Form>({
            url: `${VZXCGYSXMSEL0_BASE_URL}/${id}/form`,
            method: "get",
        });
    },

    /** 添加预算经费*/
    add(data: VZxcgYsxmSel0Form) {
        return request({
            url: `${VZXCGYSXMSEL0_BASE_URL}`,
            method: "post",
            data: data,
        });
    },

    /**
     * 更新预算经费
     *
     * @param id VZxcgYsxmSel0ID
     * @param data VZxcgYsxmSel0表单数据
     */
     update(id: number, data: VZxcgYsxmSel0Form) {
        return request({
            url: `${VZXCGYSXMSEL0_BASE_URL}/${id}`,
            method: "put",
            data: data,
        });
    },

    /**
     * 批量删除预算经费，多个以英文逗号(,)分割
     *
     * @param ids 预算经费ID字符串，多个以英文逗号(,)分割
     */
     deleteByIds(ids: string) {
        return request({
            url: `${VZXCGYSXMSEL0_BASE_URL}/${ids}`,
            method: "delete",
        });
    }
}

export default VZxcgYsxmSel0API;

/** 预算经费分页查询参数 */
export interface VZxcgYsxmSel0PageQuery extends PageQuery {
    djr?: string;
    ysbm?: string;
    xmbm?: string;
    zgbm?: string;
    fzrname?: string;
    fzr?: string;
}

/** 预算经费表单对象 */
export interface VZxcgYsxmSel0Form {
    djr?:  string;
    ysid?:  number;
    guid?:  string;
    ysbm?:  string;
    ysmc?:  string;
    ysnd?:  string;
    xmbm?:  string;
    zgbm?:  string;
    fzrname?:  string;
    fzr?:  string;
    jflb?:  string;
    boomc1?:  string;
    boomc2?:  string;
    zjly?:  string;
    yszje?:  number;
    ysje?:  number;
    cycount?:  string;
}

/** 预算经费分页对象 */
export interface VZxcgYsxmSel0PageVO {
    djr?: string;
    ysid?: number;
    guid?: string;
    ysbm?: string;
    ysmc?: string;
    ysnd?: string;
    xmbm?: string;
    zgbm?: string;
    fzrname?: string;
    fzr?: string;
    jflb?: string;
    boomc1?: string;
    boomc2?: string;
    zjly?: string;
    yszje?: number;
    ysje?: number;
    cycount?: string;
}
