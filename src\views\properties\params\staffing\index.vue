<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="关键字" prop="keywords">
          <el-input
            v-model="queryParams.keywords"
            placeholder="部门名称"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item>
          <el-button class="filter-item" type="primary" icon="search" @click="handleQuery">
            搜索
          </el-button>
          <el-button icon="refresh" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <el-table v-loading="loading" :data="deptList" style="width: 100%" row-key="ID">
        <el-table-column prop="NAME" label="部门名称" min-width="200" />
        <el-table-column prop="CODE" label="部门编码" width="200" />
        <el-table-column prop="GLYNAME" label="部门资产管理员" width="250" />
        <el-table-column prop="FZRNAME" label="部门负责人" width="250" />
        <el-table-column prop="FGXLDNAME" label="分管校领导" width="250" />
        <el-table-column label="操作" fixed="right" align="left" width="100">
          <template #default="scope">
            <el-button
              v-hasPerm="['sys:dept:edit']"
              type="primary"
              link
              size="small"
              icon="edit"
              @click.stop="handleOpenDialog(scope.row.ID)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      width="600px"
      @closed="handleCloseDialog"
    >
      <el-form ref="deptFormRef" :model="formData" label-width="150px">
        <el-form-item label="调入部门资产管理员" prop="drbmgly">
          <DDLUserList v-model="formData.bmzcgly" :dcode="formData.code" :multiple="true" />
        </el-form-item>
        <el-form-item label="部门负责人" prop="bmfzr">
          <DDLUserList v-model="formData.bmfzr" rcode="0203" />
        </el-form-item>
        <el-form-item label="分管校领导" prop="fgxld">
          <DDLUserList v-model="formData.fgxld" rcode="0207" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
          <el-button @click="handleCloseDialog">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Dept",
  inheritAttrs: false,
});

import DeptAPI, { DeptVO, DeptQuery, DeptForm } from "@/api/system/dept";

const queryFormRef = ref(ElForm);
const loading = ref(false);
const queryParams = reactive<DeptQuery>({});
const deptList = ref<DeptVO[]>();
const formData = reactive<DeptForm>({
  status: 1,
  parentId: "0",
  sort: 1,
});
const dialog = reactive({
  title: "",
  visible: false,
});

// 查询部门
function handleQuery() {
  loading.value = true;
  DeptAPI.getAdminList(queryParams).then((data) => {
    deptList.value = data;
    loading.value = false;
  });
}

// 重置查询
function handleResetQuery() {
  queryFormRef.value.resetFields();
  handleQuery();
}

/**
 * 打开部门弹窗
 */
async function handleOpenDialog(deptId: number) {
  dialog.visible = true;
  DeptAPI.getFormData(deptId).then((data) => {
    Object.assign(formData, data);
    dialog.title = formData.name + "配置管理员";
    console.log(formData);
  });
}
// 关闭弹窗
function handleCloseDialog() {
  dialog.visible = false;
  handleQuery();
}

function handleSubmit() {
  console.log("提交");
  console.log(formData);
  loading.value = true;
  const deptId = formData.id;
  if (deptId) {
    DeptAPI.updateAdmin(deptId, formData)
      .then(() => {
        ElMessage.success("修改成功");
        handleCloseDialog();
        handleQuery();
      })
      .finally(() => (loading.value = false));
  }
}

onMounted(() => {
  handleQuery();
});
</script>
