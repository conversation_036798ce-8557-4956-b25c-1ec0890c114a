<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="120px;">
          <el-form-item label="处置单号" prop="czbh">
            <el-input
              v-model="queryParams.czbh"
              placeholder="处置单号"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item label="处置方式" prop="czlx">
            <DDLYcode v-model="queryParams.czlx" ycode="0102" />
          </el-form-item>
          <el-form-item label="登记时间" prop="djsj">
            <el-date-picker
              v-model="queryParams.djsj"
              class="!w-[240px]"
              type="daterange"
              range-separator="~"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item v-if="view" label="审核环节" prop="" />
          <el-form-item v-if="view" label="审核状态" prop="sjzt">
            <DDLCheckStatus v-model="queryParams.sjzt" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon><Search /></template>
              搜索
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon><Refresh /></template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="处置单列表">
        <div>
          <el-button
            v-if="add"
            type="danger"
            plain
            :disabled="removeIds.length === 0"
            @click="handleDelete()"
          >
            <template #icon><Delete /></template>
            删除
          </el-button>
          <el-radio-group
            v-if="check"
            v-model="queryParams.checkstatus"
            @change="handleChangeRadio"
          >
            <el-radio :value="0" border>待审</el-radio>
            <el-radio :value="1" border>已审</el-radio>
          </el-radio-group>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed />
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column key="czbh" label="处置单号" prop="czbh" min-width="150" align="center" />
        <el-table-column
          key="czlx"
          label="处置类型"
          prop="czlxname"
          min-width="150"
          align="center"
        />
        <el-table-column key="djr" label="申请人" prop="djrname" min-width="150" align="center" />
        <el-table-column
          key="djr"
          label="申请部门"
          prop="djbmname"
          min-width="150"
          align="center"
        />
        <el-table-column key="djsj" label="登记时间" prop="djsj" min-width="150" align="center">
          <template #default="scope">
            <span>{{ format(scope.row.djsj, "YYYY-MM-DD") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          key="netcode"
          label="数据状态"
          prop="netcodename"
          min-width="100"
          align="center"
        />
        <el-table-column fixed="right" label="操作" width="180" align="center">
          <template #default="scope">
            <el-button
              v-if="add"
              type="primary"
              icon="edit"
              size="small"
              @click="handleOpenDialog(true, scope.row)"
            >
              编辑
            </el-button>
            <el-button v-if="add" type="danger" size="small" @click="handleDelete(scope.row.id)">
              <template #icon><Delete /></template>
              删除
            </el-button>
            <el-button
              v-if="check && queryParams.checkstatus == 0"
              type="primary"
              icon="DocumentChecked"
              size="small"
              @click="handleOpenDialog(false, scope.row)"
            >
              审核
            </el-button>
            <el-button
              v-if="view || (check && queryParams.checkstatus == 1)"
              type="success"
              icon="Document"
              size="small"
              @click="handleOpenDialog(false, scope.row)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 处置表单弹窗 -->
    <el-drawer v-model="dialog.visible" :title="dialog.title" append-to-body size="75%">
      <el-card>
        <DisposeView
          :key="itemGuid + '1'"
          :guid="itemGuid"
          :editable="itemEditable"
          :netcode="itemNetcode"
          :djbm="itemDjbm"
          :checkstatus="queryParams.checkstatus"
          :RefreshFatherDrawer="handleCloseDialog"
        />
      </el-card>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import disposeAPI, { disposePageVO, disposePageQuery } from "@/api/properties/dispose";
import DisposeEdit from "./components/DisposeEdit.vue";
import DisposeView from "./components/DisposeView.vue";
import { format } from "@/utils/day";
//————————————————————————————————————————————暴露的方法,和请求参数
/** 关闭调拨弹窗 */
function handleCloseDialog(close?: boolean) {
  if (close) {
    dialog.visible = false;
  }
  handleQuery();
}
// 获取路由参数,view/add/check
const route = useRoute();
const type = ref(route.query.type?.toString() || "view");
//显示隐藏权限,用于v-if
const add = type.value == "add";
const view = type.value == "view";
const check = type.value == "check";
//————————————————————————————————————————————查询相关
//页面参数
const queryFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);
const queryParams = reactive<disposePageQuery>({
  pageNum: 1,
  pageSize: 10,
  type: type.value,
  checkstatus: 0,
});

// 处置表格数据
const pageData = ref<disposePageVO[]>([]);

/** 查询处置 */
function handleQuery() {
  loading.value = true;
  disposeAPI
    .getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .catch((error) => {
      console.error("获取数据失败:", error);
      ElMessage.error("获取数据失败");
      pageData.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置处置查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}
const handleChangeRadio = () => {
  handleQuery();
};
//————————————————————————————————————————————操作相关
const removeIds = ref<number[]>([]);

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  removeIds.value = selection.map((item: any) => item.id);
}

/** 删除处置 */
function handleDelete(id?: number) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      disposeAPI
        .deleteByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

//————————————————————————————————————————————弹窗相关
const dialog = reactive({
  title: "",
  visible: false,
});
// 打开弹窗
/** 打开弹窗 */
const itemGuid = ref<string>("");
const itemId = ref<number | undefined>();
const itemEditable = ref<boolean>(false);
const itemDjbm = ref<string>("");
const itemNetcode = ref<string>("");
function handleOpenDialog(editable: boolean, row?: any) {
  //打开弹窗，如果没有guid则生成一个GUID
  itemGuid.value = row?.guid;
  itemId.value = row?.id;
  itemEditable.value = editable;
  itemDjbm.value = row?.djbm;
  itemNetcode.value = row?.netcode || "";
  dialog.visible = true;
}

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.el-form {
  width: auto;
}
.el-form-item {
  width: 330px;
}
</style>
