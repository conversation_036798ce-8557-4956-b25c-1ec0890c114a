<template>
  <div class="app-container">
   <el-card>
    <!-- ********************** 用插槽显示筛选框 ********************** -->
    <template #header>
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
                <el-form-item label="$fieldConfig.fieldComment" prop="djr">
                      <el-input
                          v-model="queryParams.djr"
                          placeholder="$fieldConfig.fieldComment"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="ysbm">
                      <el-input
                          v-model="queryParams.ysbm"
                          placeholder="$fieldConfig.fieldComment"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="xmbm">
                      <el-input
                          v-model="queryParams.xmbm"
                          placeholder="$fieldConfig.fieldComment"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="zgbm">
                      <el-input
                          v-model="queryParams.zgbm"
                          placeholder="$fieldConfig.fieldComment"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="fzrname">
                      <el-input
                          v-model="queryParams.fzrname"
                          placeholder="$fieldConfig.fieldComment"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="fzr">
                      <el-input
                          v-model="queryParams.fzr"
                          placeholder="$fieldConfig.fieldComment"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon><Search /></template>
            搜索
          </el-button>
          <el-button @click="handleResetQuery">
            <template #icon><Refresh /></template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="列表">
        <div>
          <el-button
              v-hasPerm="['cg:vZxcgYsxmSel0:add']"
              type="success"
              plain
              size="small"
              @click="formType.view = false; handleEdit()"
          >
            <template #icon><Plus /></template>
            新增
          </el-button>
          <el-button
              v-hasPerm="['cg:vZxcgYsxmSel0:delete']"
              type="danger"
              plain
              size="small"
              :disabled="removeIds.length === 0"
              @click="handleDelete()"
          >
            <template #icon><Delete /></template>
            删除
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
          ref="dataTableRef"
          v-loading="loading"
          :data="pageData"
          highlight-current-row
          border
          @selection-change="handleSelectionChange"
          @expand-change="handleExpandChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed />
        <el-table-column label="展开" type="expand" width="55">
          <template #default="props">
            <div style="padding: 0px 70px; background-color: #ffffff">
              <!-- <TransferPropertiesList
                :dcbm="props.row.dcbm"
                :guid="props.row.guid || ''"
              ></TransferPropertiesList> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
                <el-table-column
                    key="djr"
                    label="$fieldConfig.fieldComment"
                    prop="djr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysid"
                    label="$fieldConfig.fieldComment"
                    prop="ysid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="guid"
                    label="$fieldConfig.fieldComment"
                    prop="guid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysbm"
                    label="$fieldConfig.fieldComment"
                    prop="ysbm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysmc"
                    label="$fieldConfig.fieldComment"
                    prop="ysmc"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysnd"
                    label="$fieldConfig.fieldComment"
                    prop="ysnd"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xmbm"
                    label="$fieldConfig.fieldComment"
                    prop="xmbm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zgbm"
                    label="$fieldConfig.fieldComment"
                    prop="zgbm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="fzrname"
                    label="$fieldConfig.fieldComment"
                    prop="fzrname"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="fzr"
                    label="$fieldConfig.fieldComment"
                    prop="fzr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="jflb"
                    label="$fieldConfig.fieldComment"
                    prop="jflb"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="boomc1"
                    label="$fieldConfig.fieldComment"
                    prop="boomc1"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="boomc2"
                    label="$fieldConfig.fieldComment"
                    prop="boomc2"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zjly"
                    label="$fieldConfig.fieldComment"
                    prop="zjly"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="yszje"
                    label="$fieldConfig.fieldComment"
                    prop="yszje"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysje"
                    label="$fieldConfig.fieldComment"
                    prop="ysje"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cycount"
                    label="$fieldConfig.fieldComment"
                    prop="cycount"
                    min-width="150"
                    align="center"
                />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button
                v-hasPerm="['cg:vZxcgYsxmSel0:edit']"
                type="primary"
                size="small"
                icon="Edit"
                link
                @click="formType.view = false;handleRowEdit(scope.row.id)"
            >
              编辑
            </el-button>
            <el-button
                v-hasPerm="['cg:vZxcgYsxmSel0:delete']"
                type="danger"
                size="small"
                icon="Delete"
                link
                @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
            <el-button
              v-if="check"
              type="primary"
              icon="DocumentChecked"
              size="small"
              link
              @click="hancleRowCheck"
            >
              审核
            </el-button>
            <el-button
              v-if="view"
              type="success"
              icon="Document"
              size="small"
              link
              @click="hancleRowView"
            >
              查看
            </el-button>
            <el-button
              v-if="view"
              type="primary"
              icon="Printer"
              size="small"
              link
              @click="formType.view = true;hancleRowPrint"
            >
              打印
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="handleQuery()"
      />
    </el-card>

    <!-- 预算经费表单弹窗 -->   
    <el-drawer 
      v-model="dialog.visible" 
      :title="dialog.title" 
      append-to-body 
      size="75%"
      :before-close="handleCloseDialog"

    >
<el-card>
        <template #header>
          <Title name="XX信息">
            <div>
              <el-button
                v-if="!formType.view"
                type="primary"
                @click="handleSubmit()"
              >
                保存
              </el-button>
              <el-button v-if="!formType.view" type="danger" @click="handleSubmit()">
                提交
              </el-button>
            </div>
          </Title>
        </template>



      <el-form ref="dataFormRef" :model="formData" :rules="rules" label-width="100px"  :inline="true"
          :disabled="formType.view">
                <el-form-item label="$fieldConfig.fieldComment" prop="djr">
                      <el-input
                          v-model="formData.djr"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="ysid">
                      <el-input
                          v-model="formData.ysid"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="guid">
                      <el-input
                          v-model="formData.guid"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="ysbm">
                      <el-input
                          v-model="formData.ysbm"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="ysmc">
                      <el-input
                          v-model="formData.ysmc"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="ysnd">
                      <el-input
                          v-model="formData.ysnd"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="xmbm">
                      <el-input
                          v-model="formData.xmbm"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="zgbm">
                      <el-input
                          v-model="formData.zgbm"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="fzrname">
                      <el-input
                          v-model="formData.fzrname"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="fzr">
                      <el-input
                          v-model="formData.fzr"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="jflb">
                      <el-input
                          v-model="formData.jflb"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="boomc1">
                      <el-input
                          v-model="formData.boomc1"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="boomc2">
                      <el-input
                          v-model="formData.boomc2"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="zjly">
                      <el-input
                          v-model="formData.zjly"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="yszje">
                      <el-input
                          v-model="formData.yszje"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="ysje">
                      <el-input
                          v-model="formData.ysje"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="cycount">
                      <el-input
                          v-model="formData.cycount"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
      </el-form>
      <template #footer></template>
     </el-card>
    </ el-drawer>
  </div>
</template>

<script setup lang="ts">
  defineOptions({
    name: "VZxcgYsxmSel0",
    inheritAttrs: false,
  });
  import { useUserStore } from "@/store";
  import VZxcgYsxmSel0API, { VZxcgYsxmSel0PageVO, VZxcgYsxmSel0Form, VZxcgYsxmSel0PageQuery } from "@/api/cg/V-zxcg-ysxm-sel0";

  // 获取路由参数,view/add/check
  const route = useRoute();
  const type = ref(route.query.type?.toString() || "view");
  //显示隐藏权限,用于v-if
  const add = type.value == "add";
  const view = type.value == "view";
  const check = type.value == "check";
  //页面参数
  const queryFormRef = ref(ElForm);
  const loading = ref(false);
  const removeIds = ref<number[]>([]);
  const total = ref(0);

  const queryParams = reactive<VZxcgYsxmSel0PageQuery>({
    pageNum: 1,
    pageSize: 10,
  });

  // 预算经费表格数据
  const pageData = ref<VZxcgYsxmSel0PageVO[]>([]);

  // 弹窗
  const dialog = reactive({
    title: "",
    visible: false,
  });



  /** 查询预算经费 */
  function handleQuery() {
    loading.value = true;
          VZxcgYsxmSel0API.getPage(queryParams)
        .then((data) => {
          pageData.value = data.list;
          total.value = data.total;
        })
        .finally(() => {
          loading.value = false;
        });
  }

  /** 重置预算经费查询 */
  function handleResetQuery() {
    queryFormRef.value!.resetFields();
    queryParams.pageNum = 1;
    handleQuery();
  }

  /** 行复选框选中记录选中ID集合 */
  function handleSelectionChange(selection: any) {
    removeIds.value = selection.map((item: any) => item.id);
  }

  /** 打开预算经费弹窗 */
  function handleEdit(id?: number) {
    dialog.visible = true;
    if (id) {
      dialog.title = "修改预算经费";
            VZxcgYsxmSel0API.getFormData(id).then((data) => {
        Object.assign(formData, data);
      });
    } else {
      dialog.title = "新增预算经费";
    }
  }

 

  /** 关闭预算经费弹窗 */
  function handleCloseDialog() {
    dialog.visible = false;
    dataFormRef.value.resetFields();
    dataFormRef.value.clearValidate();
    Object.assign(formData, {});

  }

  /** 删除预算经费 */
  function handleDelete(id?: number) {
    const ids = [id || removeIds.value].join(",");
    if (!ids) {
      ElMessage.warning("请勾选删除项");
      return;
    }

    ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(
        () => {
          loading.value = true;
                VZxcgYsxmSel0API.deleteByIds(ids)
              .then(() => {
                ElMessage.success("删除成功");
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        },
        () => {
          ElMessage.info("已取消删除");
        }
    );
  }

  onMounted(() => {
    handleQuery();
  });

  //--------------以下是form表单相关
const dataFormRef = ref(ElForm);

  // 预算经费表单数据
  const formData = reactive<VZxcgYsxmSel0Form>({});

const formType = reactive({
  add: false,
  edit: false,
  view: false,
});

  // 预算经费表单校验规则
  const rules = reactive({
                      guid: [{ required: true, message: "请输入${fieldConfig.fieldComment}", trigger: "blur" }],
                      ysbm: [{ required: true, message: "请输入${fieldConfig.fieldComment}", trigger: "blur" }],
                      ysmc: [{ required: true, message: "请输入${fieldConfig.fieldComment}", trigger: "blur" }],
                      fzrname: [{ required: true, message: "请输入${fieldConfig.fieldComment}", trigger: "blur" }],
                      fzr: [{ required: true, message: "请输入${fieldConfig.fieldComment}", trigger: "blur" }],
                      ysje: [{ required: true, message: "请输入${fieldConfig.fieldComment}", trigger: "blur" }],
  });

   /** 提交预算经费表单 */
  function handleSubmit() {
    dataFormRef.value.validate((valid: any) => {
      if (valid) {
        loading.value = true;
        const id = formData.id;
        if (id) {
                VZxcgYsxmSel0API.update(id, formData)
              .then(() => {
                ElMessage.success("修改成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        } else {
                VZxcgYsxmSel0API.add(formData)
              .then(() => {
                ElMessage.success("新增成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        }
      }
    });
  }
</script>
<style lang="scss" scoped>

</style>
