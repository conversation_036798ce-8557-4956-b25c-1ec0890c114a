import request from "@/utils/request";

const INFOGYS_BASE_URL = "/api/v1/infoGyss";

const InfoGysAPI = {
  /** 获取InfoGys分页数据 */
  getPage(queryParams?: InfoGysPageQuery) {
    return request<any, PageResult<InfoGysPageVO[]>>({
      url: `${INFOGYS_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取InfoGys表单数据
   *
   * @param id InfoGysID
   * @returns InfoGys表单数据
   */
  getFormData(id: number) {
    return request<any, InfoGysForm>({
      url: `${INFOGYS_BASE_URL}/${id}/form`,
      method: "get",
    });
  },

  /** 添加InfoGys*/
  add(data: InfoGysForm) {
    return request({
      url: `${INFOGYS_BASE_URL}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新InfoGys
   *
   * @param id InfoGysID
   * @param data InfoGys表单数据
   */
  update(id: number, data: InfoGysForm) {
    return request({
      url: `${INFOGYS_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 批量删除InfoGys，多个以英文逗号(,)分割
   *
   * @param ids InfoGysID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${INFOGYS_BASE_URL}/${ids}`,
      method: "delete",
    });
  },
};

export default InfoGysAPI;

/** InfoGys分页查询参数 */
export interface InfoGysPageQuery extends PageQuery {
  /** 机构编码 */
  code?: string;
  /** 机构名称 */
  cname?: string;
}

/** InfoGys表单对象 */
export interface InfoGysForm {
  /** 供应商注册表ID */
  id?: number;
  /** 系统编码(后台唯一码) */
  xtbm?: string;
  /** Guid */
  guid?: string;
  /** 机构编码 */
  code?: string;
  /** 机构名称 */
  cname?: string;
  /** 机构英文名 */
  ename?: string;
  /** 单位性质(XCODE) */
  type?: string;
  /** 行政区域（省市区(县)） */
  xzqy?: string;
  /** 单位地址 */
  address?: string;
  /** 邮编 */
  ecode?: string;
  /** 单位电话 */
  dwtel?: string;
  /** 单位传真 */
  fox?: string;
  /** 单位邮箱 */
  email?: string;
  /** 单位网站 */
  web?: string;
  /** 营业执照注册号 */
  yyzz?: string;
  /** 营业执照照片 */
  yyzzpic?: string;
  /** 国税登记证编号 */
  gsdjz?: string;
  /** 地税登记证编号 */
  dsdjz?: string;
  /** 税务登记证照片 */
  swdjzpic?: string;
  /** 组织机构代码证照片 */
  zzjgpic?: string;
  /** 法人代表身份证照片 */
  frpic?: string;
  /** 注册资金 */
  zczj?: number;
  /** 资产总额 */
  zcze?: number;
  /** 资金类别（XCODE） */
  zjlb?: string;
  /** 所属行业（Xcode） */
  sshy?: string;
  /** 企业人数(xcode) */
  qymember?: string;
  /** 经营范围 */
  jyfw?: string;
  /** 银行帐号 */
  yhzh?: string;
  /** 开户银行(Xcode) */
  khyh?: string;
  /** 注册有效期 */
  zcyxq?: Date;
  /** 注册日期 */
  zcrq?: Date;
  /** 法人姓名 */
  frname?: string;
  /** 法人身份证 */
  frsfz?: string;
  /** 法人固定电话 */
  frtel?: string;
  /** 法人手机 */
  frmobie?: string;
  /** 联系人姓名 */
  lxrname?: string;
  /** 联系人身份证 */
  lxrsfz?: string;
  /** 联系人职务 */
  lxrzw?: string;
  /** 联系人固定电话 */
  lxrtel?: string;
  /** 联系人手机 */
  lxrmobie?: string;
  /** 联系人传真 */
  lxrfox?: string;
  /** 联系人邮箱 */
  lxremail?: string;
  /** 备注 */
  note?: string;
  /** 登记日期 */
  djrq?: Date;
  /** 审核日期 */
  shrq?: Date;
  /** 审核内容 */
  shnr?: string;
  /** 审核状态（0未审，1退回，3通过，4禁用） */
  szjt?: string;
  /** 登录名 */
  name?: string;
  /** 密码 */
  password?: string;
  /** 用户类型 */
  role?: string;
  /** Flag */
  flag?: string;
  /** 供应商信用等级 */
  xydj?: string;
  /** 评价内容 */
  pjnr?: string;
  /** 质量 */
  zl?: string;
  /** 速度 */
  sd?: string;
  /** 态度 */
  td?: string;
  /** 财务返回的供应商编码 */
  provCode?: string;
  /** 联行号 */
  lhh?: string;
  /** 收款人户名 */
  skrhm?: string;
}

/** InfoGys分页对象 */
export interface InfoGysPageVO {
  /** 供应商注册表ID */
  id?: number;
  /** 系统编码(后台唯一码) */
  xtbm?: string;
  /** Guid */
  guid?: string;
  /** 机构编码 */
  code?: string;
  /** 机构名称 */
  cname?: string;
  /** 机构英文名 */
  ename?: string;
  /** 单位性质(XCODE) */
  type?: string;
  /** 行政区域（省市区(县)） */
  xzqy?: string;
  /** 单位地址 */
  address?: string;
  /** 邮编 */
  ecode?: string;
  /** 单位电话 */
  dwtel?: string;
  /** 单位传真 */
  fox?: string;
  /** 单位邮箱 */
  email?: string;
  /** 单位网站 */
  web?: string;
  /** 营业执照注册号 */
  yyzz?: string;
  /** 营业执照照片 */
  yyzzpic?: string;
  /** 国税登记证编号 */
  gsdjz?: string;
  /** 地税登记证编号 */
  dsdjz?: string;
  /** 税务登记证照片 */
  swdjzpic?: string;
  /** 组织机构代码证照片 */
  zzjgpic?: string;
  /** 法人代表身份证照片 */
  frpic?: string;
  /** 注册资金 */
  zczj?: number;
  /** 资产总额 */
  zcze?: number;
  /** 资金类别（XCODE） */
  zjlb?: string;
  /** 所属行业（Xcode） */
  sshy?: string;
  /** 企业人数(xcode) */
  qymember?: string;
  /** 经营范围 */
  jyfw?: string;
  /** 银行帐号 */
  yhzh?: string;
  /** 开户银行(Xcode) */
  khyh?: string;
  /** 注册有效期 */
  zcyxq?: Date;
  /** 注册日期 */
  zcrq?: Date;
  /** 法人姓名 */
  frname?: string;
  /** 法人身份证 */
  frsfz?: string;
  /** 法人固定电话 */
  frtel?: string;
  /** 法人手机 */
  frmobie?: string;
  /** 联系人姓名 */
  lxrname?: string;
  /** 联系人身份证 */
  lxrsfz?: string;
  /** 联系人职务 */
  lxrzw?: string;
  /** 联系人固定电话 */
  lxrtel?: string;
  /** 联系人手机 */
  lxrmobie?: string;
  /** 联系人传真 */
  lxrfox?: string;
  /** 联系人邮箱 */
  lxremail?: string;
  /** 备注 */
  note?: string;
  /** 登记日期 */
  djrq?: Date;
  /** 审核日期 */
  shrq?: Date;
  /** 审核内容 */
  shnr?: string;
  /** 审核状态（0未审，1退回，3通过，4禁用） */
  szjt?: string;
  /** 登录名 */
  name?: string;
  /** 密码 */
  password?: string;
  /** 用户类型 */
  role?: string;
  /** Flag */
  flag?: string;
  /** 供应商信用等级 */
  xydj?: string;
  /** 评价内容 */
  pjnr?: string;
  /** 质量 */
  zl?: string;
  /** 速度 */
  sd?: string;
  /** 态度 */
  td?: string;
  /** 财务返回的供应商编码 */
  provCode?: string;
  /** 联行号 */
  lhh?: string;
  /** 收款人户名 */
  skrhm?: string;
}
