<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item label="合同编号" prop="htbh">
            <el-input
              v-model="queryParams.htbh"
              placeholder="合同编号"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item label="合同名称" prop="htmc">
            <el-input
              v-model="queryParams.htmc"
              placeholder="合同名称"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon><Search /></template>
              搜索
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon><Refresh /></template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="列表">
        <div>
          <el-button
            v-if="add"
            type="success"
            plain
            size="small"
            @click="handleOpenDialog(true, 0, '')"
          >
            <template #icon><Plus /></template>
            新增
          </el-button>
          <el-button
            v-if="add"
            type="danger"
            plain
            size="small"
            :disabled="removeIds.length === 0"
            @click="handleDelete()"
          >
            <template #icon><Delete /></template>
            删除
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column v-if="add" type="selection" width="55" align="center" fixed />
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>

        <el-table-column key="htbh" label="合同编号" prop="htbh" min-width="150" align="center" />
        <el-table-column key="htmc" label="合同名称" prop="htmc" min-width="150" align="center" />
        <el-table-column
          key="yflxr"
          label="乙方联系人"
          prop="yflxr"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="yflxfs"
          label="乙方联系方式"
          prop="yflxfs"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="jflxr"
          label="甲方联系人"
          prop="jflxr"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="jflxfs"
          label="甲方联系方式"
          prop="jflxfs"
          min-width="150"
          align="center"
        />
        <el-table-column key="htje" label="合同金额" prop="htje" min-width="150" align="center" />
        <el-table-column key="djsj" label="登记时间" prop="djsj" min-width="150" align="center">
          <template #default="scope">
            <span>{{ format(scope.row.djsj, "YYYY-MM-DD") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          key="netcode"
          label="数据状态"
          prop="netcodename"
          min-width="100"
          align="center"
        />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button
              v-if="add"
              type="primary"
              size="small"
              icon="Edit"
              link
              @click="handleOpenDialog(true, scope.row.htid, scope.row.guid)"
            >
              编辑
            </el-button>
            <el-button
              v-if="add"
              type="danger"
              size="small"
              icon="Delete"
              link
              @click="handleDelete(scope.row.guid)"
            >
              删除
            </el-button>
            <el-button
              v-if="check"
              type="primary"
              icon="DocumentChecked"
              size="small"
              link
              @click="handleOpenDialog(false, scope.row.htid, scope.row.guid)"
            >
              审核
            </el-button>
            <el-button
              type="success"
              icon="Document"
              size="small"
              link
              @click="handleOpenDialog(false, scope.row.htid, scope.row.guid)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 采购合同表单弹窗 -->
    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      append-to-body
      size="75%"
      :before-close="handleCloseDialog"
    >
      <el-card>
        <el-steps
          style="width: 100%"
          :active="dialog.active"
          finish-status="success"
          title="创建步骤"
          simple
        >
          <el-step title="1.采购合同起草" />
        </el-steps>
        <htqc-info
          v-if="dialog.active == 1"
          :guid="dialog.guid"
          :editable="dialog.editEnable"
          @nowstep="handleNowstep"
        />
      </el-card>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "cght",
  inheritAttrs: false,
});
import { useUserStore } from "@/store";
import ZxcgCghtAPI, { ZxcgCghtPageVO, ZxcgCghtForm, ZxcgCghtPageQuery } from "@/api/cg/cght";
import { useRoute } from "vue-router";
import { format } from "@/utils/day";

// 获取路由参数,view/add/check
const route = useRoute();
const type = ref(route.query.type?.toString() || "view");
//显示隐藏权限,用于v-if
const add = type.value == "edit";
const view = type.value == "view";
const check = type.value == "sp";
//页面参数
const queryFormRef = ref(ElForm);
const loading = ref(false);
const removeIds = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<ZxcgCghtPageQuery>({
  pageNum: 1,
  pageSize: 10,
});
queryParams.pagetype = type.value;

// 采购合同表格数据
const pageData = ref<ZxcgCghtPageVO[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  guid: "",
  htid: 0,
  visible: false,
  editEnable: false,
  active: 1,
});

/** 查询采购合同 */
function handleQuery() {
  loading.value = true;
  ZxcgCghtAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置采购合同查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  removeIds.value = selection.map((item: any) => item.id);
}

/** 打开采购合同弹窗 */
function handleOpenDialog(editEnable: boolean, htid?: number, guid?: string) {
  dialog.htid = htid || 0;
  dialog.visible = true;
  dialog.editEnable = editEnable;
  dialog.active = 1;
  console.log("handleOpenDialog", editEnable, guid);
  if (guid) {
    dialog.title = "修改采购合同";
    dialog.guid = guid;
  } else {
    dialog.title = "新增采购合同";
    dialog.guid = "";
  }
}
const handleNowstep = (newName: number) => {
  dialog.active = newName;
};
/** 关闭采购合同弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  handleQuery();
}

/** 删除采购合同 */
function handleDelete(id?: number) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      ZxcgCghtAPI.deleteByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped></style>
