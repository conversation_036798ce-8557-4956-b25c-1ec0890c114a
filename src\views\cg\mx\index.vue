<template>
  <div class="app-container">
   <el-card>
    <!-- ********************** 用插槽显示筛选框 ********************** -->
    <template #header>
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
                <el-form-item label="母表GUID|hidden" prop="parentguid">
                      <el-input
                          v-model="queryParams.parentguid"
                          placeholder="母表GUID|hidden"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="外键，自增|hidden" prop="sqid">
                      <el-input
                          v-model="queryParams.sqid"
                          placeholder="外键，自增|hidden"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="明细编号" prop="mxbh">
                      <el-input
                          v-model="queryParams.mxbh"
                          placeholder="明细编号"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="物品名称" prop="wpmc">
                      <el-input
                          v-model="queryParams.wpmc"
                          placeholder="物品名称"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="rwid|hidden" prop="rwid">
                      <el-input
                          v-model="queryParams.rwid"
                          placeholder="rwid|hidden"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="wtid|hidden" prop="wtid">
                      <el-input
                          v-model="queryParams.wtid"
                          placeholder="wtid|hidden"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="wjid|hidden" prop="wjid">
                      <el-input
                          v-model="queryParams.wjid"
                          placeholder="wjid|hidden"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="bdid|hidden" prop="bdid">
                      <el-input
                          v-model="queryParams.bdid"
                          placeholder="bdid|hidden"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon><Search /></template>
            搜索
          </el-button>
          <el-button @click="handleResetQuery">
            <template #icon><Refresh /></template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="列表">
        <div>
          <el-button
              v-hasPerm="['cg:zxcgMx:add']"
              type="success"
              plain
              size="small"
              @click="formType.view = false; handleEdit()"
          >
            <template #icon><Plus /></template>
            新增
          </el-button>
          <el-button
              v-hasPerm="['cg:zxcgMx:delete']"
              type="danger"
              plain
              size="small"
              :disabled="removeIds.length === 0"
              @click="handleDelete()"
          >
            <template #icon><Delete /></template>
            删除
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
          ref="dataTableRef"
          v-loading="loading"
          :data="pageData"
          highlight-current-row
          border
          @selection-change="handleSelectionChange"
          @expand-change="handleExpandChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed />
        <el-table-column label="展开" type="expand" width="55">
          <template #default="props">
            <div style="padding: 0px 70px; background-color: #ffffff">
              <!-- <TransferPropertiesList
                :dcbm="props.row.dcbm"
                :guid="props.row.guid || ''"
              ></TransferPropertiesList> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
                <el-table-column
                    key="mxid"
                    label="采购明细表|hidden"
                    prop="mxid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="guid"
                    label="guid|hidden"
                    prop="guid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="parentguid"
                    label="母表GUID|hidden"
                    prop="parentguid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sqid"
                    label="外键，自增|hidden"
                    prop="sqid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="type"
                    label="类别|hidden"
                    prop="type"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysmx"
                    label="预算明细|hidden"
                    prop="ysmx"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="mxbh"
                    label="明细编号"
                    prop="mxbh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="wpmc"
                    label="物品名称"
                    prop="wpmc"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cgml"
                    label="采购目录"
                    prop="cgml"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ppcs"
                    label="品牌厂商"
                    prop="ppcs"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="gg"
                    label="规格"
                    prop="gg"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xxcs"
                    label="详细参数"
                    prop="xxcs"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ists"
                    label="是否特殊 进口、辐射、其它|hidden"
                    prop="ists"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="dw"
                    label="单位"
                    prop="dw"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sl"
                    label="数量"
                    prop="sl"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="price"
                    label="单价（元）"
                    prop="price"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysje"
                    label="金额（元）"
                    prop="ysje"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="time1"
                    label="开始时间|hidden"
                    prop="time1"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="time2"
                    label="结束时间|hidden"
                    prop="time2"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="notes"
                    label="其他说明|hidden"
                    prop="notes"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="gys"
                    label="供应商|hidden"
                    prop="gys"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="jgdm"
                    label="组织机构代码|hidden"
                    prop="jgdm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="gysfr"
                    label="法人|hidden"
                    prop="gysfr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="frsfz"
                    label="法人身份证|hidden"
                    prop="frsfz"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="gyslxr"
                    label="供应商联系人|hidden"
                    prop="gyslxr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="gystel"
                    label="联系电话|hidden"
                    prop="gystel"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="khyh"
                    label="资产类型（0资产，1非资产）|hidden"
                    prop="khyh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="khzh"
                    label="开户帐号|hidden"
                    prop="khzh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xsl"
                    label="采购数量|hidden"
                    prop="xsl"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cjjg"
                    label="成交价格(元)|hidden"
                    prop="cjjg"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="isba"
                    label="是否备案（0 否，1是）|hidden"
                    prop="isba"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="rwid"
                    label="rwid|hidden"
                    prop="rwid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="wtid"
                    label="wtid|hidden"
                    prop="wtid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="wmwtid"
                    label="wmwtid|hidden"
                    prop="wmwtid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="wjid"
                    label="wjid|hidden"
                    prop="wjid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="baid"
                    label="baid|hidden"
                    prop="baid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="bdid"
                    label="bdid|hidden"
                    prop="bdid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="mxzt"
                    label="明细状态 0执行，1完成|hidden"
                    prop="mxzt"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="yt"
                    label="用途？|hidden"
                    prop="yt"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="syr"
                    label="使用人|hidden"
                    prop="syr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cfdd"
                    label="存放地点|hidden"
                    prop="cfdd"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xysl"
                    label="协议数量？|hidden"
                    prop="xysl"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sgly"
                    label="申购理由"
                    prop="sgly"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cjprice"
                    label="成交价？|hidden"
                    prop="cjprice"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sfjk"
                    label="是否进口"
                    prop="sfjk"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysbm"
                    label="预算编码"
                    prop="ysbm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="iscb"
                    label="是否超标 0否 1是 （可以超出采购目录限额的金额）"
                    prop="iscb"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sdje"
                    label="审定金额|hidden"
                    prop="sdje"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="hjly"
                    label="核减理由|hidden"
                    prop="hjly"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="bCode"
                    label="费用项"
                    prop="bCode"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="buCode"
                    label="预算项"
                    prop="buCode"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by1"
                    label="材料类型管理|hidden"
                    prop="by1"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xh"
                    label="材料类型管理|hidden"
                    prop="xh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zcydz"
                    label="政采云链接地址"
                    prop="zcydz"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by2"
                    label="用款指标ID（BudgetId）"
                    prop="by2"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by3"
                    label="用款明细指标ID（DetailId）"
                    prop="by3"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="fltk"
                    label="采购方式适用的法律条款代码"
                    prop="fltk"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="qrswh"
                    label="确认书文号"
                    prop="qrswh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="yxj"
                    label="项目优先级程度0普通  1 紧急"
                    prop="yxj"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="babz"
                    label="备案标志0 不备案 1 外网备案 2 内网备案"
                    prop="babz"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sfbm"
                    label="是否保密0 保密  1 普通"
                    prop="sfbm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by5"
                    label="付款方式"
                    prop="by5"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by4"
                    label="资金类型"
                    prop="by4"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="by9"
                    label="政采云购物车过来的数据"
                    prop="by9"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="jkly"
                    label="进口理由"
                    prop="jkly"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sbjg"
                    label="浙江省财建议书上报结果 0失败 1成功"
                    prop="sbjg"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sbjgsm"
                    label="上报结果说明"
                    prop="sbjgsm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zxid"
                    label="询价，竞价表id，合并明细使用"
                    prop="zxid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="yxbh"
                    label="意向编号"
                    prop="yxbh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="qrsbh"
                    label="确认书编号"
                    prop="qrsbh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cfguid"
                    label="被拆分的明细的guid,如果不为空，表示已经被拆分过了"
                    prop="cfguid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="isbg"
                    label="是否变更"
                    prop="isbg"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="mxordbh"
                    label="明细订单编号（冻结解冻）"
                    prop="mxordbh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="wcid"
                    label="网超推荐id"
                    prop="wcid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cfmx"
                    label="经贸用的，作废"
                    prop="cfmx"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysjf"
                    label="经贸用的，作废"
                    prop="ysjf"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="dqrsbh"
                    label="大确认书编号"
                    prop="dqrsbh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cpguid"
                    label="base_product.guid"
                    prop="cpguid"
                    min-width="150"
                    align="center"
                />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button
                v-hasPerm="['cg:zxcgMx:edit']"
                type="primary"
                size="small"
                icon="Edit"
                link
                @click="formType.view = false;handleRowEdit(scope.row.id)"
            >
              编辑
            </el-button>
            <el-button
                v-hasPerm="['cg:zxcgMx:delete']"
                type="danger"
                size="small"
                icon="Delete"
                link
                @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
            <el-button
              v-if="check"
              type="primary"
              icon="DocumentChecked"
              size="small"
              link
              @click="hancleRowCheck"
            >
              审核
            </el-button>
            <el-button
              v-if="view"
              type="success"
              icon="Document"
              size="small"
              link
              @click="hancleRowView"
            >
              查看
            </el-button>
            <el-button
              v-if="view"
              type="primary"
              icon="Printer"
              size="small"
              link
              @click="formType.view = true;hancleRowPrint"
            >
              打印
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="handleQuery()"
      />
    </el-card>

    <!-- 采购申请明细表单弹窗 -->   
    <el-drawer 
      v-model="dialog.visible" 
      :title="dialog.title" 
      append-to-body 
      size="75%"
      :before-close="handleCloseDialog"

    >
<el-card>
        <template #header>
          <Title name="XX信息">
            <div>
              <el-button
                v-if="!formType.view"
                type="primary"
                @click="handleSubmit()"
              >
                保存
              </el-button>
              <el-button v-if="!formType.view" type="danger" @click="handleSubmit()">
                提交
              </el-button>
            </div>
          </Title>
        </template>



      <el-form ref="dataFormRef" :model="formData" :rules="rules" label-width="100px"  :inline="true"
          :disabled="formType.view">
                <el-form-item label="采购明细表|hidden" prop="mxid">
                      <el-input
                          v-model="formData.mxid"
                          placeholder="采购明细表|hidden"
                      />
                </el-form-item>
                <el-form-item label="guid|hidden" prop="guid">
                      <el-input
                          v-model="formData.guid"
                          placeholder="guid|hidden"
                      />
                </el-form-item>
                <el-form-item label="母表GUID|hidden" prop="parentguid">
                      <el-input
                          v-model="formData.parentguid"
                          placeholder="母表GUID|hidden"
                      />
                </el-form-item>
                <el-form-item label="外键，自增|hidden" prop="sqid">
                      <el-input
                          v-model="formData.sqid"
                          placeholder="外键，自增|hidden"
                      />
                </el-form-item>
                <el-form-item label="类别|hidden" prop="type">
                      <el-input
                          v-model="formData.type"
                          placeholder="类别|hidden"
                      />
                </el-form-item>
                <el-form-item label="预算明细|hidden" prop="ysmx">
                      <el-input
                          v-model="formData.ysmx"
                          placeholder="预算明细|hidden"
                      />
                </el-form-item>
                <el-form-item label="明细编号" prop="mxbh">
                      <el-input
                          v-model="formData.mxbh"
                          placeholder="明细编号"
                      />
                </el-form-item>
                <el-form-item label="物品名称" prop="wpmc">
                      <el-input
                          v-model="formData.wpmc"
                          placeholder="物品名称"
                      />
                </el-form-item>
                <el-form-item label="采购目录" prop="cgml">
                      <el-input
                          v-model="formData.cgml"
                          placeholder="采购目录"
                      />
                </el-form-item>
                <el-form-item label="品牌厂商" prop="ppcs">
                      <el-input
                          v-model="formData.ppcs"
                          placeholder="品牌厂商"
                      />
                </el-form-item>
                <el-form-item label="规格" prop="gg">
                      <el-input
                          v-model="formData.gg"
                          placeholder="规格"
                      />
                </el-form-item>
                <el-form-item label="详细参数" prop="xxcs">
                      <el-input
                          v-model="formData.xxcs"
                          placeholder="详细参数"
                      />
                </el-form-item>
                <el-form-item label="是否特殊 进口、辐射、其它|hidden" prop="ists">
                      <el-input
                          v-model="formData.ists"
                          placeholder="是否特殊 进口、辐射、其它|hidden"
                      />
                </el-form-item>
                <el-form-item label="单位" prop="dw">
                      <el-input
                          v-model="formData.dw"
                          placeholder="单位"
                      />
                </el-form-item>
                <el-form-item label="数量" prop="sl">
                      <el-input
                          v-model="formData.sl"
                          placeholder="数量"
                      />
                </el-form-item>
                <el-form-item label="单价（元）" prop="price">
                      <el-input
                          v-model="formData.price"
                          placeholder="单价（元）"
                      />
                </el-form-item>
                <el-form-item label="金额（元）" prop="ysje">
                      <el-input
                          v-model="formData.ysje"
                          placeholder="金额（元）"
                      />
                </el-form-item>
                <el-form-item label="开始时间|hidden" prop="time1">
                      <el-input
                          v-model="formData.time1"
                          placeholder="开始时间|hidden"
                      />
                </el-form-item>
                <el-form-item label="结束时间|hidden" prop="time2">
                      <el-input
                          v-model="formData.time2"
                          placeholder="结束时间|hidden"
                      />
                </el-form-item>
                <el-form-item label="其他说明|hidden" prop="notes">
                      <el-input
                          v-model="formData.notes"
                          placeholder="其他说明|hidden"
                      />
                </el-form-item>
                <el-form-item label="供应商|hidden" prop="gys">
                      <el-input
                          v-model="formData.gys"
                          placeholder="供应商|hidden"
                      />
                </el-form-item>
                <el-form-item label="组织机构代码|hidden" prop="jgdm">
                      <el-input
                          v-model="formData.jgdm"
                          placeholder="组织机构代码|hidden"
                      />
                </el-form-item>
                <el-form-item label="法人|hidden" prop="gysfr">
                      <el-input
                          v-model="formData.gysfr"
                          placeholder="法人|hidden"
                      />
                </el-form-item>
                <el-form-item label="法人身份证|hidden" prop="frsfz">
                      <el-input
                          v-model="formData.frsfz"
                          placeholder="法人身份证|hidden"
                      />
                </el-form-item>
                <el-form-item label="供应商联系人|hidden" prop="gyslxr">
                      <el-input
                          v-model="formData.gyslxr"
                          placeholder="供应商联系人|hidden"
                      />
                </el-form-item>
                <el-form-item label="联系电话|hidden" prop="gystel">
                      <el-input
                          v-model="formData.gystel"
                          placeholder="联系电话|hidden"
                      />
                </el-form-item>
                <el-form-item label="资产类型（0资产，1非资产）|hidden" prop="khyh">
                      <el-input
                          v-model="formData.khyh"
                          placeholder="资产类型（0资产，1非资产）|hidden"
                      />
                </el-form-item>
                <el-form-item label="开户帐号|hidden" prop="khzh">
                      <el-input
                          v-model="formData.khzh"
                          placeholder="开户帐号|hidden"
                      />
                </el-form-item>
                <el-form-item label="采购数量|hidden" prop="xsl">
                      <el-input
                          v-model="formData.xsl"
                          placeholder="采购数量|hidden"
                      />
                </el-form-item>
                <el-form-item label="成交价格(元)|hidden" prop="cjjg">
                      <el-input
                          v-model="formData.cjjg"
                          placeholder="成交价格(元)|hidden"
                      />
                </el-form-item>
                <el-form-item label="是否备案（0 否，1是）|hidden" prop="isba">
                      <el-input
                          v-model="formData.isba"
                          placeholder="是否备案（0 否，1是）|hidden"
                      />
                </el-form-item>
                <el-form-item label="rwid|hidden" prop="rwid">
                      <el-input
                          v-model="formData.rwid"
                          placeholder="rwid|hidden"
                      />
                </el-form-item>
                <el-form-item label="wtid|hidden" prop="wtid">
                      <el-input
                          v-model="formData.wtid"
                          placeholder="wtid|hidden"
                      />
                </el-form-item>
                <el-form-item label="wmwtid|hidden" prop="wmwtid">
                      <el-input
                          v-model="formData.wmwtid"
                          placeholder="wmwtid|hidden"
                      />
                </el-form-item>
                <el-form-item label="wjid|hidden" prop="wjid">
                      <el-input
                          v-model="formData.wjid"
                          placeholder="wjid|hidden"
                      />
                </el-form-item>
                <el-form-item label="baid|hidden" prop="baid">
                      <el-input
                          v-model="formData.baid"
                          placeholder="baid|hidden"
                      />
                </el-form-item>
                <el-form-item label="bdid|hidden" prop="bdid">
                      <el-input
                          v-model="formData.bdid"
                          placeholder="bdid|hidden"
                      />
                </el-form-item>
                <el-form-item label="明细状态 0执行，1完成|hidden" prop="mxzt">
                      <el-input
                          v-model="formData.mxzt"
                          placeholder="明细状态 0执行，1完成|hidden"
                      />
                </el-form-item>
                <el-form-item label="用途？|hidden" prop="yt">
                      <el-input
                          v-model="formData.yt"
                          placeholder="用途？|hidden"
                      />
                </el-form-item>
                <el-form-item label="使用人|hidden" prop="syr">
                      <el-input
                          v-model="formData.syr"
                          placeholder="使用人|hidden"
                      />
                </el-form-item>
                <el-form-item label="存放地点|hidden" prop="cfdd">
                      <el-input
                          v-model="formData.cfdd"
                          placeholder="存放地点|hidden"
                      />
                </el-form-item>
                <el-form-item label="协议数量？|hidden" prop="xysl">
                      <el-input
                          v-model="formData.xysl"
                          placeholder="协议数量？|hidden"
                      />
                </el-form-item>
                <el-form-item label="申购理由" prop="sgly">
                      <el-input
                          v-model="formData.sgly"
                          placeholder="申购理由"
                      />
                </el-form-item>
                <el-form-item label="成交价？|hidden" prop="cjprice">
                      <el-input
                          v-model="formData.cjprice"
                          placeholder="成交价？|hidden"
                      />
                </el-form-item>
                <el-form-item label="是否进口" prop="sfjk">
                      <el-input
                          v-model="formData.sfjk"
                          placeholder="是否进口"
                      />
                </el-form-item>
                <el-form-item label="预算编码" prop="ysbm">
                      <el-input
                          v-model="formData.ysbm"
                          placeholder="预算编码"
                      />
                </el-form-item>
                <el-form-item label="是否超标 0否 1是 （可以超出采购目录限额的金额）" prop="iscb">
                      <el-input
                          v-model="formData.iscb"
                          placeholder="是否超标 0否 1是 （可以超出采购目录限额的金额）"
                      />
                </el-form-item>
                <el-form-item label="审定金额|hidden" prop="sdje">
                      <el-input
                          v-model="formData.sdje"
                          placeholder="审定金额|hidden"
                      />
                </el-form-item>
                <el-form-item label="核减理由|hidden" prop="hjly">
                      <el-input
                          v-model="formData.hjly"
                          placeholder="核减理由|hidden"
                      />
                </el-form-item>
                <el-form-item label="费用项" prop="bCode">
                      <el-input
                          v-model="formData.bCode"
                          placeholder="费用项"
                      />
                </el-form-item>
                <el-form-item label="预算项" prop="buCode">
                      <el-input
                          v-model="formData.buCode"
                          placeholder="预算项"
                      />
                </el-form-item>
                <el-form-item label="材料类型管理|hidden" prop="by1">
                      <el-input
                          v-model="formData.by1"
                          placeholder="材料类型管理|hidden"
                      />
                </el-form-item>
                <el-form-item label="材料类型管理|hidden" prop="xh">
                      <el-input
                          v-model="formData.xh"
                          placeholder="材料类型管理|hidden"
                      />
                </el-form-item>
                <el-form-item label="政采云链接地址" prop="zcydz">
                      <el-input
                          v-model="formData.zcydz"
                          placeholder="政采云链接地址"
                      />
                </el-form-item>
                <el-form-item label="用款指标ID（BudgetId）" prop="by2">
                      <el-input
                          v-model="formData.by2"
                          placeholder="用款指标ID（BudgetId）"
                      />
                </el-form-item>
                <el-form-item label="用款明细指标ID（DetailId）" prop="by3">
                      <el-input
                          v-model="formData.by3"
                          placeholder="用款明细指标ID（DetailId）"
                      />
                </el-form-item>
                <el-form-item label="采购方式适用的法律条款代码" prop="fltk">
                      <el-input
                          v-model="formData.fltk"
                          placeholder="采购方式适用的法律条款代码"
                      />
                </el-form-item>
                <el-form-item label="确认书文号" prop="qrswh">
                      <el-input
                          v-model="formData.qrswh"
                          placeholder="确认书文号"
                      />
                </el-form-item>
                <el-form-item label="项目优先级程度0普通  1 紧急" prop="yxj">
                      <el-input
                          v-model="formData.yxj"
                          placeholder="项目优先级程度0普通  1 紧急"
                      />
                </el-form-item>
                <el-form-item label="备案标志0 不备案 1 外网备案 2 内网备案" prop="babz">
                      <el-input
                          v-model="formData.babz"
                          placeholder="备案标志0 不备案 1 外网备案 2 内网备案"
                      />
                </el-form-item>
                <el-form-item label="是否保密0 保密  1 普通" prop="sfbm">
                      <el-input
                          v-model="formData.sfbm"
                          placeholder="是否保密0 保密  1 普通"
                      />
                </el-form-item>
                <el-form-item label="付款方式" prop="by5">
                      <el-input
                          v-model="formData.by5"
                          placeholder="付款方式"
                      />
                </el-form-item>
                <el-form-item label="资金类型" prop="by4">
                      <el-input
                          v-model="formData.by4"
                          placeholder="资金类型"
                      />
                </el-form-item>
                <el-form-item label="政采云购物车过来的数据" prop="by9">
                      <el-input
                          v-model="formData.by9"
                          placeholder="政采云购物车过来的数据"
                      />
                </el-form-item>
                <el-form-item label="进口理由" prop="jkly">
                      <el-input
                          v-model="formData.jkly"
                          placeholder="进口理由"
                      />
                </el-form-item>
                <el-form-item label="浙江省财建议书上报结果 0失败 1成功" prop="sbjg">
                      <el-input
                          v-model="formData.sbjg"
                          placeholder="浙江省财建议书上报结果 0失败 1成功"
                      />
                </el-form-item>
                <el-form-item label="上报结果说明" prop="sbjgsm">
                      <el-input
                          v-model="formData.sbjgsm"
                          placeholder="上报结果说明"
                      />
                </el-form-item>
                <el-form-item label="询价，竞价表id，合并明细使用" prop="zxid">
                      <el-input
                          v-model="formData.zxid"
                          placeholder="询价，竞价表id，合并明细使用"
                      />
                </el-form-item>
                <el-form-item label="意向编号" prop="yxbh">
                      <el-input
                          v-model="formData.yxbh"
                          placeholder="意向编号"
                      />
                </el-form-item>
                <el-form-item label="确认书编号" prop="qrsbh">
                      <el-input
                          v-model="formData.qrsbh"
                          placeholder="确认书编号"
                      />
                </el-form-item>
                <el-form-item label="被拆分的明细的guid,如果不为空，表示已经被拆分过了" prop="cfguid">
                      <el-input
                          v-model="formData.cfguid"
                          placeholder="被拆分的明细的guid,如果不为空，表示已经被拆分过了"
                      />
                </el-form-item>
                <el-form-item label="是否变更" prop="isbg">
                      <el-input
                          v-model="formData.isbg"
                          placeholder="是否变更"
                      />
                </el-form-item>
                <el-form-item label="明细订单编号（冻结解冻）" prop="mxordbh">
                      <el-input
                          v-model="formData.mxordbh"
                          placeholder="明细订单编号（冻结解冻）"
                      />
                </el-form-item>
                <el-form-item label="网超推荐id" prop="wcid">
                      <el-input
                          v-model="formData.wcid"
                          placeholder="网超推荐id"
                      />
                </el-form-item>
                <el-form-item label="经贸用的，作废" prop="cfmx">
                      <el-input
                          v-model="formData.cfmx"
                          placeholder="经贸用的，作废"
                      />
                </el-form-item>
                <el-form-item label="经贸用的，作废" prop="ysjf">
                      <el-input
                          v-model="formData.ysjf"
                          placeholder="经贸用的，作废"
                      />
                </el-form-item>
                <el-form-item label="大确认书编号" prop="dqrsbh">
                      <el-input
                          v-model="formData.dqrsbh"
                          placeholder="大确认书编号"
                      />
                </el-form-item>
                <el-form-item label="base_product.guid" prop="cpguid">
                      <el-input
                          v-model="formData.cpguid"
                          placeholder="base_product.guid"
                      />
                </el-form-item>
      </el-form>
      <template #footer></template>
     </el-card>
    </ el-drawer>
  </div>
</template>

<script setup lang="ts">
  defineOptions({
    name: "ZxcgMx",
    inheritAttrs: false,
  });
  import { useUserStore } from "@/store";
  import ZxcgMxAPI, { ZxcgMxPageVO, ZxcgMxForm, ZxcgMxPageQuery } from "@/api/cg/zxcg-mx";

  // 获取路由参数,view/add/check
  const route = useRoute();
  const type = ref(route.query.type?.toString() || "view");
  //显示隐藏权限,用于v-if
  const add = type.value == "add";
  const view = type.value == "view";
  const check = type.value == "check";
  //页面参数
  const queryFormRef = ref(ElForm);
  const loading = ref(false);
  const removeIds = ref<number[]>([]);
  const total = ref(0);

  const queryParams = reactive<ZxcgMxPageQuery>({
    pageNum: 1,
    pageSize: 10,
  });

  // 采购申请明细表格数据
  const pageData = ref<ZxcgMxPageVO[]>([]);

  // 弹窗
  const dialog = reactive({
    title: "",
    visible: false,
  });



  /** 查询采购申请明细 */
  function handleQuery() {
    loading.value = true;
          ZxcgMxAPI.getPage(queryParams)
        .then((data) => {
          pageData.value = data.list;
          total.value = data.total;
        })
        .finally(() => {
          loading.value = false;
        });
  }

  /** 重置采购申请明细查询 */
  function handleResetQuery() {
    queryFormRef.value!.resetFields();
    queryParams.pageNum = 1;
    handleQuery();
  }

  /** 行复选框选中记录选中ID集合 */
  function handleSelectionChange(selection: any) {
    removeIds.value = selection.map((item: any) => item.id);
  }

  /** 打开采购申请明细弹窗 */
  function handleEdit(id?: number) {
    dialog.visible = true;
    if (id) {
      dialog.title = "修改采购申请明细";
            ZxcgMxAPI.getFormData(id).then((data) => {
        Object.assign(formData, data);
      });
    } else {
      dialog.title = "新增采购申请明细";
    }
  }

 

  /** 关闭采购申请明细弹窗 */
  function handleCloseDialog() {
    dialog.visible = false;
    dataFormRef.value.resetFields();
    dataFormRef.value.clearValidate();
    Object.assign(formData, {});

  }

  /** 删除采购申请明细 */
  function handleDelete(id?: number) {
    const ids = [id || removeIds.value].join(",");
    if (!ids) {
      ElMessage.warning("请勾选删除项");
      return;
    }

    ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(
        () => {
          loading.value = true;
                ZxcgMxAPI.deleteByIds(ids)
              .then(() => {
                ElMessage.success("删除成功");
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        },
        () => {
          ElMessage.info("已取消删除");
        }
    );
  }

  onMounted(() => {
    handleQuery();
  });

  //--------------以下是form表单相关
const dataFormRef = ref(ElForm);

  // 采购申请明细表单数据
  const formData = reactive<ZxcgMxForm>({});

const formType = reactive({
  add: false,
  edit: false,
  view: false,
});

  // 采购申请明细表单校验规则
  const rules = reactive({
                      guid: [{ required: true, message: "请输入guid|hidden", trigger: "blur" }],
                      parentguid: [{ required: true, message: "请输入母表GUID|hidden", trigger: "blur" }],
                      sqid: [{ required: true, message: "请输入外键，自增|hidden", trigger: "blur" }],
                      mxbh: [{ required: true, message: "请输入明细编号", trigger: "blur" }],
  });

   /** 提交采购申请明细表单 */
  function handleSubmit() {
    dataFormRef.value.validate((valid: any) => {
      if (valid) {
        loading.value = true;
        const id = formData.id;
        if (id) {
                ZxcgMxAPI.update(id, formData)
              .then(() => {
                ElMessage.success("修改成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        } else {
                ZxcgMxAPI.add(formData)
              .then(() => {
                ElMessage.success("新增成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        }
      }
    });
  }
</script>
<style lang="scss" scoped>

</style>
