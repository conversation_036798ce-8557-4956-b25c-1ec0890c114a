<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="财政分类名称" prop="ycodename">
          <el-input
            v-model="queryParams.ycodename"
            placeholder="财政分类名称"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="教育分类名称" prop="zcodename">
          <el-input
            v-model="queryParams.zcodename"
            placeholder="教育分类名称"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item>
          <el-button class="filter-item" type="primary" icon="search" @click="handleQuery">
            搜索
          </el-button>
          <el-button icon="refresh" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <el-table
        v-loading="loading"
        :data="CzList"
        row-key="xh"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="ycodename" label="财政分类名称" min-width="200" />
        <el-table-column prop="ycode" label="财政分类代码" min-width="200" />
        <el-table-column prop="zcodename" label="教育分类名称" min-width="200" />
        <el-table-column prop="zcode" label="教育分类代码" min-width="200" />

        <el-table-column label="操作" fixed="right" align="left" width="200">
          <template #default="scope">
            <el-button type="danger" link size="small" @click.stop="handleOpenDialog(scope.row.xh)">
              编辑教育分类
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      width="600px"
      @closed="handleCloseDialog"
    >
      <el-form ref="CzCodeFormRef" :model="formData" label-width="150px">
        <el-form-item label="财政分类名称" prop="ycodename">
          <span>{{ formData.ycodename }}</span>
        </el-form-item>
        <el-form-item label="教育分类名称" prop="zcodename">
          <!-- 弹出选择 -->
          <div class="flex">
            <el-input v-model="formData.zcodename" :disabled="true" placeholder="请选择教育分类" />
            <el-button type="primary" @click="visibleEdu = true">选择</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
          <el-button @click="handleCloseDialog">取 消</el-button>
        </div>
      </template>
      <!-- 教育弹窗 -->
      <el-dialog v-model="visibleEdu" title="教育分类选择框" width="60%">
        <TreeCzJyCode type="JY" @node-click="handleJycodeNodeClick" />
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Dept",
  inheritAttrs: false,
});

import storeAPI, { CzCodeForm, CzCodeVO, CzQuery } from "@/api/properties/store";
import { jycodes } from "@/assets/JYCode";

const queryFormRef = ref(ElForm);
const queryParams = reactive<CzQuery>({});
const loading = ref(false);
const CzList = ref<CzCodeVO[]>([]);
const dialog = reactive({
  title: "",
  visible: false,
});
const formData = reactive<CzCodeForm>({});
const visibleEdu = ref(false);

const handleJycodeNodeClick = (treeNodeValue: string) => {
  visibleEdu.value = false;
  formData.zcode = treeNodeValue;
};

//监听formData.czcode的值，并从codes中找到对应的name
watch(
  () => formData.zcode,
  (newVal) => {
    if (newVal) {
      const codeItem = findInTree(jycodes.value, newVal);
      console.log("codeItem--jycodeName", codeItem);
      if (codeItem) {
        formData.zcodename = codeItem.label;
      }
    } else {
      formData.zcodename = "";
    }
  }
);

// 递归查找树形结构中的节点
function findInTree(tree: any[], value: string): any {
  for (const node of tree) {
    if (node.value === value) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findInTree(node.children, value);
      if (found) {
        return found;
      }
    }
  }
  return null;
}

// 重置查询
function handleResetQuery() {
  queryFormRef.value.resetFields();
  handleQuery();
}
async function handleOpenDialog(CzCodeId: number) {
  console.log(CzCodeId);
  dialog.visible = true;
  dialog.title = "编辑财政分类映射";
  storeAPI.getCzCodeForm(CzCodeId).then((data) => {
    Object.assign(formData, data);
    dialog.title = formData.ycodename + "配置管理员";
    console.log(formData);
  });
}
// 关闭弹窗
function handleCloseDialog() {
  dialog.visible = false;
  //handleQuery();
}

function handleSubmit() {
  console.log("提交");
  console.log(formData);
  loading.value = true;
  const CzCodeId = formData.xh;
  if (CzCodeId) {
    storeAPI
      .updateCzcode(CzCodeId, formData)
      .then(() => {
        ElMessage.success("修改成功");
        handleCloseDialog();
        handleQuery();
      })
      .finally(() => (loading.value = false));
  }
}

// 查询部门
function handleQuery() {
  loading.value = true;
  storeAPI.getCzCodeList(queryParams).then((data) => {
    CzList.value = data;
    loading.value = false;
  });
}

onMounted(() => {
  handleQuery();
});
</script>
