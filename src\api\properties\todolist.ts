import request from "@/utils/request";
/** 流程日志组件模块API */
const WORKFLOW_BASE_URL = "/api/v1/workflow/todolist/";

const toDoListAPI = {
  //结束待办
  delToDoById(params: { id: string }) {
    return request({
      url: `${WORKFLOW_BASE_URL}` + "end",
      method: "get",
      params: params,
    });
  },
  //个人待办分页列表
  getToDoList(params: { pageNum: number; pageSize: number; sxmc: string; ucode: string }) {
    return request({
      url: `${WORKFLOW_BASE_URL}` + "page",
      method: "get",
      params: params,
    });
  },
  //待办分页列表
  getToDoListAll(params: { pageNum: number; pageSize: number; sxmc: string; ucode: string }) {
    return request({
      url: `${WORKFLOW_BASE_URL}` + "all/page",
      method: "get",
      params: params,
    });
  },
  //个人分类待办
  getGroupToDoList() {
    return request({
      url: `${WORKFLOW_BASE_URL}` + "group",
      method: "get",
    });
  },
};

export default toDoListAPI;

/** dzb分页查询参数 */
export interface ToDoMenu {
  path?: string;
  sxmc?: string;
  cmun?: number;
  sxbh?: string;
  menuId?: number;
}
