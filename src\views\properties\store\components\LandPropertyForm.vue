<template>
  <!-- 土地信息 -->
  <el-form :model="formData" label-width="120px" ref="formRef" :inline="true" :rules="rules"
    :disabled="!props.editable">
    <el-form-item label="土地总面积(㎡)" prop="tdzmj">
      <el-input v-model="formData.tdzmj" placeholder="请输入土地总面积" type="number" />
    </el-form-item>

    <el-form-item label="出借面积(㎡)">
      <el-input v-model="formData.cjmj" placeholder="请输入出借面积" type="number" />
    </el-form-item>

    <el-form-item label="建筑面积(㎡)">
      <el-input v-model="formData.jzmj" placeholder="请输入建筑面积" type="number" />
    </el-form-item>

    <el-form-item label="土地证载明面积(㎡)">
      <el-input v-model="formData.tdzzmmj" placeholder="请输入土地证载明面积" type="number" />
    </el-form-item>

    <el-form-item label="产权形式" required prop="cqxs">
      <DDLXcode v-model="formData.cqxs" xcode="020206" clearable />
    </el-form-item>

    <el-form-item label="有产权情况" v-if="formData.cqxs === '02020601'">
      <DDLXcode v-model="formData.ycqqk" xcode="02020601" clearable />
    </el-form-item>

    <el-form-item label="无产权情况" v-if="formData.cqxs === '02020603'">
      <DDLXcode v-model="formData.wcqqk" xcode="02020603" clearable />
    </el-form-item>

    <el-form-item label="权属性质" required prop="qsxz">
      <DDLXcode v-model="formData.qsxz" xcode="020207" clearable />
    </el-form-item>

    <el-form-item label="权属证明">
      <el-input v-model="formData.qszm" placeholder="请输入权属证明" />
    </el-form-item>

    <el-form-item label="权属证号">
      <el-input v-model="formData.qszh" placeholder="请输入权属证号" />
    </el-form-item>

    <el-form-item label="产权单位">
      <el-input v-model="formData.cqdw" placeholder="请输入产权单位" />
    </el-form-item>

    <el-form-item label="发证时间">
      <el-date-picker v-model="formData.fzsj" type="date" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择发证时间" />
    </el-form-item>

    <el-form-item label="土地等级">
      <el-input v-model="formData.tddj" placeholder="请输入土地等级" />
    </el-form-item>

    <el-form-item label="土地使用权人">
      <el-input v-model="formData.syqr" placeholder="请输入土地使用权人" />
    </el-form-item>

    <el-form-item label="土地图号">
      <el-input v-model="formData.tdth" placeholder="请输入土地图号" />
    </el-form-item>

    <el-form-item label="土地证号">
      <el-input v-model="formData.tdzh" placeholder="请输入土地证号" />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { defineProps, reactive, onMounted } from "vue";
import storeAPI, { ZcinfoDifferentForm } from "@/api/properties/store";
import { ElLoading } from "element-plus";

const formRef = ref(ElForm);
// 组件属性定义
const props = defineProps({
  guid: {
    type: String
  },
  zclxbh: {
    type: String
  },
  editable: {
    type: Boolean,
    required: true
  }
})
// 表单数据
const formData = reactive<ZcinfoDifferentForm>({
  zclxbh: props.zclxbh || '',
})

// 表单验证规则
const rules = reactive({
  jzjg: [
    { required: true, message: '请选择建筑结构', trigger: 'change' }
  ],
  cqxs: [
    { required: true, message: '请选择产权形式', trigger: 'change' }
  ],
  qsxz: [
    { required: true, message: '请选择权属性质', trigger: 'change' }
  ]
})

// 产权形式选项
const cqxsOptions = [
  { value: '02020601', label: '有产权' },
  { value: '02020603', label: '无产权' },
  { value: '02020605', label: '其他' }
];

// 有产权情况选项
const ycqkOptions = [
  { value: '0202060101', label: '自有' },
  { value: '0202060103', label: '租赁' },
  { value: '0202060105', label: '划拨' },
  { value: '0202060107', label: '其他' }
];

// 无产权情况选项
const wcqkOptions = [
  { value: '0202060301', label: '借用' },
  { value: '0202060303', label: '代管' },
  { value: '0202060305', label: '其他' }
];

// 权属性质选项
const qsxzOptions = [
  { value: '02020701', label: '国有' },
  { value: '02020703', label: '集体' },
  { value: '02020705', label: '私有' },
  { value: '02020707', label: '其他' }
];

// 产权形式变更处理
const handleCqxsChange = (value: string) => {
  // 当产权形式变更时，清空相关联的字段
  if (value === '02020601') {
    formData.wcqqk = '';
  } else if (value === '02020603') {
    formData.ycqqk = '';
  } else {
    formData.ycqqk = '';
    formData.wcqqk = '';
  }
};

// 监听值变化
watch(
  () => [props.guid, props.zclxbh],
  (val) => {
    console.log('props:', val)
    if (val && val[0] != '' && val[1] != undefined) {
      console.log('props:', val, val[0], val[1])
      //获取表单数据
      storeAPI.getFormDataDiff({ guid: val[0], zclxbh: val[1] }).then((res) => {
        Object.assign(formData, res)
      })

      formData.zclxbh = val[1]
      formData.guid = val[0]
    }
  },
  { deep: true, immediate: true }
)


// 提交表单
const submitForm = () => {
  return new Promise((resolve, reject) => {
    formRef.value?.validate((valid) => {
      if (valid) {
        // 显示加载状态
        const loading = ElLoading.service({
          lock: true,
          text: '正在保存...',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        storeAPI.updateDiff(formData).then((res) => {
          // 关闭加载状态
          loading.close()
          // 显示成功消息
          ElMessage({
            type: 'success',
            message: '保存成功'
          })
          console.log('表单提交成功:', res)
          resolve(res)
        }).catch(error => {
          // 关闭加载状态
          loading.close()
          // 显示错误消息
          ElMessage({
            type: 'error',
            message: '保存失败: ' + (error.message || '未知错误')
          })
          console.error('表单提交失败:', error)
          reject(error)
        })
      } else {
        ElMessage({
          type: 'warning',
          message: '请填写必填项'
        })
        reject(new Error('表单验证失败'))
        return false
      }
    })
  })
}


// 表单验证方法
const validateForm = () => {
  return new Promise((resolve, reject) => {
    formRef.value?.validate((valid) => {
      if (valid) {
        resolve(true)
      } else {
        reject(new Error('表单验证失败'))
      }
    })
  })
}


// 导出方法供父组件使用
defineExpose({
  submitForm,
  validate: validateForm
})

</script>

<style scoped>
.el-form-item {
  width: 330px;
}
</style>