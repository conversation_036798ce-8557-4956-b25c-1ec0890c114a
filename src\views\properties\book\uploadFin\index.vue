<!-- cursor测试:1 -->
<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item v-if="view" label="资产编号" prop="zcbh">
            <el-input
              v-model="queryParams.zcbh"
              placeholder="资产编号"
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item v-if="view" label="资产名称" prop="zcmc">
            <el-input
              v-model="queryParams.zcmc"
              placeholder="资产名称"
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item style="width: 600px" label="财政分类" prop="czcode">
              <CascaderCzJyCode
                v-model="queryParams.czcode"
                type="CZ"
                :multiple="true"
                clearable
              />
            </el-form-item>
            <el-form-item label="入账月份" prop="djsj">
              <el-date-picker
                v-model="queryParams.djsj"
                :editable="false"
                class="filter-item"
                type="daterange"
                range-separator="~"
                start-placeholder="开始时间"
                end-placeholder="截止时间"
                value-format="YYYY-MM"
                clearable
              />
            </el-form-item>
          <el-form-item v-if="view" label="状态" prop="rzzt">
            <el-select v-model="queryParams.rzzt" clearable :loading="loading" placeholder="请选择">
              <el-option label="--全部--" value="" />
              <el-option label="未上传" value="02023602" />
              <el-option label="已上传" value="02023603" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon>
                <Search />
              </template>
              搜索
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon>
                <Refresh />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="上传财政列表">
        <div>
          <el-button
            plain
            type="danger"
            icon="Top"
            :disabled="removeIds.length === 0"
            @click="handleReport()"
          >
            上传财政
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        stripe
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" align="center" fixed />
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column key="zcbh" label="资产编号" prop="zcbh" min-width="100" align="center" />
        <el-table-column
          key="zcmc"
          label="资产名称"
          prop="zcmc"
          min-width="100"
          align="center"
        />
        <el-table-column
          key="czcodename"
          label="资产分类"
          prop="czcodename"
          min-width="100"
          align="center"
        />
        <el-table-column
          key="pp"
          label="品牌"
          prop="pp"
          min-width="100"
          align="center"
        />
        <el-table-column
          key="ggxh"
          label="规格型号"
          prop="ggxh"
          min-width="100"
          align="center"
        />
        <el-table-column key="sl" label="数量" prop="sl" min-width="80" align="center" />
        <el-table-column key="dj" label="单价(元)" prop="dj" min-width="100" align="center" />
        <el-table-column key="je" label="原值(元)" prop="je" min-width="100" align="center" />
        <el-table-column key="djbmname" label="登记部门" prop="djbmname" min-width="100" align="center" />
        <el-table-column key="zcgjztname" label="状态" prop="zcgjztname" min-width="100" align="center" />
        <el-table-column fixed="right" label="操作" width="120">
          <template #default="scope">
            <el-button
              type="success"
              icon="Document"
              size="small"
              @click="handleRowView(scope.row.guid, scope.row.rkguid)">
              查看
            </el-button>   
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 10"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 查看资产弹窗 -->
    <el-drawer v-model="itemVisible" append-to-body size="70%">
      <CardPanel :key="itemGuid" :guid="itemGuid" :rkguid="itemRkGuid" />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import uploadfinAPI, { uploadfinPageVO, uploadfinPageQuery } from "@/api/properties/uploadFin";

//————————————————————————————————————————————暴露的方法,和请求参数
function RefreshFatherDrawer(close?: boolean) {
  //是否关闭弹窗由参数决定，默认不关闭
  if (close) {
    dialog.storeVisible = false;
  }
  handleQuery();
}
// 获取路由参数,view/add/check
const route = useRoute();
const type = ref(route.query.type?.toString() || "view");
//显示隐藏权限,用于v-if
const add = type.value == "add";
const view = type.value == "view";
const check = type.value == "check";

const removeIds = ref<number[]>([]);

//————————————————————————————————————————————查询相关
const queryFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);
const queryParams = reactive<uploadfinPageQuery>({
  pageNum: 1,
  pageSize: 10,
  rzzt: "02023602"
});
// 财政上传表格数据
const pageData = ref<uploadfinPageVO[]>([]);

// 选中项发生变化
function handleSelectionChange(selection: any[]) {
  removeIds.value = selection.map((item: any) => item.id);
}

/** 查询财政上传 */
function handleQuery() {
  loading.value = true;
  console.log("刷新列表数据");
  uploadfinAPI
    .getPage(queryParams)
    .then((data) => {

      pageData.value = data.list;
      total.value = data.total;
      console.log("data");
      console.log(pageData.value);
    })
    .catch((error) => {
      console.error("获取数据失败:", error);
      ElMessage.error("获取数据失败");
      pageData.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置变动查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}
const handleChangeRadio = () => {
  handleResetQuery();
};


//————————————————————————————————————————————弹窗相关
// 弹窗
const dialog = reactive({
  title: "",
  storeVisible: false,
  accountVisible: false,
});

/** 打开入库弹窗 */
const itemVisible = ref(false);
const itemGuid = ref("");
const itemRkGuid = ref("");

const handleRowView = (guid: string, rkguid: string) => {
  if (!guid) {
    ElMessage.warning("资产编号不能为空");
    return;
  }
  itemGuid.value = guid;
  itemVisible.value = true;
  itemRkGuid.value = rkguid;
};

/** 关闭弹窗 */
function handleCloseDialog() {
  dialog.storeVisible = false;
  handleQuery();
}

/** 财政上报 */
function handleReport(id?: number) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选相应选项");
    return;
  }

  ElMessageBox.confirm("确认上传已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}

.flex {
  display: flex;
  flex-direction: row;
}
</style>