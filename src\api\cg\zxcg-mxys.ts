import request from "@/utils/request";

const ZXCGMXYS_BASE_URL = "/api/v1/zxcgMxyss";

const ZxcgMxysAPI = {
    /** 获取采购明细预算分页数据 */
    getPage(queryParams?: ZxcgMxysPageQuery) {
        return request<any, PageResult<ZxcgMxysPageVO[]>>({
            url: `${ZXCGMXYS_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    },
    /**
     * 获取采购明细预算表单数据
     *
     * @param id ZxcgMxysID
     * @returns ZxcgMxys表单数据
     */
    getFormData(id: number) {
        return request<any, ZxcgMxysForm>({
            url: `${ZXCGMXYS_BASE_URL}/${id}/form`,
            method: "get",
        });
    },

    /** 添加采购明细预算*/
    add(data: ZxcgMxysForm) {
        return request({
            url: `${ZXCGMXYS_BASE_URL}`,
            method: "post",
            data: data,
        });
    },

    /**
     * 更新采购明细预算
     *
     * @param id ZxcgMxysID
     * @param data ZxcgMxys表单数据
     */
     update(id: number, data: ZxcgMxysForm) {
        return request({
            url: `${ZXCGMXYS_BASE_URL}/${id}`,
            method: "put",
            data: data,
        });
    },

    /**
     * 批量删除采购明细预算，多个以英文逗号(,)分割
     *
     * @param ids 采购明细预算ID字符串，多个以英文逗号(,)分割
     */
     deleteByIds(ids: string) {
        return request({
            url: `${ZXCGMXYS_BASE_URL}/${ids}`,
            method: "delete",
        });
    }
}

export default ZxcgMxysAPI;

/** 采购明细预算分页查询参数 */
export interface ZxcgMxysPageQuery extends PageQuery {
    /** 主表的表名 zxcg_mx|zxcg_mxzx|zxcg_mxzc */
    zbTable?: string;
    /** 主表的guid */
    zbGuid?: string;
}

/** 采购明细预算表单对象 */
export interface ZxcgMxysForm {
    id?:  number;
    guid?:  string;
    /** 预算金额 */
    ysje?:  number;
    /** 预算编码 */
    ysbm?:  string;
    /** 费用项 */
    bCode?:  string;
    /** 预算项 */
    buCode?:  string;
    /** 主表的表名 zxcg_mx|zxcg_mxzx|zxcg_mxzc */
    zbTable?:  string;
    /** 主表的guid */
    zbGuid?:  string;
    /** 已支付金额 */
    zfje?:  number;
}

/** 采购明细预算分页对象 */
export interface ZxcgMxysPageVO {
    id?: number;
    guid?: string;
    /** 预算金额 */
    ysje?: number;
    /** 预算编码 */
    ysbm?: string;
    /** 费用项 */
    bCode?: string;
    /** 预算项 */
    buCode?: string;
    /** 主表的表名 zxcg_mx|zxcg_mxzx|zxcg_mxzc */
    zbTable?: string;
    /** 主表的guid */
    zbGuid?: string;
    /** 已支付金额 */
    zfje?: number;
}
