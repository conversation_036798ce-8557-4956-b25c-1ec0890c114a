<!-- 采购意向列表 -->
<template>
  <el-card>
    <!-- ********************** 用插槽显示筛选框 ********************** -->
    <template #header>
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
        <el-form-item label="计划单号" prop="cgyxdh">
          <el-input v-model="queryParams.cgyxdh" placeholder="请输计划单号" />
        </el-form-item>
        <el-form-item label="计划名称" prop="ysxmmc">
          <el-input v-model="queryParams.ysxmmc" placeholder="请输入预算项目名称/计划名称" />
        </el-form-item>
        <el-form-item label="经费负责人" prop="xmfzrname">
          <el-input v-model="queryParams.xmfzrname" placeholder="请输入经费负责人姓名" />
        </el-form-item>
        <el-form-item label="所属部门" prop="djbm">
          <DDLDeptList v-model="queryParams.djbm" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon><Search /></template>
            搜索
          </el-button>
        </el-form-item>
      </el-form>
    </template>

    <Title name="意向清单"></Title>
    <!-- ********************** 列表内容 ********************** -->
    <el-table
      v-loading="loading"
      stripe
      :data="pageData"
      highlight-current-row
      :border="true"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="序号" type="index" width="55" align="center" fixed>
        <template #default="scope">
          <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目编号" prop="ysxmbh" width="120" />
      <el-table-column label="项目名称" prop="ysxmmc" width="200" />
      <el-table-column label="计划名称" prop="cgxmmc" width="200" />
      <el-table-column label="计划金额(元)" prop="ysje" min-width="90" />
      <el-table-column label="登记人" prop="djrname" min-width="80" />
      <el-table-column label="所属部门" prop="djbmname" min-width="100" />

      <!-- ********************** 操作列 **********************
         按钮：查询View；编辑Edit；删除Delete -->
      <el-table-column label="操作" fixed="right" width="80">
        <template #default="scope">
          <el-button
            type="primary"
            icon="Pointer"
            size="small"
            link
            @click="handleConfirm(scope.row.guid, scope.row.cgxmmc)"
          >
            选择
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- ********************** 翻页 ********************** -->
    <pagination
      v-if="total > 0"
      v-model:total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="handleQuery"
    />
  </el-card>
</template>

<script setup lang="ts">
import yxApi from "@/api/cg/cgyx";
//————————————————————————————————————————————暴露的方法,和请求参数
//查询类型，新增【add】，编辑【edit】，查询【view】
const props = defineProps({
  handleReturnConfirm: {
    type: Function,
    required: true,
  },
  ysxmbh: {
    type: String,
  },
});
//——————————————————————————————————————————————————查询相关
const loading = ref(false);

const total = ref(0);
const pageData = ref<any[]>([]);
const queryParams = reactive<any>({
  pageNum: 1,
  pageSize: 10,
  cgyxdh: "",
  ysxmbh: "",
  ysxmmc: "",
  xmfzrname: "",
  djbm: "",
});
const handleQuery = () => {
  loading.value = true;
  yxApi
    .SqSelgetPage(queryParams)
    .then((res: any) => {
      console.log("shuju:");
      console.log(pageData);
      pageData.value = res.list;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
};

//——————————————————————————————————————————————————操作相关
const seletctIds = ref<number[]>([]);
// 选中项发生变化
function handleSelectionChange(selection: any[]) {
  seletctIds.value = selection.map((item) => item.guid);
  console.log(seletctIds.value);
}
const handleConfirm = (yxguid: string, cgxmmc: string) => {
  props.handleReturnConfirm(yxguid, cgxmmc);
  handleQuery();
};

//——————————————————————————————————————————————————弹窗相关参数
//const itemVisible = ref(false);
//const itemGuid = ref("");

onMounted(() => {
  queryParams.ysxmbh = props.ysxmbh;
  handleQuery();
});

watch(
  () => props.ysxmbh,
  (newValue) => {
    queryParams.sybm = newValue;
    handleQuery();
  }
);
</script>
<style lang="scss" scoped></style>
