<template>
  <div>
    <Title name="处置单信息">
      <div v-if="editable">
        <el-button type="primary" @click="handleSave()">保存</el-button>
        <el-button type="danger" @click="handleSubmit()">提交</el-button>
      </div>
    </Title>

    <el-form
      ref="dataFormRef"
      :model="formData"
      :rules="rules"
      label-width="auto"
      :inline="true"
      :disabled="!editable"
    >
      <el-form-item label="处置单号" prop="czbh">
        <el-input v-model="formData.czbh" :disabled="true" placeholder="系统自动生成" />
      </el-form-item>

      <el-form-item label="处置方式" prop="czlx">
        <DDLYcode :key="formData.czlx" v-model="formData.czlx" ycode="0102" />
      </el-form-item>

      <el-form-item label="部门资产管理员" prop="frmcode">
        <DDLUserList
          :key="formData.djbm"
          v-model="formData.frmcode"
          rcode="0202"
          :dcode="formData.djbm"
        />
      </el-form-item>

      <el-form-item label="申请人" prop="djrname">
        <el-input v-model="formData.djrname" :disabled="true" />
      </el-form-item>
      <el-form-item label="申请部门" prop="djbmname">
        <el-input v-model="formData.djbmname" :disabled="true" />
      </el-form-item>
      <div>
        <el-form-item label="处置说明" prop="czyy" style="width: 800px">
          <el-input v-model="formData.czyy" type="textarea" placeholder="处置说明" :rows="3" />
        </el-form-item>
      </div>
    </el-form>

    <Title name="处置资产清单">
      <div>
        <el-button v-if="editable" plain type="danger" icon="plus" @click="handleAddPorperties">
          添加处置资产
        </el-button>
      </div>
    </Title>

    <el-table v-loading="loading" :data="pageData" highlight-current-row :border="true" stripe>
      <el-table-column v-if="editable" type="selection" width="50" align="center" fixed />
      <el-table-column type="index" label="序号" width="55" />
      <el-table-column label="资产编号" prop="zcbh" width="120" />
      <el-table-column label="资产名称" prop="zcmc" width="200" />
      <el-table-column label="金额(元)" prop="je" width="100" />
      <el-table-column label="数量" prop="sl" min-width="80" />
      <el-table-column label="使用人" prop="syrname" width="200" />
      <el-table-column label="使用部门" prop="sybmname" width="300" />
      <!-- <el-table-column label="存放地点" prop="cfdd" width="150" /> -->
      <!-- ********************** 操作列 **********************
         按钮：查询View；编辑Edit；删除Delete -->
      <el-table-column label="操作" fixed="right" width="150">
        <template #default="scope">
          <el-button
            v-hasPerm="'sys:user:password:reset'"
            type="primary"
            icon="View"
            size="small"
            link
            @click="hancleRowView(scope.row.zcGuid)"
          >
            查看
          </el-button>
          <el-button
            v-if="editable"
            type="danger"
            icon="Delete"
            size="small"
            link
            @click="hancleRowDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- ********************** 翻页 ********************** -->
    <pagination
      v-if="total > 10"
      v-model:total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="handlePageQuery"
    />
    <!-- 附件上传 -->
    <div v-if="editable">
      <Title name="附件上传" />
      <FileUpload :guid="props.guid" code="02030301" />
    </div>
    <!-- 附件查看 -->
    <div v-else>
      <Title name="附件查看" />
      <FileView :guid="props.guid" />
    </div>
    <!-- 添加明细弹窗 -->
    <el-drawer v-model="choosedialog.visible" :title="choosedialog.title" append-to-body size="65%">
      <DisposeChoose :key="keyId" type="edit" :handleReturnConfirm="handleReturnConfirm" />
    </el-drawer>
    <!-- 查看资产弹窗 -->
    <el-drawer v-model="itemVisible" append-to-body size="70%">
      <CardPanel :key="itemGuid" :guid="itemGuid" :rkguid="itemRkGuid" />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ElLoading } from "element-plus";
import disposeAPI, { disposeForm } from "@/api/properties/dispose";
import DisposeChoose from "@/views/properties/dispose/components/DisposeChoose.vue";
import { useUserStore } from "@/store";
import CardPanel from "@/components/Properties/CardPanel/index.vue";
import BookApt from "@/api/properties/book";
import Test from "./test.vue";
//————————————————————————————————————————————暴露的方法,和请求参数

const props = defineProps({
  id: {
    type: Number,
  },
  //ID和guid
  guid: {
    type: String,
    required: true,
  },
  //保存成功后需要关闭抽屉并刷新数据
  RefreshFatherDrawer: {
    type: Function,
    required: true,
  },
  //是否可编辑
  editable: {
    type: Boolean,
    required: true,
  },
  djbm: {
    type: String,
  },
});

// 获取子组件的返回,选择的资产编号,
const handleReturnConfirm = (zcguid: string[]) => {
  if (!zcguid.length) {
    ElMessage.warning("请选择要处置的资产");
    return;
  }
  const param = { czGuid: props.guid || "", zcGuid: zcguid.join(",") };
  loading.value = true;
  const dialogloading = ElLoading.service({
    lock: true,
    text: "处理中",
  });
  disposeAPI
    .addDetail(param)
    .then(() => {
      // 添加成功
      ElMessage.success("添加成功");
      handlePageQuery();
      choosedialog.visible = false;
      keyId.value++;
    })
    .catch((error) => {
      ElMessage.error("添加处置资产失败");
    })
    .finally(() => {
      loading.value = false;
      dialogloading.close();
    });
};

//——————————————————————————————————————————————————form查询相关
//初始化一些用户的信息
const { nickname, dcode } = useUserStore().userInfo;
//表单
let formData = reactive<disposeForm>({
  czlx: "",
});
const dataFormRef = ref(ElForm);

const rules = reactive({
  frmcode: [{ required: true, message: "不能为空", trigger: "blur" }],
  // dcbmgly: [{ required: true, message: "不能为空", trigger: "blur" }],
  // drbm: [{ required: true, message: "不能为空", trigger: "blur" }],
  // drbmgly: [{ required: true, message: "不能为空", trigger: "blur" }],
  // drr: [{ required: true, message: "不能为空", trigger: "blur" }],
});
/** 初始化信息 */
function handleFormQuery() {
  disposeAPI
    .getFormData(props.guid)
    .then((res) => {
      Object.assign(formData, res);
      console.log("formData", formData);
    })
    .finally(() => {});
}

//——————————————————————————————————————————————————form操作相关
const handleSave = async () => {
  await dataFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const submitData = { ...formData };
      const id = formData.id;

      const apiCall = id ? disposeAPI.update(id, submitData) : disposeAPI.add(submitData);
      await apiCall
        .then(async (res) => {
          ElMessage.success("处置单保存成功");
          //保存成功后，重新用id去查询一次数据，处置页面不能直接新增，因此不需要重新刷新数据
          // if (!id) {
          //   handleFormQuery(Number(res));
          // }
          props.RefreshFatherDrawer();
        })
        .catch((error) => {
          ElMessage.error("操作失败，失败原因：" + error.message);
        })
        .finally(() => {});
    }
  });
};

/** 提交表单 */
const handleSubmit = async () => {
  dataFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      ElMessageBox.confirm("确认提交审批?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          await handleSave().then(async () => {
            //第二层保存完成
            const submitData = { ...formData };
            const dialogloading = ElLoading.service({
              lock: true,
              text: "处理中",
            });

            await disposeAPI
              .submit(submitData.guid || "")
              .then(() => {
                ElMessage.success("提交成功");
                props.RefreshFatherDrawer(true);
              })
              .finally(() => {
                dialogloading.close();
              })
              .catch((error) => {
                ElMessage.error(error.message);
              });
          });
        })
        .catch(() => {});
    }
  });
};
//——————————————————————————————————————————————————list查询相关
// list相关方法
const djbm = ref("");
const loading = ref(false);
const total = ref(0);
// 表格数据
const pageData = ref<any[]>([]);
//请求参数
const queryParams = reactive<any>({
  pageNum: 1,
  pageSize: 10,
  czGuid: props.guid,
});
const handlePageQuery = () => {
  //guid为空是新增
  if (props.guid != "") {
    loading.value = true;
    disposeAPI
      .getDetailsPage(queryParams)
      .then((data) => {
        pageData.value = data.list;
        total.value = data.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }
};
//——————————————————————————————————————————————————list操作相关
const removeIds = ref<number[]>([]);
//删除项
const hancleRowDelete = (id?: number) => {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }
  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      disposeAPI
        .deleteDetailsByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handlePageQuery();
          keyId.value++;
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
};

//—————————————————————————————————————————————资产选择弹窗
const keyId = ref(0);
// 弹窗显隐
const choosedialog = reactive({
  title: "资产处置明细",
  visible: false,
});

const handleAddPorperties = () => {
  choosedialog.visible = true;
};

//------------------------------打开弹窗查看资产信息
const itemVisible = ref(false);
const itemGuid = ref("");
const itemRkGuid = ref("");

let CardData = reactive<any>({});
const hancleRowView = async (guid: string) => {
  await BookApt.getFormData(guid).then((res) => {
    Object.assign(CardData, res);
    itemGuid.value = guid;
    itemRkGuid.value = CardData.rkguid;
    itemVisible.value = true;
  });
};

onMounted(() => {
  handleFormQuery();
  handlePageQuery();
});
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}
</style>
