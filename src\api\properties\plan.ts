import request from "@/utils/request";

const plan_BASE_URL = "/api/v1/plan";

const planAPI = {
  /** 获取领用分页数据 */
  getPage(queryParams?: planPageQuery) {
    return request<any, PageResult<planPageVO[]>>({
      url: `${plan_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },

  /** 获取领用分页数据 */
  getAllPage(queryParams?: planPageQuery) {
    return request<any, PageResult<planPageVO[]>>({
      url: `${plan_BASE_URL}/allpage`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取领用表单数据
   *
   * @param id planID
   * @returns plan表单数据
   */
  getFormData(guid: string) {
    return request<any, planForm>({
      url: `${plan_BASE_URL}/form/${guid}`,
      method: "get",
    });
  },

  /** 添加领用*/ // 添加/api/v1/lend/add/{guid}
  add(guid: string) {
    return request({
      url: `${plan_BASE_URL}/add/${guid}`,
      method: "post",
    });
  },

  /** 添加领用*/
  addplan(data: planForm): Promise<boolean> {
    return request<any, boolean>({
      url: `${plan_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新领用
   *
   * @param id planID
   * @param data plan表单数据
   */
  update(id: number, data: planForm): Promise<boolean> {
    return request<any, boolean>({
      url: `${plan_BASE_URL}/update/${id}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 提交领用
   *
   * @param id planID
   * @param data plan表单数据
   */
  submit(guid: string, data?: any) {
    return request({
      url: `${plan_BASE_URL}/submit/${guid}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 批量删除领用，多个以英文逗号(,)分割
   *
   * @param ids 领用ID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${plan_BASE_URL}/delete/${ids}`,
      method: "get",
    });
  },

  //———————————————————————————————— 明细相关接口————————————————————————————
  /** 添加领用明细*/
  addDetail(data: { syGuid: string; zcGuid: string }) {
    return request({
      url: `${plan_BASE_URL}/details/add`,
      method: "post",
      data: data,
    });
  },

  //回收/api/v1/plans/callback/{guid}
  callBack(guid: string) {
    return request({
      url: `${plan_BASE_URL}/callback/${guid}`,
      method: "get",
    });
  },

  //回收/api/v1/plans/stop/{guid}
  stop(guid: string) {
    return request({
      url: `${plan_BASE_URL}/stop/${guid}`,
      method: "get",
    });
  },

  //回收/api/v1/plans/node/list
  getNodeList() {
    return request({
      url: `${plan_BASE_URL}/node/list`,
      method: "get",
    });
  },
};

export default planAPI;

/** 领用分页查询参数 */
export interface planPageQuery extends PageQuery {
  /** 计划单号 */
  jhbh?: string;
  /** 计划名称 */
  jhmc?: string;
  /** 年份 */
  nf?: string;
  /** 数据编码 */
  netcode?: string;
  /** 数据状态 */
  sjzt?: string;
  /** 登记时间 */
  djsj?: string[];
  //查询类型，是新增页审核页还是查询页
  type?: string;
  //查询代办/已办
  checkstatus?: number;
}

/** 领用表单对象 */
export interface planForm {
  id?: number;
  guid?: string;
  /** 计划编号,年4位+2位 */
  jhbh?: string;
  /** 计划名称 */
  jhmc?: string;
  /** 集合编码：1初查、2抽查 */
  jhlx?: string;
  /** 清查范围 */
  qcfw?: string;
  /** 清查要求 */
  qcyq?: string;
  /** 起始日期 */
  startRq?: Date;
  /** 结束日期 */
  endRq?: Date;
  /** 登记部门 */
  djbm?: string;
  /** 登记时间 */
  djsj?: string;
  /** 登记人 */
  djr?: string;
  /** 节点编码 */
  netcode?: string;
  /** 数据状态 */
  sjzt?: string;
  /** 计划状态,0:待执行、1:在执行、2:已完成  */
  jhzt?: string;
  /** 备注 */
  notes?: string;
  /** 数据类型：已入库|已入账等 */
  dataType?: string;
  /** 资产类型:用逗号隔开,目前只选择zc_tyb的类型 */
  zclxbhs?: string;
  zclxbh?: string[];
  /** 本次盘点资产总数量 */
  zczs?: number;
  /** 入库起始时间 */
  startRksj?: Date;
  /** 入库截止时间 */
  endRksj?: Date;
  /** 起始原值 */
  startJe?: number;
  /** 截止原值 */
  endJe?: number;
  /** 部门列表 */
  bms?: string;
  bm?: string[];
  nexter?: string;
  nextbm?: string;
  /** 资产抽查比例 */
  ccbl?: number;
  /** 部门选择类型 */
  bmlx?: string;
  ycsm?: string;
  /** 登记部门流程控制字段 */
  djbm1?: string;
}

/** 领用分页对象 */
export interface planPageVO {
  /** ID */
  id?: number;
  /** Guid */
  guid?: string;
  /** 计划编号 */
  jhbh?: string;
  /** 计划名称 */
  jhmc?: string;
  /** 计划类型 */
  jhlx?: string;
  start_rq?: Date;
  end_rq?: Date;
  djsj?: Date;
  /** 数据编码 */
  netcode?: string;
  /** 数据状态 */
  sjzt?: string;
  nodemc?: string;
}
