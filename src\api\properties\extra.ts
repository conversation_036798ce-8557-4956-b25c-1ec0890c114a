import request from "@/utils/request";

const EXTRA_BASE_URL = "/api/v1/zcFsjbs";

const extraAPI = {
  /** 获取分页数据 */
  getPage(queryParams?: ZcFsjbPageQuery) {
    return request<any, PageResult<ZcFsjbPageVO[]>>({
      url: `${EXTRA_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取表单数据
   *
   * @param id ZcFsjbID
   * @returns ZcFsjb表单数据
   */
  getFormData(id: number) {
    return request<any, ZcFsjbForm>({
      url: `${EXTRA_BASE_URL}/form/${id}`,
      method: "get",
    });
  },

  /** 添加*/
  add(data: ZcFsjbForm) {
    return request({
      url: `${EXTRA_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新
   *
   * @param id ZcFsjbID
   * @param data ZcFsjb表单数据
   */
  update(id: number, data: ZcFsjbForm) {
    return request({
      url: `${EXTRA_BASE_URL}/update/${id}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 批量删除，多个以英文逗号(,)分割
   *
   * @param ids 字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${EXTRA_BASE_URL}/delete/${ids}`,
      method: "get",
    });
  },
};

export default extraAPI;

/** 分页查询参数 */
export interface ZcFsjbPageQuery extends PageQuery {}

/** 表单对象 */
export interface ZcFsjbForm {
  /** 序号 */
  id?: number;
  /** 统一编码 */
  guid?: string;
  /** 入库guid（外键） */
  rkguid?: string;
  /** 附件编号 */
  fjbh?: string;
  /** 附属件名称 */
  fsjmc?: string;
  /** 品牌 */
  pp?: string;
  /** 规格型号 */
  ggxh?: string;
  /** 计量单位 */
  jldw?: string;
  /** 数量 */
  sl?: number;
  /** 单价 */
  dj?: number;
  /** 金额 */
  je?: number;
  /** 附属件说明 */
  fsjsm?: string;
  /** 备注 */
  notes?: string;
  /** 登记时间 */
  djsj?: Date;
}

/** 分页对象 */
export interface ZcFsjbPageVO {
  /** 序号 */
  id?: number;
  /** 统一编码 */
  guid?: string;
  /** 入库guid（外键） */
  rkguid?: string;
  /** 附件编号 */
  fjbh?: string;
  /** 附属件名称 */
  fsjmc?: string;
  /** 品牌 */
  pp?: string;
  /** 规格型号 */
  ggxh?: string;
  /** 计量单位 */
  jldw?: string;
  /** 数量 */
  sl?: number;
  /** 单价 */
  dj?: number;
  /** 金额 */
  je?: number;
  /** 附属件说明 */
  fsjsm?: string;
  /** 备注 */
  notes?: string;
  /** 登记时间 */
  djsj?: Date;
}
