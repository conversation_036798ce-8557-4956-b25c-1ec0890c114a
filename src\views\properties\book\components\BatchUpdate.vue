<!-- cursor测试:1 -->
<template>
  <el-card>
    <template #header>批量修改卡片信息</template>
    <el-form ref="dataFormRef" :model="formData" label-width="auto">
      <el-form-item label="使用方向：" prop="syfx">
        <DDLXcode v-model="formData.syfx" xcode="020204" />
      </el-form-item>

      <el-form-item label="使用部门：" prop="sybm">
        <DDLDeptList v-model="formData.sybm" />
      </el-form-item>
      <el-form-item label="资产管理员" prop="gly">
        <DDLUserList v-model="formData.gly" rcode="0202" :dcode="formData.sybm" />
      </el-form-item>
      <el-form-item label="使用人：" prop="syr">
        <DDLUserList v-model="formData.syr" :dcode="formData.sybm" />
      </el-form-item>
      <el-form-item label="存放地点：" prop="cfdd">
        <CascaderACode v-model="formData.cfdd" />
      </el-form-item>
      <el-form-item label="备注：" prop="notes">
        <el-input v-model="formData.notes" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="handleSubmit()">确认修改</el-button>
      <el-button type="primary" @click="handleReset()">重置</el-button>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import cardApi, { batchUpdateParam } from "@/api/properties/card";
import { PropType } from "vue";
import CascaderCzJyCode from "@/components/Properties/CascaderCzJyCode/index.vue";
import CascaderACode from "@/components/Properties/CascaderACode/index.vue";
import { ElLoading } from "element-plus";
const props = defineProps({
  // guids: {
  //   type: String,
  //   required: true,
  //   validator: (value: string) => value.length > 0,
  // },
  ids: {
    type: String,
    required: true,
    validator: (value: string) => value.length > 0,
  },
  handleQuery: {
    type: Function as PropType<() => void>,
    required: true,
  },
});
// 自定义事件
const emit = defineEmits<{
  closebatch: [];
}>();
const formData = reactive<batchUpdateParam>({
  syfx: "",
  syr: "",
  sybm: "",
  cfdd: "",
  notes: "",
  gly: "",
});

const dataFormRef = ref<InstanceType<typeof ElForm>>();

const rules = reactive({
  syfx: [{ required: true, message: "使用方向不能为空", trigger: "blur" }],
  sybm: [{ required: true, message: "使用部门不能为空", trigger: "blur" }],
  gly: [{ required: true, message: "资产管理员不能为空", trigger: "blur" }],
  syr: [{ required: true, message: "使用人不能为空", trigger: "blur" }],
  cfdd: [{ required: true, message: "存放地点不能为空", trigger: "blur" }],
  ggxh: [{ required: false, message: "请输入规格型号", trigger: "blur" }],
});

/** 提交表单 */
const handleSubmit = () => {
  //新增遮罩功能并关闭弹窗
  dataFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      const loading = ElLoading.service({
        lock: true,
        text: "正在保存...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      cardApi
        .batchUpdate(props.ids, formData)
        .then(() => {
          ElMessage.success("修改成功");
          props.handleQuery();
          emit("closebatch");
        })
        .catch((error) => {
          console.error("批量更新资产失败:", error);
          ElMessage.error("批量更新资产失败");
        })
        .finally(() => {
          loading.close();
        });
    }
  });
};

const handleReset = () => {
  dataFormRef.value?.resetFields();
  Object.assign(formData, {
    syfx: "",
    ggxh: "",
    syr: "",
    sybm: "",
    cfdd: "",
    notes: "",
    gly: "",
  });
};

onMounted(() => {
  //handleQuery();
});
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}
</style>
