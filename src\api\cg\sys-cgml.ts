import request from "@/utils/request";

const SYSCGML_BASE_URL = "/api/v1/sysCgmls";

const SysCgmlAPI = {
  /** 获取采购目录分页数据 */
  getPage(queryParams?: SysCgmlPageQuery) {
    return request<any, PageResult<SysCgmlPageVO[]>>({
      url: `${SYSCGML_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },
  getTree() {
    return request<any, OptionType[]>({
      url: `${SYSCGML_BASE_URL}/tree`,
      method: "get",
    });
  },
  /**
   * 获取采购目录表单数据
   *
   * @param id SysCgmlID
   * @returns SysCgml表单数据
   */
  getFormData(id: number) {
    return request<any, SysCgmlForm>({
      url: `${SYSCGML_BASE_URL}/${id}/form`,
      method: "get",
    });
  },

  /** 添加采购目录*/
  add(data: SysCgmlForm) {
    return request({
      url: `${SYSCGML_BASE_URL}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新采购目录
   *
   * @param id SysCgmlID
   * @param data SysCgml表单数据
   */
  update(id: number, data: SysCgmlForm) {
    return request({
      url: `${SYSCGML_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 批量删除采购目录，多个以英文逗号(,)分割
   *
   * @param ids 采购目录ID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${SYSCGML_BASE_URL}/${ids}`,
      method: "delete",
    });
  },
};

export default SysCgmlAPI;

/** 采购目录分页查询参数 */
export interface SysCgmlPageQuery extends PageQuery {
  code?: string;
  fhbs?: string;
  /** 是否定点(1-定点 0-非定点) */
  sfdd?: string;
  /** 去掉尾号0的短编码 */
  dcode?: string;
}

/** 采购目录表单对象 */
export interface SysCgmlForm {
  id?: number;
  guid?: string;
  code?: string;
  /** 省财对应采购目录 */
  ycode?: string;
  name?: string;
  remark?: string;
  sort?: string;
  flag?: string;
  role?: string;
  xebz?: number;
  xzje?: string;
  cwcode?: string;
  cwname?: string;
  cgfs?: string;
  zzxs?: string;
  fhbs?: string;
  /** 中山竞价设备类别 */
  zssblb?: string;
  /** 中山竞价经费科目 */
  zsjfkm?: string;
  /** 是否集采(1-集采 0-非集采) */
  sfjc?: string;
  /** 是否定点(1-定点 0-非定点) */
  sfdd?: string;
  /** 定点采购的金额大于多少要上传调研报告 */
  ddje?: number;
  /** 去掉尾号0的短编码 */
  dcode?: string;
}

/** 采购目录分页对象 */
export interface SysCgmlPageVO {
  id?: number;
  guid?: string;
  code?: string;
  /** 省财对应采购目录 */
  ycode?: string;
  name?: string;
  remark?: string;
  sort?: string;
  flag?: string;
  role?: string;
  xebz?: number;
  xzje?: string;
  cwcode?: string;
  cwname?: string;
  cgfs?: string;
  zzxs?: string;
  fhbs?: string;
  /** 中山竞价设备类别 */
  zssblb?: string;
  /** 中山竞价经费科目 */
  zsjfkm?: string;
  /** 是否集采(1-集采 0-非集采) */
  sfjc?: string;
  /** 是否定点(1-定点 0-非定点) */
  sfdd?: string;
  /** 定点采购的金额大于多少要上传调研报告 */
  ddje?: number;
  /** 去掉尾号0的短编码 */
  dcode?: string;
}

// 创建一个ref来存储数据，并使用一个变量来跟踪是否已加载
const Cgmlcodes = ref<OptionType[]>([]);
// 定义一个函数来获取数据，包含缓存逻辑
const fetchCgmlcodes = async () => {
  console.log("正在获取采购目录代码树...");
  try {
    // 如果当前值为空，则调用API获取数据
    if (Cgmlcodes.value.length === 0) {
      const data = await SysCgmlAPI.getTree();
      Cgmlcodes.value = data;
    }
  } catch (error) {
    console.error("获取采购目录代码树失败:", error);
    // 可以在这里添加错误处理逻辑
  }
};

// 在模块加载时立即执行一次数据获取
(async () => {
  try {
    await fetchCgmlcodes();
  } catch (error) {
    console.error("首次获取采购目录代码树失败:", error);
  }
})();

// 导出数据和获取函数
export { Cgmlcodes, fetchCgmlcodes };
