<template>
  <div class="app-container">
   <el-card>
    <!-- ********************** 用插槽显示筛选框 ********************** -->
    <template #header>
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
                <el-form-item label="采购意向单号  年月+顺序号4位：10位" prop="cgyxdh">
                      <el-input
                          v-model="queryParams.cgyxdh"
                          placeholder="采购意向单号  年月+顺序号4位：10位"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="预算项目编码" prop="ysxmbh">
                      <el-input
                          v-model="queryParams.ysxmbh"
                          placeholder="预算项目编码"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="预算项目名称" prop="ysxmmc">
                      <el-input
                          v-model="queryParams.ysxmmc"
                          placeholder="预算项目名称"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon><Search /></template>
            搜索
          </el-button>
          <el-button @click="handleResetQuery">
            <template #icon><Refresh /></template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="列表">
        <div>
          <el-button
              v-hasPerm="['cg:zxcgYxb:add']"
              type="success"
              plain
              size="small"
              @click="formType.view = false; handleEdit()"
          >
            <template #icon><Plus /></template>
            新增
          </el-button>
          <el-button
              v-hasPerm="['cg:zxcgYxb:delete']"
              type="danger"
              plain
              size="small"
              :disabled="removeIds.length === 0"
              @click="handleDelete()"
          >
            <template #icon><Delete /></template>
            删除
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
          ref="dataTableRef"
          v-loading="loading"
          :data="pageData"
          highlight-current-row
          border
          @selection-change="handleSelectionChange"
          @expand-change="handleExpandChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed />
        <el-table-column label="展开" type="expand" width="55">
          <template #default="props">
            <div style="padding: 0px 70px; background-color: #ffffff">
              <!-- <TransferPropertiesList
                :dcbm="props.row.dcbm"
                :guid="props.row.guid || ''"
              ></TransferPropertiesList> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
                <el-table-column
                    key="cgyxdh"
                    label="采购意向单号  年月+顺序号4位：10位"
                    prop="cgyxdh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysxmbh"
                    label="预算项目编码"
                    prop="ysxmbh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysxmmc"
                    label="预算项目名称"
                    prop="ysxmmc"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysxmbh2"
                    label="预算项目编码2"
                    prop="ysxmbh2"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysxmmc2"
                    label="预算项目名称2"
                    prop="ysxmmc2"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="yspfsj"
                    label="预算批复时间"
                    prop="yspfsj"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cgxmmc"
                    label="采购项目名称 默认=预算项目名称，可改"
                    prop="cgxmmc"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cgxqgk"
                    label="采购需求概况"
                    prop="cgxqgk"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysje"
                    label="预算金额（元）"
                    prop="ysje"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="yjcgsj"
                    label="预计采购时间 到年月"
                    prop="yjcgsj"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="szbm"
                    label="所在部门"
                    prop="szbm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xmfzr"
                    label="项目负责人"
                    prop="xmfzr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="djr"
                    label="登记人"
                    prop="djr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="djbm"
                    label="登记部门"
                    prop="djbm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="djsj"
                    label="登记时间"
                    prop="djsj"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="lxr"
                    label="联系人"
                    prop="lxr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="bgdh"
                    label="办公电话"
                    prop="bgdh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="email"
                    label="电子邮箱"
                    prop="email"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cgml"
                    label="采购类别"
                    prop="cgml"
                    min-width="150"
                    align="center"
                />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button
                v-hasPerm="['cg:zxcgYxb:edit']"
                type="primary"
                size="small"
                icon="Edit"
                link
                @click="formType.view = false;handleRowEdit(scope.row.id)"
            >
              编辑
            </el-button>
            <el-button
                v-hasPerm="['cg:zxcgYxb:delete']"
                type="danger"
                size="small"
                icon="Delete"
                link
                @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
            <el-button
              v-if="check"
              type="primary"
              icon="DocumentChecked"
              size="small"
              link
              @click="hancleRowCheck"
            >
              审核
            </el-button>
            <el-button
              v-if="view"
              type="success"
              icon="Document"
              size="small"
              link
              @click="hancleRowView"
            >
              查看
            </el-button>
            <el-button
              v-if="view"
              type="primary"
              icon="Printer"
              size="small"
              link
              @click="formType.view = true;hancleRowPrint"
            >
              打印
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="handleQuery()"
      />
    </el-card>

    <!-- 意向表单弹窗 -->   
    <el-drawer 
      v-model="dialog.visible" 
      :title="dialog.title" 
      append-to-body 
      size="75%"
      :before-close="handleCloseDialog"

    >
<el-card>
        <template #header>
          <Title name="XX信息">
            <div>
              <el-button
                v-if="!formType.view"
                type="primary"
                @click="handleSubmit()"
              >
                保存
              </el-button>
              <el-button v-if="!formType.view" type="danger" @click="handleSubmit()">
                提交
              </el-button>
            </div>
          </Title>
        </template>



      <el-form ref="dataFormRef" :model="formData" :rules="rules" label-width="100px"  :inline="true"
          :disabled="formType.view">
                <el-form-item label="采购意向单号  年月+顺序号4位：10位" prop="cgyxdh">
                      <el-input
                          v-model="formData.cgyxdh"
                          placeholder="采购意向单号  年月+顺序号4位：10位"
                      />
                </el-form-item>
                <el-form-item label="预算项目编码" prop="ysxmbh">
                      <el-input
                          v-model="formData.ysxmbh"
                          placeholder="预算项目编码"
                      />
                </el-form-item>
                <el-form-item label="预算项目名称" prop="ysxmmc">
                      <el-input
                          v-model="formData.ysxmmc"
                          placeholder="预算项目名称"
                      />
                </el-form-item>
                <el-form-item label="预算项目编码2" prop="ysxmbh2">
                      <el-input
                          v-model="formData.ysxmbh2"
                          placeholder="预算项目编码2"
                      />
                </el-form-item>
                <el-form-item label="预算项目名称2" prop="ysxmmc2">
                      <el-input
                          v-model="formData.ysxmmc2"
                          placeholder="预算项目名称2"
                      />
                </el-form-item>
                <el-form-item label="预算批复时间" prop="yspfsj">
                      <el-input
                          v-model="formData.yspfsj"
                          placeholder="预算批复时间"
                      />
                </el-form-item>
                <el-form-item label="采购项目名称 默认=预算项目名称，可改" prop="cgxmmc">
                      <el-input
                          v-model="formData.cgxmmc"
                          placeholder="采购项目名称 默认=预算项目名称，可改"
                      />
                </el-form-item>
                <el-form-item label="采购需求概况" prop="cgxqgk">
                      <el-input
                          v-model="formData.cgxqgk"
                          placeholder="采购需求概况"
                      />
                </el-form-item>
                <el-form-item label="预算金额（元）" prop="ysje">
                      <el-input
                          v-model="formData.ysje"
                          placeholder="预算金额（元）"
                      />
                </el-form-item>
                <el-form-item label="预计采购时间 到年月" prop="yjcgsj">
                      <el-input
                          v-model="formData.yjcgsj"
                          placeholder="预计采购时间 到年月"
                      />
                </el-form-item>
                <el-form-item label="所在部门" prop="szbm">
                      <el-input
                          v-model="formData.szbm"
                          placeholder="所在部门"
                      />
                </el-form-item>
                <el-form-item label="项目负责人" prop="xmfzr">
                      <el-input
                          v-model="formData.xmfzr"
                          placeholder="项目负责人"
                      />
                </el-form-item>
                <el-form-item label="登记人" prop="djr">
                      <el-input
                          v-model="formData.djr"
                          placeholder="登记人"
                      />
                </el-form-item>
                <el-form-item label="登记部门" prop="djbm">
                      <el-input
                          v-model="formData.djbm"
                          placeholder="登记部门"
                      />
                </el-form-item>
                <el-form-item label="登记时间" prop="djsj">
                      <el-input
                          v-model="formData.djsj"
                          placeholder="登记时间"
                      />
                </el-form-item>
                <el-form-item label="联系人" prop="lxr">
                      <el-input
                          v-model="formData.lxr"
                          placeholder="联系人"
                      />
                </el-form-item>
                <el-form-item label="办公电话" prop="bgdh">
                      <el-input
                          v-model="formData.bgdh"
                          placeholder="办公电话"
                      />
                </el-form-item>
                <el-form-item label="电子邮箱" prop="email">
                      <el-input
                          v-model="formData.email"
                          placeholder="电子邮箱"
                      />
                </el-form-item>
                <el-form-item label="采购类别" prop="cgml">
                      <el-input
                          v-model="formData.cgml"
                          placeholder="采购类别"
                      />
                </el-form-item>
      </el-form>
      <template #footer></template>
     </el-card>
    </ el-drawer>
  </div>
</template>

<script setup lang="ts">
  defineOptions({
    name: "ZxcgYxb",
    inheritAttrs: false,
  });
  import { useUserStore } from "@/store";
  import ZxcgYxbAPI, { ZxcgYxbPageVO, ZxcgYxbForm, ZxcgYxbPageQuery } from "@/api/cg/cgyx";

  // 获取路由参数,view/add/check
  const route = useRoute();
  const type = ref(route.query.type?.toString() || "view");
  //显示隐藏权限,用于v-if
  const add = type.value == "add";
  const view = type.value == "view";
  const check = type.value == "check";
  //页面参数
  const queryFormRef = ref(ElForm);
  const loading = ref(false);
  const removeIds = ref<number[]>([]);
  const total = ref(0);

  const queryParams = reactive<ZxcgYxbPageQuery>({
    pageNum: 1,
    pageSize: 10,
  });

  // 意向表格数据
  const pageData = ref<ZxcgYxbPageVO[]>([]);

  // 弹窗
  const dialog = reactive({
    title: "",
    visible: false,
  });



  /** 查询意向 */
  function handleQuery() {
    loading.value = true;
          ZxcgYxbAPI.getPage(queryParams)
        .then((data) => {
          pageData.value = data.list;
          total.value = data.total;
        })
        .finally(() => {
          loading.value = false;
        });
  }

  /** 重置意向查询 */
  function handleResetQuery() {
    queryFormRef.value!.resetFields();
    queryParams.pageNum = 1;
    handleQuery();
  }

  /** 行复选框选中记录选中ID集合 */
  function handleSelectionChange(selection: any) {
    removeIds.value = selection.map((item: any) => item.id);
  }

  /** 打开意向弹窗 */
  function handleEdit(id?: number) {
    dialog.visible = true;
    if (id) {
      dialog.title = "修改意向";
            ZxcgYxbAPI.getFormData(id).then((data) => {
        Object.assign(formData, data);
      });
    } else {
      dialog.title = "新增意向";
    }
  }

 

  /** 关闭意向弹窗 */
  function handleCloseDialog() {
    dialog.visible = false;
    dataFormRef.value.resetFields();
    dataFormRef.value.clearValidate();
    Object.assign(formData, {});

  }

  /** 删除意向 */
  function handleDelete(id?: number) {
    const ids = [id || removeIds.value].join(",");
    if (!ids) {
      ElMessage.warning("请勾选删除项");
      return;
    }

    ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(
        () => {
          loading.value = true;
                ZxcgYxbAPI.deleteByIds(ids)
              .then(() => {
                ElMessage.success("删除成功");
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        },
        () => {
          ElMessage.info("已取消删除");
        }
    );
  }

  onMounted(() => {
    handleQuery();
  });

  //--------------以下是form表单相关
const dataFormRef = ref(ElForm);

  // 意向表单数据
  const formData = reactive<ZxcgYxbForm>({});

const formType = reactive({
  add: false,
  edit: false,
  view: false,
});

  // 意向表单校验规则
  const rules = reactive({
                      ysxmbh: [{ required: true, message: "请输入预算项目编码", trigger: "blur" }],
  });

   /** 提交意向表单 */
  function handleSubmit() {
    dataFormRef.value.validate((valid: any) => {
      if (valid) {
        loading.value = true;
        const id = formData.id;
        if (id) {
                ZxcgYxbAPI.update(id, formData)
              .then(() => {
                ElMessage.success("修改成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        } else {
                ZxcgYxbAPI.add(formData)
              .then(() => {
                ElMessage.success("新增成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        }
      }
    });
  }
</script>
<style lang="scss" scoped>

</style>
