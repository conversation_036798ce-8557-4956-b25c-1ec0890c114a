<template>
  <div class="mobile-search-bar">
    <!-- 搜索框 -->
    <div class="search-input-wrapper">
      <div class="search-input-container">
        <div class="search-icon">
          <svg-icon icon-class="search" size="16px" />
        </div>
        <input
          v-model="searchValue"
          type="text"
          :placeholder="placeholder"
          class="search-input"
          @input="handleInput"
          @keyup.enter="handleSearch"
          @focus="handleFocus"
          @blur="handleBlur"
        />
        <div v-if="searchValue" class="clear-icon" @click="handleClear">
          <svg-icon icon-class="close" size="14px" />
        </div>
      </div>
      
      <!-- 筛选按钮 -->
      <div v-if="showFilter" class="filter-button" @click="toggleFilter">
        <svg-icon icon-class="filter" size="16px" />
        <span v-if="hasActiveFilters" class="filter-dot"></span>
      </div>
    </div>

    <!-- 筛选面板 -->
    <div v-if="showFilterPanel" class="filter-panel">
      <div class="filter-header">
        <span class="filter-title">筛选条件</span>
        <div class="filter-actions">
          <button class="reset-btn" @click="handleReset">重置</button>
          <button class="confirm-btn" @click="handleConfirm">确定</button>
        </div>
      </div>
      
      <div class="filter-content">
        <slot name="filters" :filters="filters" :updateFilter="updateFilter"></slot>
      </div>
    </div>

    <!-- 搜索历史 -->
    <div v-if="showHistory && searchHistory.length > 0" class="search-history">
      <div class="history-header">
        <span class="history-title">搜索历史</span>
        <button class="clear-history-btn" @click="clearHistory">清空</button>
      </div>
      <div class="history-list">
        <div
          v-for="(item, index) in searchHistory"
          :key="index"
          class="history-item"
          @click="selectHistory(item)"
        >
          <svg-icon icon-class="history" size="14px" />
          <span class="history-text">{{ item }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface FilterItem {
  key: string
  value: any
  label?: string
}

interface Props {
  modelValue?: string
  placeholder?: string
  showFilter?: boolean
  filters?: FilterItem[]
  maxHistory?: number
  storageKey?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'search', value: string): void
  (e: 'filter', filters: FilterItem[]): void
  (e: 'clear'): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入搜索关键词',
  showFilter: false,
  maxHistory: 10,
  storageKey: 'mobile-search-history'
})

const emit = defineEmits<Emits>()

const searchValue = ref('')
const showFilterPanel = ref(false)
const showHistory = ref(false)
const filters = ref<FilterItem[]>(props.filters || [])
const searchHistory = ref<string[]>([])

// 是否有激活的筛选条件
const hasActiveFilters = computed(() => {
  return filters.value.some(filter => 
    filter.value !== undefined && 
    filter.value !== null && 
    filter.value !== '' &&
    (Array.isArray(filter.value) ? filter.value.length > 0 : true)
  )
})

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  searchValue.value = newVal || ''
}, { immediate: true })

// 监听搜索值变化
watch(searchValue, (newVal) => {
  emit('update:modelValue', newVal)
})

// 处理输入
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  searchValue.value = target.value
}

// 处理搜索
const handleSearch = () => {
  const value = searchValue.value.trim()
  if (value) {
    addToHistory(value)
    emit('search', value)
  }
  showHistory.value = false
}

// 处理清空
const handleClear = () => {
  searchValue.value = ''
  emit('clear')
  showHistory.value = false
}

// 处理焦点
const handleFocus = () => {
  loadHistory()
  showHistory.value = searchHistory.value.length > 0
}

// 处理失焦
const handleBlur = () => {
  // 延迟隐藏，避免点击历史项时立即隐藏
  setTimeout(() => {
    showHistory.value = false
  }, 200)
}

// 切换筛选面板
const toggleFilter = () => {
  showFilterPanel.value = !showFilterPanel.value
  showHistory.value = false
}

// 更新筛选条件
const updateFilter = (key: string, value: any) => {
  const index = filters.value.findIndex(f => f.key === key)
  if (index >= 0) {
    filters.value[index].value = value
  } else {
    filters.value.push({ key, value })
  }
}

// 重置筛选
const handleReset = () => {
  filters.value.forEach(filter => {
    filter.value = Array.isArray(filter.value) ? [] : ''
  })
}

// 确认筛选
const handleConfirm = () => {
  emit('filter', filters.value)
  showFilterPanel.value = false
}

// 选择历史记录
const selectHistory = (item: string) => {
  searchValue.value = item
  handleSearch()
}

// 添加到历史记录
const addToHistory = (value: string) => {
  if (!value.trim()) return
  
  // 移除重复项
  const index = searchHistory.value.indexOf(value)
  if (index > -1) {
    searchHistory.value.splice(index, 1)
  }
  
  // 添加到开头
  searchHistory.value.unshift(value)
  
  // 限制数量
  if (searchHistory.value.length > props.maxHistory) {
    searchHistory.value = searchHistory.value.slice(0, props.maxHistory)
  }
  
  // 保存到本地存储
  saveHistory()
}

// 清空历史记录
const clearHistory = () => {
  searchHistory.value = []
  saveHistory()
  showHistory.value = false
}

// 加载历史记录
const loadHistory = () => {
  try {
    const stored = localStorage.getItem(props.storageKey)
    if (stored) {
      searchHistory.value = JSON.parse(stored)
    }
  } catch (error) {
    console.error('Failed to load search history:', error)
  }
}

// 保存历史记录
const saveHistory = () => {
  try {
    localStorage.setItem(props.storageKey, JSON.stringify(searchHistory.value))
  } catch (error) {
    console.error('Failed to save search history:', error)
  }
}

// 初始化
onMounted(() => {
  loadHistory()
})
</script>

<style lang="scss" scoped>
.mobile-search-bar {
  position: relative;
  background: white;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.search-input-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20px;
  padding: 0 12px;
  height: 36px;
}

.search-icon {
  color: #999;
  margin-right: 8px;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 14px;
  outline: none;
  
  &::placeholder {
    color: #999;
  }
}

.clear-icon {
  color: #999;
  cursor: pointer;
  padding: 4px;
  
  &:active {
    color: #666;
  }
}

.filter-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: #f5f5f5;
  border-radius: 18px;
  color: #666;
  cursor: pointer;
  
  &:active {
    background: #e8e8e8;
  }
}

.filter-dot {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 6px;
  height: 6px;
  background: #ff4d4f;
  border-radius: 50%;
}

.filter-panel {
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.filter-actions {
  display: flex;
  gap: 12px;
}

.reset-btn,
.confirm-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

.reset-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: var(--el-color-primary);
  color: white;
}

.filter-content {
  padding: 16px;
}

.search-history {
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.history-title {
  font-size: 14px;
  color: #666;
}

.clear-history-btn {
  background: none;
  border: none;
  color: var(--el-color-primary);
  font-size: 12px;
  cursor: pointer;
}

.history-list {
  padding: 8px 0;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  cursor: pointer;
  
  &:active {
    background: #f5f5f5;
  }
}

.history-text {
  font-size: 14px;
  color: #333;
}
</style>
