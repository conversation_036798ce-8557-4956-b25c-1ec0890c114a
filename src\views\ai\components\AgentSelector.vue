<template>
  <div class="agent-selector">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>选择智能体</span>
          <el-button type="primary" size="small" @click="fetchAgents">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </template>

      <div v-loading="loading" class="agents-list">
        <template v-if="agents.length > 0">
          <el-radio-group v-model="selectedAgentId" @change="handleAgentChange">
            <div v-for="agent in agents" :key="agent.id" class="agent-item">
              <el-radio :label="agent.id">
                <div class="agent-info">
                  <el-avatar :src="agent.avatar" :alt="agent.name">
                    {{ agent.name.charAt(0) }}
                  </el-avatar>
                  <div class="agent-details">
                    <h4>{{ agent.name }}</h4>
                    <p>{{ agent.description }}</p>
                    <div v-if="agent.tags && agent.tags.length" class="agent-tags">
                      <el-tag v-for="tag in agent.tags" :key="tag" size="small" effect="plain">
                        {{ tag }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </el-radio>
            </div>
          </el-radio-group>
        </template>
        <el-empty v-else description="暂无可用智能体" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { Refresh } from "@element-plus/icons-vue";
import CozeAPI, { Agent } from "@/views/ai/api/index";
defineOptions({
  name: "AgentSelector",
});

// 定义事件
const emit = defineEmits(["agent-selected"]);

// 状态变量
const agents = ref<Agent[]>([]);
const selectedAgentId = ref<string>("");
const loading = ref<boolean>(false);

// 获取智能体列表
const fetchAgents = async () => {
  try {
    loading.value = true;
    const response = await CozeAPI.getAgentList("7488911497675030566");
    agents.value = response || [];

    // 如果没有选中的智能体但列表不为空，默认选中第一个
    if (!selectedAgentId.value && agents.value.length > 0) {
      selectedAgentId.value = agents.value[0].id;
      emit("agent-selected", selectedAgentId.value);
    }
  } catch (error) {
    console.error("获取智能体列表失败:", error);
    ElMessage.error("获取智能体列表失败，请重试");
  } finally {
    loading.value = false;
  }
};

// 处理智能体选择变更
const handleAgentChange = (agentId: string) => {
  emit("agent-selected", agentId);
};

// 组件挂载时获取智能体列表
onMounted(() => {
  fetchAgents();
});
</script>

<style lang="scss" scoped>
.agent-selector {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .agents-list {
    max-height: 500px;
    overflow-y: auto;

    .agent-item {
      padding: 12px 0;
      border-bottom: 1px solid var(--el-border-color-lighter);

      &:last-child {
        border-bottom: none;
      }

      .agent-info {
        display: flex;
        align-items: flex-start;
        margin-left: 8px;

        .agent-details {
          margin-left: 12px;

          h4 {
            margin: 0 0 4px;
            font-size: 14px;
          }

          p {
            margin: 0 0 8px;
            font-size: 12px;
            color: var(--el-text-color-secondary);
          }

          .agent-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
          }
        }
      }
    }
  }
}
</style>
