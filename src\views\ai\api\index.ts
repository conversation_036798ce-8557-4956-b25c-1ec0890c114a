import request from "@/utils/request";
import requestCoze from "@/utils/requestCoze";
//https://api.coze.cn/v1/space/published_bots_list

const BASE_URL = "https://api.coze.cn";
const token =
  process.env.COZE_API_TOKEN ||
  "pat_o0kfGToWVIPwPERKTn62bkhnPM75yeCBvfdjPcXSYanzP8pQ4sP1aPDkJ8dWcYBJ";
const HEADERS = {
  Authorization: "Bearer " + token,
  "Content-Type": "application/json",
};
/**
 * Coze平台API接口服务
 */
export default class CozeChatAPI {
  /**
   * 智能体列表
   * @param space_id 空间ID
   * @returns 智能体列表
   */
  static getAgentList(space_id: string) {
    return requestCoze({
      url: `${BASE_URL}` + "/v1/space/published_bots_list",
      headers: HEADERS,
      method: "get",
      params: {
        space_id: space_id,
        page_size: 10,
        page_num: 1,
      },
    });
  }

  /**
   * 初始化智能体会话
   * @param agentId 智能体ID
   * @returns 会话ID和初始信息
   */
  static initChat(agentId: string) {
    return request({
      url: `${BASE_URL}`,
      headers: HEADERS,
      method: "post",
      data: { agentId },
    });
  }

  /**
   * 发送消息到智能体
   * @param sessionId 会话ID
   * @param message 用户消息
   * @returns 智能体响应
   */
  static sendMessage(sessionId: string, message: string) {
    return request({
      url: "/api/coze/chat/send",
      method: "post",
      data: { sessionId, message },
    });
  }

  /**
   * 获取会话历史记录
   * @param sessionId 会话ID
   * @returns 会话历史记录
   */
  static getChatHistory(sessionId: string) {
    return request({
      url: "/api/coze/chat/history",
      method: "get",
      params: { sessionId },
    });
  }

  /**
   * 结束会话
   * @param sessionId 会话ID
   */
  static endChat(sessionId: string) {
    return request({
      url: "/api/coze/chat/end",
      method: "post",
      data: { sessionId },
    });
  }
}

/**
 * Coze平台API相关类型定义
 */

/**
 * 智能体信息
 */
export interface Agent {
  /** 智能体ID */
  id: string;
  /** 智能体名称 */
  name: string;
  /** 智能体描述 */
  description?: string;
  /** 智能体头像 */
  avatar?: string;
  /** 智能体标签 */
  tags?: string[];
}

/**
 * 消息类型
 */
export enum MessageType {
  /** 用户消息 */
  USER = "user",
  /** 智能体消息 */
  AGENT = "agent",
  /** 系统消息 */
  SYSTEM = "system",
}

/**
 * 消息内容类型
 */
export enum ContentType {
  /** 文本消息 */
  TEXT = "text",
  /** 图片消息 */
  IMAGE = "image",
  /** 文件消息 */
  FILE = "file",
}

/**
 * 消息内容
 */
export interface MessageContent {
  /** 内容类型 */
  type: ContentType;
  /** 内容值 */
  value: string;
}

/**
 * 聊天消息
 */
export interface ChatMessage {
  /** 消息ID */
  id: string;
  /** 会话ID */
  sessionId: string;
  /** 消息类型 */
  type: MessageType;
  /** 发送者ID */
  senderId: string;
  /** 发送者名称 */
  senderName: string;
  /** 消息内容 */
  content: MessageContent[];
  /** 发送时间 */
  timestamp: number;
  /** 是否正在加载 */
  loading?: boolean;
}

/**
 * 会话信息
 */
export interface ChatSession {
  /** 会话ID */
  id: string;
  /** 智能体ID */
  agentId: string;
  /** 会话标题 */
  title: string;
  /** 创建时间 */
  createdAt: number;
  /** 最后活动时间 */
  lastActiveAt: number;
  /** 消息列表 */
  messages: ChatMessage[];
}

/**
 * 初始化会话响应
 */
export interface InitChatResponse {
  /** 会话ID */
  sessionId: string;
  /** 会话信息 */
  session: ChatSession;
}

/**
 * 发送消息响应
 */
export interface SendMessageResponse {
  /** 消息ID */
  messageId: string;
  /** 智能体响应消息 */
  reply: ChatMessage;
}
