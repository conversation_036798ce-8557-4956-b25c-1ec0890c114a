<template>
  <div class="mobile-search-home">
    <!-- 搜索入口 -->
    <div class="search-entry">
      <div class="search-box" @click="showSearchModal = true">
        <svg-icon icon-class="search" size="16px" />
        <span class="search-placeholder">搜索资产、用户、部门...</span>
      </div>
    </div>

    <!-- 快捷查询 -->
    <div class="quick-search">
      <div class="section-title">快捷查询</div>
      <div class="quick-grid">
        <div class="quick-item" @click="navigateTo('/mobile/search/asset')">
          <div class="quick-icon asset">
            <svg-icon icon-class="asset" size="24px" />
          </div>
          <div class="quick-text">资产查询</div>
          <div class="quick-desc">查询资产信息</div>
        </div>
        
        <div class="quick-item" @click="navigateTo('/mobile/search/user')">
          <div class="quick-icon user">
            <svg-icon icon-class="user" size="24px" />
          </div>
          <div class="quick-text">用户查询</div>
          <div class="quick-desc">查询用户信息</div>
        </div>
        
        <div class="quick-item" @click="navigateTo('/mobile/search/dept')">
          <div class="quick-icon dept">
            <svg-icon icon-class="department" size="24px" />
          </div>
          <div class="quick-text">部门查询</div>
          <div class="quick-desc">查询部门信息</div>
        </div>
        
        <div class="quick-item" @click="handleScanSearch">
          <div class="quick-icon scan">
            <svg-icon icon-class="scan" size="24px" />
          </div>
          <div class="quick-text">扫码查询</div>
          <div class="quick-desc">扫描二维码</div>
        </div>
      </div>
    </div>

    <!-- 搜索历史 -->
    <div v-if="searchHistory.length > 0" class="search-history">
      <div class="section-title">
        <span>搜索历史</span>
        <button class="clear-btn" @click="clearHistory">清空</button>
      </div>
      <div class="history-list">
        <div
          v-for="(item, index) in searchHistory"
          :key="index"
          class="history-item"
          @click="searchFromHistory(item)"
        >
          <svg-icon icon-class="history" size="14px" />
          <span class="history-text">{{ item.keyword }}</span>
          <span class="history-type">{{ getTypeText(item.type) }}</span>
        </div>
      </div>
    </div>

    <!-- 热门搜索 -->
    <div class="hot-search">
      <div class="section-title">热门搜索</div>
      <div class="hot-tags">
        <div
          v-for="tag in hotSearchTags"
          :key="tag.keyword"
          class="hot-tag"
          @click="searchHotTag(tag)"
        >
          {{ tag.keyword }}
        </div>
      </div>
    </div>

    <!-- 搜索统计 -->
    <div class="search-stats">
      <div class="section-title">查询统计</div>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-number">{{ stats.assetCount }}</div>
          <div class="stat-label">资产总数</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ stats.userCount }}</div>
          <div class="stat-label">用户总数</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ stats.deptCount }}</div>
          <div class="stat-label">部门总数</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ stats.searchCount }}</div>
          <div class="stat-label">今日查询</div>
        </div>
      </div>
    </div>

    <!-- 全局搜索弹窗 -->
    <div v-if="showSearchModal" class="search-modal-overlay" @click="closeSearchModal">
      <div class="search-modal" @click.stop>
        <div class="modal-header">
          <div class="modal-search-box">
            <svg-icon icon-class="search" size="16px" />
            <input
              v-model="globalSearchKeyword"
              type="text"
              placeholder="输入关键词搜索..."
              class="modal-search-input"
              @keyup.enter="performGlobalSearch"
              ref="searchInput"
            />
            <button v-if="globalSearchKeyword" class="clear-btn" @click="globalSearchKeyword = ''">
              <svg-icon icon-class="close" size="14px" />
            </button>
          </div>
          <button class="cancel-btn" @click="closeSearchModal">取消</button>
        </div>
        
        <div class="modal-content">
          <!-- 搜索类型选择 -->
          <div class="search-types">
            <div
              v-for="type in searchTypes"
              :key="type.key"
              class="search-type"
              :class="{ active: selectedSearchType === type.key }"
              @click="selectedSearchType = type.key"
            >
              <svg-icon :icon-class="type.icon" size="16px" />
              <span>{{ type.label }}</span>
            </div>
          </div>
          
          <!-- 搜索建议 -->
          <div v-if="searchSuggestions.length > 0" class="search-suggestions">
            <div class="suggestions-title">搜索建议</div>
            <div
              v-for="suggestion in searchSuggestions"
              :key="suggestion.id"
              class="suggestion-item"
              @click="selectSuggestion(suggestion)"
            >
              <svg-icon :icon-class="getSuggestionIcon(suggestion.type)" size="14px" />
              <span class="suggestion-text">{{ suggestion.text }}</span>
              <span class="suggestion-type">{{ getTypeText(suggestion.type) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'MobileSearchHome'
})

const router = useRouter()

// 搜索相关
const showSearchModal = ref(false)
const globalSearchKeyword = ref('')
const selectedSearchType = ref('all')
const searchInput = ref<HTMLInputElement>()

// 搜索历史
const searchHistory = ref<any[]>([])

// 热门搜索标签
const hotSearchTags = ref([
  { keyword: '电脑', type: 'asset' },
  { keyword: '打印机', type: 'asset' },
  { keyword: '张三', type: 'user' },
  { keyword: '技术部', type: 'dept' },
  { keyword: '办公桌', type: 'asset' },
  { keyword: '李四', type: 'user' }
])

// 搜索建议
const searchSuggestions = ref<any[]>([])

// 统计数据
const stats = ref({
  assetCount: 0,
  userCount: 0,
  deptCount: 0,
  searchCount: 0
})

// 搜索类型配置
const searchTypes = [
  { key: 'all', label: '全部', icon: 'search' },
  { key: 'asset', label: '资产', icon: 'asset' },
  { key: 'user', label: '用户', icon: 'user' },
  { key: 'dept', label: '部门', icon: 'department' }
]

// 导航到指定页面
const navigateTo = (path: string) => {
  router.push(path)
}

// 扫码搜索
const handleScanSearch = () => {
  ElMessage.info('扫码功能开发中')
}

// 显示搜索弹窗
const showSearchModalHandler = async () => {
  showSearchModal.value = true
  await nextTick()
  searchInput.value?.focus()
}

// 关闭搜索弹窗
const closeSearchModal = () => {
  showSearchModal.value = false
  globalSearchKeyword.value = ''
  searchSuggestions.value = []
}

// 执行全局搜索
const performGlobalSearch = () => {
  if (!globalSearchKeyword.value.trim()) return
  
  // 添加到搜索历史
  addToHistory(globalSearchKeyword.value, selectedSearchType.value)
  
  // 根据搜索类型跳转到对应页面
  const searchPath = getSearchPath(selectedSearchType.value)
  router.push({
    path: searchPath,
    query: { keyword: globalSearchKeyword.value }
  })
  
  closeSearchModal()
}

// 从历史记录搜索
const searchFromHistory = (item: any) => {
  const searchPath = getSearchPath(item.type)
  router.push({
    path: searchPath,
    query: { keyword: item.keyword }
  })
}

// 搜索热门标签
const searchHotTag = (tag: any) => {
  const searchPath = getSearchPath(tag.type)
  router.push({
    path: searchPath,
    query: { keyword: tag.keyword }
  })
  
  // 添加到搜索历史
  addToHistory(tag.keyword, tag.type)
}

// 选择搜索建议
const selectSuggestion = (suggestion: any) => {
  globalSearchKeyword.value = suggestion.text
  selectedSearchType.value = suggestion.type
  performGlobalSearch()
}

// 获取搜索路径
const getSearchPath = (type: string) => {
  switch (type) {
    case 'asset':
      return '/mobile/search/asset'
    case 'user':
      return '/mobile/search/user'
    case 'dept':
      return '/mobile/search/dept'
    default:
      return '/mobile/search/asset' // 默认搜索资产
  }
}

// 获取类型文本
const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    asset: '资产',
    user: '用户',
    dept: '部门',
    all: '全部'
  }
  return typeMap[type] || '未知'
}

// 获取建议图标
const getSuggestionIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    asset: 'asset',
    user: 'user',
    dept: 'department'
  }
  return iconMap[type] || 'search'
}

// 添加到搜索历史
const addToHistory = (keyword: string, type: string) => {
  const historyItem = { keyword, type, timestamp: Date.now() }
  
  // 移除重复项
  const index = searchHistory.value.findIndex(item => 
    item.keyword === keyword && item.type === type
  )
  if (index > -1) {
    searchHistory.value.splice(index, 1)
  }
  
  // 添加到开头
  searchHistory.value.unshift(historyItem)
  
  // 限制数量
  if (searchHistory.value.length > 10) {
    searchHistory.value = searchHistory.value.slice(0, 10)
  }
  
  // 保存到本地存储
  saveHistory()
}

// 清空搜索历史
const clearHistory = () => {
  searchHistory.value = []
  saveHistory()
}

// 加载搜索历史
const loadHistory = () => {
  try {
    const stored = localStorage.getItem('mobile-search-history')
    if (stored) {
      searchHistory.value = JSON.parse(stored)
    }
  } catch (error) {
    console.error('Failed to load search history:', error)
  }
}

// 保存搜索历史
const saveHistory = () => {
  try {
    localStorage.setItem('mobile-search-history', JSON.stringify(searchHistory.value))
  } catch (error) {
    console.error('Failed to save search history:', error)
  }
}

// 加载统计数据
const loadStats = async () => {
  try {
    // 这里应该调用实际的API
    stats.value = {
      assetCount: 1234,
      userCount: 56,
      deptCount: 12,
      searchCount: 89
    }
  } catch (error) {
    console.error('Failed to load stats:', error)
  }
}

// 监听搜索关键词变化，提供搜索建议
watch(globalSearchKeyword, async (newKeyword) => {
  if (newKeyword.length > 1) {
    // 模拟搜索建议
    searchSuggestions.value = [
      { id: 1, text: `${newKeyword}相关资产`, type: 'asset' },
      { id: 2, text: `${newKeyword}相关用户`, type: 'user' },
      { id: 3, text: `${newKeyword}相关部门`, type: 'dept' }
    ]
  } else {
    searchSuggestions.value = []
  }
})

onMounted(() => {
  loadHistory()
  loadStats()
})
</script>

<style lang="scss" scoped>
.mobile-search-home {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 16px;
}

.search-entry {
  margin-bottom: 20px;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  
  &:active {
    background: #f8f8f8;
  }
}

.search-placeholder {
  flex: 1;
  color: #999;
  font-size: 14px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.clear-btn {
  background: none;
  border: none;
  color: var(--el-color-primary);
  font-size: 12px;
  cursor: pointer;
}

.quick-search {
  margin-bottom: 20px;
}

.quick-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.quick-item {
  background: white;
  border-radius: 12px;
  padding: 20px 16px;
  text-align: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
  
  &:active {
    transform: scale(0.98);
  }
}

.quick-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 12px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.asset {
    background: #e6f7ff;
    color: #1890ff;
  }
  
  &.user {
    background: #f6ffed;
    color: #52c41a;
  }
  
  &.dept {
    background: #fff7e6;
    color: #fa8c16;
  }
  
  &.scan {
    background: #fff2f0;
    color: #ff4d4f;
  }
}

.quick-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.quick-desc {
  font-size: 12px;
  color: #999;
}

.search-history {
  margin-bottom: 20px;
}

.history-list {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background: #f5f5f5;
  }
}

.history-text {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.history-type {
  font-size: 12px;
  color: #999;
  padding: 2px 6px;
  background: #f0f0f0;
  border-radius: 8px;
}

.hot-search {
  margin-bottom: 20px;
}

.hot-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.hot-tag {
  padding: 6px 12px;
  background: white;
  border-radius: 16px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  &:active {
    background: #f0f0f0;
  }
}

.search-stats {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #999;
}

// 搜索弹窗样式
.search-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.search-modal {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: white;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-search-box {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 20px;
}

.modal-search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 14px;
  outline: none;
}

.cancel-btn {
  background: none;
  border: none;
  color: var(--el-color-primary);
  font-size: 14px;
  cursor: pointer;
}

.modal-content {
  padding: 16px;
}

.search-types {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
}

.search-type {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 16px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  
  &.active {
    background: var(--el-color-primary);
    color: white;
  }
}

.search-suggestions {
  .suggestions-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  cursor: pointer;
  
  &:active {
    background: #f5f5f5;
  }
}

.suggestion-text {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.suggestion-type {
  font-size: 12px;
  color: #999;
}
</style>
