<template>
  <div class="mobile-asset-search">
    <!-- 搜索栏 -->
    <SearchBar
      v-model="searchKeyword"
      placeholder="搜索资产名称或编号"
      :show-filter="true"
      :filters="filterConfig"
      @search="handleSearch"
      @filter="handleFilter"
      @clear="handleClear"
    >
      <template #filters="{ filters, updateFilter }">
        <FilterPanel :filters="filterConfig" @update="updateFilter" />
      </template>
    </SearchBar>

    <!-- 搜索结果 -->
    <div class="search-results">
      <!-- 结果统计 -->
      <div v-if="hasSearched" class="result-stats">
        <span class="stats-text">找到 {{ total }} 条结果</span>
        <div class="sort-button" @click="showSortPanel = true">
          <svg-icon icon-class="sort" size="14px" />
          <span>排序</span>
        </div>
      </div>

      <!-- 资产列表 -->
      <div class="asset-list">
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <span>搜索中...</span>
        </div>

        <div v-else-if="assetList.length === 0 && hasSearched" class="empty-result">
          <svg-icon icon-class="empty" size="48px" />
          <div class="empty-text">未找到相关资产</div>
          <div class="empty-tips">请尝试调整搜索条件</div>
        </div>

        <div v-else class="asset-items">
          <div
            v-for="asset in assetList"
            :key="asset.id"
            class="asset-item"
            @click="viewAssetDetail(asset)"
          >
            <div class="asset-header">
              <div class="asset-name">{{ asset.zcmc }}</div>
              <div class="asset-status" :class="getStatusClass(asset.status)">
                {{ getStatusText(asset.status) }}
              </div>
            </div>
            
            <div class="asset-info">
              <div class="info-row">
                <span class="info-label">资产编号:</span>
                <span class="info-value">{{ asset.zcbh }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">使用人:</span>
                <span class="info-value">{{ asset.syrname || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">使用部门:</span>
                <span class="info-value">{{ asset.syrbmname || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">资产价值:</span>
                <span class="info-value price">¥{{ formatPrice(asset.zcjz) }}</span>
              </div>
            </div>

            <div class="asset-actions">
              <button class="action-btn" @click.stop="viewAssetDetail(asset)">
                查看详情
              </button>
              <button class="action-btn secondary" @click.stop="scanAsset(asset)">
                扫码
              </button>
            </div>
          </div>
        </div>

        <!-- 加载更多 -->
        <div v-if="hasMore && !loading" class="load-more">
          <button class="load-more-btn" @click="loadMore">
            加载更多
          </button>
        </div>
      </div>
    </div>

    <!-- 排序面板 -->
    <div v-if="showSortPanel" class="sort-overlay" @click="showSortPanel = false">
      <div class="sort-panel" @click.stop>
        <div class="sort-header">
          <span class="sort-title">排序方式</span>
          <button class="close-btn" @click="showSortPanel = false">
            <svg-icon icon-class="close" size="16px" />
          </button>
        </div>
        <div class="sort-options">
          <div
            v-for="option in sortOptions"
            :key="option.value"
            class="sort-option"
            :class="{ active: currentSort === option.value }"
            @click="handleSort(option.value)"
          >
            {{ option.label }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import SearchBar from '@/components/mobile/SearchBar/index.vue'
import FilterPanel from '@/components/mobile/FilterPanel/index.vue'
import PropertyCardsAPI, { type PropertyListQuery } from '@/api/properties/propertyCards'

defineOptions({
  name: 'MobileAssetSearch'
})

// 搜索相关
const searchKeyword = ref('')
const hasSearched = ref(false)
const loading = ref(false)
const showSortPanel = ref(false)
const currentSort = ref('createTime_desc')

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const hasMore = computed(() => assetList.value.length < total.value)

// 数据
const assetList = ref<any[]>([])

// 查询参数
const queryParams = reactive<PropertyListQuery>({
  pageNum: 1,
  pageSize: 20,
  zcbh: '',
  syr: '',
  sybm: ''
})

// 筛选配置
const filterConfig = ref([
  {
    key: 'status',
    label: '资产状态',
    type: 'radio' as const,
    value: '',
    options: [
      { label: '全部', value: '' },
      { label: '在用', value: '1' },
      { label: '闲置', value: '2' },
      { label: '维修', value: '3' },
      { label: '报废', value: '4' }
    ]
  },
  {
    key: 'priceRange',
    label: '价值范围',
    type: 'numberrange' as const,
    value: ['', ''],
    placeholder: ['最低价值', '最高价值']
  },
  {
    key: 'dateRange',
    label: '购置日期',
    type: 'daterange' as const,
    value: ['', '']
  }
])

// 排序选项
const sortOptions = [
  { label: '按创建时间降序', value: 'createTime_desc' },
  { label: '按创建时间升序', value: 'createTime_asc' },
  { label: '按价值降序', value: 'price_desc' },
  { label: '按价值升序', value: 'price_asc' },
  { label: '按名称A-Z', value: 'name_asc' },
  { label: '按名称Z-A', value: 'name_desc' }
]

// 处理搜索
const handleSearch = (keyword: string) => {
  searchKeyword.value = keyword
  resetPagination()
  performSearch()
}

// 处理筛选
const handleFilter = (filters: any[]) => {
  // 更新查询参数
  filters.forEach(filter => {
    switch (filter.key) {
      case 'status':
        queryParams.status = filter.value
        break
      case 'priceRange':
        queryParams.minPrice = filter.value[0]
        queryParams.maxPrice = filter.value[1]
        break
      case 'dateRange':
        queryParams.startDate = filter.value[0]
        queryParams.endDate = filter.value[1]
        break
    }
  })
  
  resetPagination()
  performSearch()
}

// 处理清空
const handleClear = () => {
  searchKeyword.value = ''
  resetFilters()
  resetPagination()
  assetList.value = []
  hasSearched.value = false
}

// 执行搜索
const performSearch = async () => {
  loading.value = true
  hasSearched.value = true
  
  try {
    // 设置搜索关键词
    queryParams.zcbh = searchKeyword.value
    queryParams.pageNum = currentPage.value
    queryParams.pageSize = pageSize.value
    
    const response = await PropertyCardsAPI.getPropertyCardList(queryParams)
    
    if (currentPage.value === 1) {
      assetList.value = response.list || []
    } else {
      assetList.value.push(...(response.list || []))
    }
    
    total.value = response.total || 0
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = () => {
  currentPage.value++
  performSearch()
}

// 处理排序
const handleSort = (sortValue: string) => {
  currentSort.value = sortValue
  queryParams.sortBy = sortValue
  resetPagination()
  performSearch()
  showSortPanel.value = false
}

// 重置分页
const resetPagination = () => {
  currentPage.value = 1
  queryParams.pageNum = 1
}

// 重置筛选
const resetFilters = () => {
  filterConfig.value.forEach(filter => {
    if (filter.type === 'radio') {
      filter.value = ''
    } else if (filter.type === 'checkbox') {
      filter.value = []
    } else if (filter.type === 'daterange' || filter.type === 'numberrange') {
      filter.value = ['', '']
    }
  })
}

// 查看资产详情
const viewAssetDetail = (asset: any) => {
  // 跳转到资产详情页面
  ElMessage.info('跳转到资产详情页面')
}

// 扫码功能
const scanAsset = (asset: any) => {
  ElMessage.info('扫码功能开发中')
}

// 获取状态样式类
const getStatusClass = (status: string) => {
  const statusMap: Record<string, string> = {
    '1': 'status-active',
    '2': 'status-idle',
    '3': 'status-repair',
    '4': 'status-scrap'
  }
  return statusMap[status] || ''
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '1': '在用',
    '2': '闲置',
    '3': '维修',
    '4': '报废'
  }
  return statusMap[status] || '未知'
}

// 格式化价格
const formatPrice = (price: number) => {
  if (!price) return '0.00'
  return price.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

onMounted(() => {
  // 可以在这里加载默认数据或热门搜索
})
</script>

<style lang="scss" scoped>
.mobile-asset-search {
  background: #f5f5f5;
  min-height: 100vh;
}

.search-results {
  padding: 0 16px;
}

.result-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 12px;
}

.stats-text {
  font-size: 14px;
  color: #666;
}

.sort-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: white;
  border-radius: 16px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: #666;
  font-size: 14px;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f0f0f0;
  border-top: 2px solid var(--el-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-result {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-text {
  font-size: 16px;
  margin: 16px 0 8px;
}

.empty-tips {
  font-size: 12px;
}

.asset-items {
  .asset-item {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    
    &:active {
      background: #f8f8f8;
    }
  }
}

.asset-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.asset-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  flex: 1;
  margin-right: 12px;
}

.asset-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  
  &.status-active {
    background: #f6ffed;
    color: #52c41a;
  }
  
  &.status-idle {
    background: #fff7e6;
    color: #fa8c16;
  }
  
  &.status-repair {
    background: #e6f7ff;
    color: #1890ff;
  }
  
  &.status-scrap {
    background: #fff2f0;
    color: #ff4d4f;
  }
}

.asset-info {
  margin-bottom: 12px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.info-label {
  color: #999;
}

.info-value {
  color: #333;
  
  &.price {
    color: #ff6b35;
    font-weight: 500;
  }
}

.asset-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  flex: 1;
  height: 32px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  
  &:not(.secondary) {
    background: var(--el-color-primary);
    color: white;
  }
  
  &.secondary {
    background: #f5f5f5;
    color: #666;
  }
}

.load-more {
  text-align: center;
  padding: 20px;
}

.load-more-btn {
  padding: 12px 24px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  color: #666;
  cursor: pointer;
  
  &:active {
    background: #f5f5f5;
  }
}

.sort-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.sort-panel {
  width: 100%;
  background: white;
  border-radius: 12px 12px 0 0;
  max-height: 50vh;
  overflow-y: auto;
}

.sort-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.sort-title {
  font-size: 16px;
  font-weight: 500;
}

.close-btn {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
}

.sort-options {
  padding: 8px 0;
}

.sort-option {
  padding: 12px 20px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  
  &.active {
    color: var(--el-color-primary);
    background: #f0f7ff;
  }
  
  &:active {
    background: #f5f5f5;
  }
}
</style>
