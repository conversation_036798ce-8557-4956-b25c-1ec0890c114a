// 移动端专用样式

// 基础变量
:root {
  --mobile-navbar-height: 44px;
  --mobile-tabbar-height: 50px;
  --mobile-safe-area-bottom: env(safe-area-inset-bottom);
}

// 移动端基础样式重置
@media screen and (max-width: 992px) {
  html {
    font-size: 16px;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
      'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  // 移动端容器样式
  .mobile-container {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    position: relative;
  }

  // 移动端滚动优化
  .mobile-scroll {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    
    &::-webkit-scrollbar {
      display: none;
    }
  }

  // 移动端按钮样式
  .mobile-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    user-select: none;
    -webkit-user-select: none;
    
    &:active {
      transform: scale(0.98);
    }
    
    &.primary {
      background: var(--el-color-primary);
      color: white;
      
      &:active {
        background: var(--el-color-primary-light-3);
      }
    }
    
    &.secondary {
      background: #f5f5f5;
      color: #333;
      
      &:active {
        background: #e8e8e8;
      }
    }
    
    &.danger {
      background: #ff4d4f;
      color: white;
      
      &:active {
        background: #ff7875;
      }
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none !important;
    }
  }

  // 移动端输入框样式
  .mobile-input {
    width: 100%;
    height: 48px;
    padding: 0 16px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 16px;
    background: #fff;
    outline: none;
    transition: border-color 0.2s;
    
    &:focus {
      border-color: var(--el-color-primary);
    }
    
    &::placeholder {
      color: #999;
    }
    
    &:disabled {
      background: #f5f5f5;
      color: #999;
    }
  }

  // 移动端卡片样式
  .mobile-card {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    &.no-padding {
      padding: 0;
    }
    
    &.no-margin {
      margin-bottom: 0;
    }
  }

  // 移动端列表样式
  .mobile-list {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    
    .list-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: background-color 0.2s;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:active {
        background-color: #f5f5f5;
      }
      
      .item-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        border-radius: 8px;
        background: #f0f7ff;
        color: var(--el-color-primary);
      }
      
      .item-content {
        flex: 1;
        
        .item-title {
          font-size: 16px;
          color: #333;
          margin-bottom: 4px;
        }
        
        .item-desc {
          font-size: 14px;
          color: #999;
        }
      }
      
      .item-extra {
        color: #999;
        font-size: 14px;
      }
      
      .item-arrow {
        margin-left: 8px;
        color: #ccc;
      }
    }
  }

  // 移动端表单样式
  .mobile-form {
    .form-group {
      margin-bottom: 20px;
      
      .form-label {
        display: block;
        font-size: 14px;
        color: #333;
        margin-bottom: 8px;
        font-weight: 500;
      }
      
      .form-control {
        @extend .mobile-input;
      }
      
      .form-error {
        font-size: 12px;
        color: #ff4d4f;
        margin-top: 4px;
      }
    }
  }

  // 移动端弹窗样式
  .mobile-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
    
    .modal-content {
      background: white;
      border-radius: 12px;
      width: 100%;
      max-width: 400px;
      max-height: 80vh;
      overflow: hidden;
      
      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
        
        .modal-title {
          font-size: 18px;
          font-weight: 500;
        }
        
        .modal-close {
          cursor: pointer;
          color: #999;
          padding: 4px;
        }
      }
      
      .modal-body {
        padding: 20px;
        max-height: 60vh;
        overflow-y: auto;
      }
      
      .modal-footer {
        display: flex;
        gap: 12px;
        padding: 16px 20px;
        border-top: 1px solid #f0f0f0;
        
        .modal-button {
          @extend .mobile-button;
          flex: 1;
          height: 40px;
        }
      }
    }
  }

  // 移动端安全区域适配
  .mobile-safe-area {
    padding-bottom: var(--mobile-safe-area-bottom);
  }

  // 移动端隐藏PC端元素
  .pc-only {
    display: none !important;
  }

  // 移动端显示元素
  .mobile-only {
    display: block !important;
  }

  // 移动端文本样式
  .mobile-text {
    &.large {
      font-size: 18px;
      line-height: 1.4;
    }
    
    &.medium {
      font-size: 16px;
      line-height: 1.4;
    }
    
    &.small {
      font-size: 14px;
      line-height: 1.4;
    }
    
    &.tiny {
      font-size: 12px;
      line-height: 1.4;
    }
    
    &.primary {
      color: var(--el-color-primary);
    }
    
    &.success {
      color: var(--el-color-success);
    }
    
    &.warning {
      color: var(--el-color-warning);
    }
    
    &.danger {
      color: var(--el-color-danger);
    }
    
    &.muted {
      color: #999;
    }
  }

  // 移动端间距工具类
  .mobile-spacing {
    &.p-xs { padding: 8px; }
    &.p-sm { padding: 12px; }
    &.p-md { padding: 16px; }
    &.p-lg { padding: 20px; }
    &.p-xl { padding: 24px; }
    
    &.m-xs { margin: 8px; }
    &.m-sm { margin: 12px; }
    &.m-md { margin: 16px; }
    &.m-lg { margin: 20px; }
    &.m-xl { margin: 24px; }
    
    &.mb-xs { margin-bottom: 8px; }
    &.mb-sm { margin-bottom: 12px; }
    &.mb-md { margin-bottom: 16px; }
    &.mb-lg { margin-bottom: 20px; }
    &.mb-xl { margin-bottom: 24px; }
  }
}

// 移动端动画
@keyframes mobile-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes mobile-slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.mobile-fade-in {
  animation: mobile-fade-in 0.3s ease-out;
}

.mobile-slide-up {
  animation: mobile-slide-up 0.3s ease-out;
}
