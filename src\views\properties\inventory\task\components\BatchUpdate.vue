<template>
  <el-card>
    <template #header>批量盘点</template>
    <el-form ref="dataFormRef" :model="formData" label-width="auto">
      <el-form-item label="盘点结果" prop="qcjg">
        <DDLXcode v-model="formData.qcjg" xcode="020232" />
      </el-form-item>
      <el-form-item label="盘点情况" prop="qcqk" style="width: 800px">
        <el-input
          v-model="formData.qcqk"
          type="textarea"
          maxlength="200"
          show-word-limit
          :rows="5"
        />
      </el-form-item>
      <el-form-item label="备注" prop="notes" style="width: 800px">
        <el-input
          v-model="formData.notes"
          type="textarea"
          maxlength="200"
          show-word-limit
          :rows="5"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="handleSubmit()">确认修改</el-button>
      <el-button type="primary" @click="handleReset()">重置</el-button>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import taskApi from "@/api/properties/task";
import { ElLoading } from "element-plus";
import { PropType } from "vue";

const props = defineProps({
  ids: {
    type: String,
    required: true,
    validator: (value: string) => value.length > 0,
  },
  handleQuery: {
    type: Function as PropType<() => void>,
    required: true,
  },
  //保存成功后需要关闭抽屉并刷新数据
  RefreshFatherDrawer: {
    type: Function,
    required: true,
  },
});

const formData = reactive<any>({
  qcjg: "",
  qcqk: "",
  notes: "",
});

const dataFormRef = ref<InstanceType<typeof ElForm>>();

// 校验规则
const rules = reactive({
  qcjg: [{ required: true, message: "盘点结果不能为空", trigger: "blur" }],
  qcqk: [{ max: 200, message: "盘点情况最多200字", trigger: "blur" }],
  notes: [{ max: 200, message: "备注最多200字", trigger: "blur" }],
});

/** 提交表单 */
const handleSubmit = () => {
  dataFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      // 校验 props.ids 和 formData
      if (!props.ids || !formData.qcjg) {
        ElMessage.error("请检查输入数据是否完整");
        return;
      }

      const dialogloading = ElLoading.service({
        lock: true,
        text: "处理中",
      });

      taskApi
        .batchUpdate(props.ids, formData)
        .then(() => {
          ElMessage.success("批量更新盘点任务成功");
          props.RefreshFatherDrawer(true);
        })
        .catch((error) => {
          console.error("批量更新盘点任务失败:", error);
          ElMessage.error(`批量更新盘点任务失败: ${error.message || "未知错误"}`);
        })
        .finally(() => {
          dialogloading.close(); // 确保无论如何都会关闭loading
        });
    }
  });
};

const handleReset = () => {
  dataFormRef.value?.resetFields();
  Object.assign(formData, {
    qcjg: "",
    qcqk: "",
    notes: "",
  });
};

onMounted(() => {
  //handleQuery();
});
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}
</style>
