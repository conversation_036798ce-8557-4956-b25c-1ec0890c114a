<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="待选取采购申请列表">
        <div>
          <el-button
            type="success"
            plain
            size="small"
            @click="
              formType.view = false;
              handleOpenDialog(true, 'xm');
            "
            :disabled="sqIds.length === 0 || mxIds.length === 0"
          >
            <template #icon><Plus /></template>
            创建
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
        @expand-change="handleExpandChange"
        style="height: 260px"
      >
        <el-table-column type="selection" width="55" align="center" fixed />
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          key="sqbh"
          label="采购申请单号"
          prop="sqbh"
          min-width="125"
          align="center"
        />
        <el-table-column key="sqmc" label="项目名称" prop="sqmc" min-width="300" align="center" />
        <el-table-column key="xmbm" label="经费名称" prop="xmmc" min-width="200" align="center" />
        <el-table-column
          key="xmfzr"
          label="经费负责人"
          prop="xmfzr"
          min-width="120"
          align="center"
        />
        <el-table-column
          key="ysje"
          label="预算金额(元)"
          prop="ysje"
          min-width="110"
          align="center"
        />

        <el-table-column
          key="sqrname"
          label="申请人"
          prop="sqrname"
          min-width="100"
          align="center"
        />
        <el-table-column key="sqsj" label="申请时间" prop="sqsj" min-width="160" align="center" />

        <el-table-column
          key="sqbmmc"
          label="申请部门"
          prop="sqbmmc"
          min-width="160"
          align="center"
        />

        <el-table-column fixed="right" label="操作" width="190">
          <template #default="scope">
            <el-button
              v-if="view"
              type="success"
              icon="Document"
              size="small"
              link
              @click="handleOpenDialog(false, '', scope.row)"
            >
              查看
            </el-button>
            <el-button
              type="primary"
              size="small"
              icon="Edit"
              link
              @click="
                formType.view = false;
                handleOpenDialog(true, '', scope.row);
              "
            >
              创建
            </el-button>
            <el-button
              type="danger"
              size="small"
              icon="Delete"
              link
              @click="handleDelete(scope.row.sqid)"
            >
              退回
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 10000"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>
    <br />
    <!-- *****************************采购明细列表******************** *-->
    <el-card v-if="sqIds.length > 0">
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="待选取采购明细列表" />
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="mxdataTableRef"
        v-loading="mxloading"
        row-key="mxid"
        :data="mxpageData"
        highlight-current-row
        :border="true"
        height="260px"
        @selection-change="handleSelectionChangeMx"
      >
        <el-table-column type="selection" width="55" align="center" fixed reserve-selection />
        <el-table-column type="index" label="序号" width="55" />
        <el-table-column prop="mxbh" label="明细编号" width="160" />
        <el-table-column prop="wpmc" label="明细名称" width="320" />
        <el-table-column prop="gg" label="规格型号" width="240" />
        <el-table-column prop="sl" label="数量" width="60" />
        <el-table-column prop="dwname" label="计量单位" width="85" />
        <el-table-column prop="price" label="单价(元)" width="85" />
        <el-table-column prop="ysje" label="预算金额(元)" width="120" />
        <el-table-column fixed="right" label="操作" width="120">
          <template #default="scope">
            <el-button
              v-if="view"
              type="success"
              icon="Document"
              size="small"
              link
              @click="handleOpenDialog(false, '', scope.row)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="mxtotal > 10000"
        v-model:total="mxtotal"
        v-model:page="mxqueryParams.pageNum"
        v-model:limit="mxqueryParams.pageSize"
        @pagination="mxhandlePageQuery"
      />
    </el-card>

    <!--申请明细结束-->
    <!-- 采购任务表单弹窗 -->
    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      append-to-body
      size="80%"
      :before-close="handleCloseDialog"
    >
      <el-card>
        <!--采购任务组件-->
        <RwEdit
          :mxids="mxIds"
          :RefreshFatherDrawer="MxRefreshFatherDrawer"
          ref="RwRef"
          :editable="itemEditable"
          :key="itemGuid + '1'"
          :guid="itemGuid"
        />
      </el-card>
    </el-drawer>
  </div>
</template>

<style>
.custom-radio {
  width: 40px; /* 设置宽度 */
  display: inline-block; /* 确保宽度生效   */
}
</style>
<script setup lang="ts">
defineOptions({
  name: "ZxcgSq",
  inheritAttrs: false,
});
import { useTagsViewStore, useUserStore } from "@/store";
import ZxcgSqAPI, { ZxcgSqPageVO, ZxcgSqForm, ZxcgSqPageQuery } from "@/api/cg/zxcg-sq";
import ZxcgMxAPI, { ZxcgMxPageVO, ZxcgMxForm, ZxcgMxPageQuery } from "@/api/cg/zxcg-mx";
import ZxcgCgrwAPI, { ZxcgCgrwPageVO, ZxcgCgrwForm, ZxcgCgrwPageQuery } from "@/api/cg/zxcg-cgrw";
import { getGuid } from "@/utils/guid";
import { ref, nextTick } from "vue";
import type { ElTable } from "element-plus"; // 导入类型
import SqEdit from "@/views/cg/cgsq/components/SqEdit.vue";
import RwEdit from "@/views/cg/cgrw/components/RwEdit.vue";
import { useRouter } from "vue-router";

const userStore = useUserStore();
// 获取路由参数,view/add/check
const route = useRoute();
const type = ref(route.query.type?.toString() || "view");
//显示隐藏权限,用于v-if
const add = type.value == "add";
const view = type.value == "view";
const check = type.value == "check";
//页面参数
const queryFormRef = ref(ElForm);
const loading = ref(false);
const sqIds = ref<number[]>([]);
const apply_sqIds = ref<number[]>([]);
const total = ref(0);

const itemGuid = ref<string | undefined>();
const itemId = ref<number | undefined>();
const itemEditable = ref<boolean>(false);
const SqCgfl = ref<string | undefined>();
const queryParams = reactive<ZxcgSqPageQuery>({
  pageNum: 1,
  pageSize: 10000,
  by20: userStore.userInfo.username,
});

const formType = reactive({
  add: false,
  edit: false,
  view: false,
});

// 采购申请表格数据
const pageData = ref<ZxcgSqPageVO[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

/** 查询采购申请 */
function handleQuery() {
  loading.value = true;
  ZxcgSqAPI.getWaitRwcjPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置采购申请查询 */
/*function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}*/

/** 行复选框选中记录选中ID集合 */
const removed_sqIds = ref<number[]>([]); // 新增：记录被移除的ID
function handleSelectionChange(selection: any) {
  const oldSelection = [...sqIds.value]; // 旧的选中ID集合
  const newSelection = selection.map((item: any) => item.sqid); // 新的选中ID集合

  // 更新当前选中IDs
  sqIds.value = newSelection;

  //console.log("之前选中的IDs:", oldSelection);
  //console.log("现在选中的IDs:", newSelection);

  // 使用Set提高查找效率
  const oldSet = new Set(oldSelection);
  const newSet = new Set(newSelection);

  // 计算变化
  const addedIds = newSelection.filter((id: any) => !oldSet.has(id));
  const removedIds = oldSelection.filter((id) => !newSet.has(id));

  // 更新应用IDs
  if (addedIds.length > 0) {
    apply_sqIds.value = addedIds;
    console.log("新增选中的IDs:", apply_sqIds.value);
  } else {
    apply_sqIds.value = [];
  }

  // 更新移除IDs（如果需要）
  if (removedIds.length > 0) {
    removed_sqIds.value = removedIds;

    //这里先移除不要的sqid对应的行
    mxpageData.value.forEach((row) => {
      removed_sqIds.value.forEach((sqid) => {
        if (row.sqid === sqid) {
          mxdataTableRef.value?.toggleRowSelection(row, false);
        }
      });
    });
  } else {
    removed_sqIds.value = [];
  }
  mxhandlePageQuery();
}

/*采购任务弹窗--------------------------------------------------------*/

//const router = useRouter();
const handleOpenDialog = async (editable: boolean, sqcgfl: string, row?: ZxcgCgrwPageVO) => {
  // router.push("/cgwjedit?type=edit");
  //router.push('/user?name=john')
  //await dataFormRef.value?.validate();
  loading.value = true;
  itemGuid.value = row?.guid || getGuid();
  itemId.value = row?.rwid;
  itemEditable.value = editable;
  dialog.title = editable ? (row?.sqid ? "修改采购任务" : "新增采购任务") : "查看采购任务";
  dialog.visible = true;
  if (row) {
  } else {
    SqCgfl.value = sqcgfl;
    // console.log("SqCgfl.value", SqCgfl.value);
  }
  loading.value = false;
};
function handleCloseDialog() {
  dialog.visible = false;
  itemId.value = undefined;
  /*if (RwRef.value) {
    //await
    RwRef?.value.handleClearForm();
  }*/
}
const handleSaveSq = async () => {
  /* console.log("sqRef.value:", sqRef.value);
  if (sqRef.value) {
    console.log("我加载了，");
  }*/
};

/***采购明细 */

const mxdataTableRef = ref<InstanceType<typeof ElTable>>();
const mxIds = ref<number[]>([]);
const mxloading = ref(false);
const mxqueryParams = reactive<ZxcgMxPageQuery>({
  pageNum: 1,
  pageSize: 10000,
  sqids: sqIds ? sqIds.value.join(",") : "",
});
const mxpageData = ref<ZxcgMxPageVO[]>([]);
const mxtotal = ref(0);
/** 查询采购申请明细 */
function mxhandlePageQuery() {
  mxqueryParams.sqids = sqIds ? sqIds.value.join(",") : "";
  if (mxqueryParams.sqids) {
    mxloading.value = true;

    ZxcgMxAPI.getWaitRwcjPage(mxqueryParams)
      .then((data) => {
        mxpageData.value = data.list;
        mxtotal.value = data.total;
      })
      .finally(() => {
        mxloading.value = false;
        if (apply_sqIds.value) {
          //有增加
          mxpageData.value.forEach((row) => {
            apply_sqIds.value.forEach((sqid) => {
              if (row.sqid === sqid) {
                mxdataTableRef.value?.toggleRowSelection(row, true);
              }
            });
          });
        }
      });
  } else {
    mxpageData.value = [];
    mxtotal.value = 0;

    return;
  }
}
function handleSelectionChangeMx(selection: any) {
  mxIds.value = selection.map((item: any) => item.mxid);
}

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped></style>
