// 景区数据类型定义
export interface ScenicItem {
  id: number;
  name: string;
  level: string;
  address: string;
  openTime: string;
  ticketPrice: number;
  description?: string;
  createTime: string;
  status: number;
  images: string[]; // 新增图片数组
  rating: number; // 新增评分
  facilities: string[]; // 新增设施
}

// 酒店数据类型
export interface HotelItem {
  id: number;
  name: string;
  address: string;
  star: number; // 星级
  roomTypes: RoomType[];
  images: string[];
  facilities: string[];
  phone: string; // 新增联系电话
  introduction: string; // 新增酒店简介
  services: string[]; // 新增服务项目
}

// 房型定义
export interface RoomType {
  id: number;
  name: string;
  price: number;
  bedType: string;
  area: string;
  amenities: string[];
  images: string[];
  maxOccupancy: number; // 最大入住人数
  windowType: "有窗" | "无窗"; // 窗户类型
  breakfastIncluded: boolean; // 是否含早
}

// 订单数据结构
export interface OrderItem {
  id: number;
  orderNumber: string; // 订单号
  scenicId?: number;
  hotelId?: number;
  roomTypeId?: number;
  checkInDate: string;
  checkOutDate: string;
  quantity: number;
  totalPrice: number;
  status: "pending" | "confirmed" | "canceled";
  guestName: string; // 入住人姓名
  contactPhone: string; // 联系电话
  specialRequests: string; // 特殊要求
  createTime: string; // 下单时间
}

// 订单状态枚举
export enum OrderStatus {
  Pending = 1, // 待确认
  Confirmed = 2, // 已确认
  Cancelled = 3, // 已取消
  Completed = 4, // 已完成
}

// 房间订单数据类型
export interface RoomOrderInfo {
  id: number;
  orderNo: string;
  roomType: string;
  roomNumber: string;
  guestName: string;
  phone: string;
  checkInDate: string;
  checkOutDate: string;
  totalAmount: number;
  status: OrderStatus;
  createTime: string;
  remark?: string;
}

// 门票状态枚举
export enum TicketStatus {
  OnSale = 1, // 在售
  OffSale = 2, // 下架
  SoldOut = 3, // 售罄
}

// 门票订单状态枚举
export enum TicketOrderStatus {
  Pending = 1, // 待支付
  Paid = 2, // 已支付
  Used = 3, // 已使用
  Refunded = 4, // 已退款
  Cancelled = 5, // 已取消
}

// 门票信息接口
export interface TicketInfo {
  id: number;
  name: string; // 景点名称
  description: string; // 景点描述
  price: number; // 门票价格
  originalPrice: number; // 原价
  status: TicketStatus; // 状态
  stock: number; // 库存
  soldCount: number; // 已售数量
  validityPeriod: number; // 有效期（天）
  notice: string; // 使用须知
  images: string[]; // 景点图片
  address: string; // 景点地址
  openTime: string; // 开放时间
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
}

// 门票订单信息接口
export interface TicketOrderInfo {
  id: number;
  orderNo: string; // 订单号
  ticketId: number; // 门票ID
  ticketName: string; // 景点名称
  quantity: number; // 购买数量
  totalAmount: number; // 总金额
  status: TicketOrderStatus; // 订单状态
  visitorName: string; // 游客姓名
  visitorPhone: string; // 游客电话
  visitDate: string; // 游玩日期
  createTime: string; // 创建时间
  payTime?: string; // 支付时间
  useTime?: string; // 使用时间
  refundTime?: string; // 退款时间
}

// 模拟数据列表
export const scenicList: ScenicItem[] = [
  {
    id: 1,
    name: "西湖风景名胜区",
    level: "AAAAA",
    address: "浙江省杭州市西湖区",
    openTime: "全天开放",
    ticketPrice: 0,
    createTime: "2024-03-01",
    status: 1,
    images: ["/images/west-lake-1.jpg", "/images/west-lake-2.jpg"],
    rating: 4.8,
    facilities: ["停车场", "游客中心", "无障碍设施"],
  },
  {
    id: 2,
    name: "黄山风景区",
    level: "AAAAA",
    address: "安徽省黄山市",
    openTime: "07:00-17:00",
    ticketPrice: 190,
    createTime: "2024-03-05",
    status: 1,
    images: ["/images/huangshan-1.jpg", "/images/huangshan-2.jpg"],
    rating: 4.9,
    facilities: ["索道", "登山步道", "医疗站"],
  },
];

// 模拟订单数据
export const orderList: RoomOrderInfo[] = [
  {
    id: 1,
    orderNo: "H202403150001",
    roomType: "豪华大床房",
    roomNumber: "0601",
    guestName: "张三",
    phone: "13800138000",
    checkInDate: "2024-03-15",
    checkOutDate: "2024-03-17",
    totalAmount: 1360,
    status: OrderStatus.Pending,
    createTime: "2024-03-14 14:30:00",
  },
  {
    id: 2,
    orderNo: "H202403150002",
    roomType: "家庭套房",
    roomNumber: "0801",
    guestName: "李四",
    phone: "13900139000",
    checkInDate: "2024-03-16",
    checkOutDate: "2024-03-18",
    totalAmount: 1960,
    status: OrderStatus.Confirmed,
    createTime: "2024-03-14 15:20:00",
  },
  {
    id: 3,
    orderNo: "H202403150003",
    roomType: "商务双床房",
    roomNumber: "0901",
    guestName: "王五",
    phone: "13700137000",
    checkInDate: "2024-03-17",
    checkOutDate: "2024-03-19",
    totalAmount: 1580,
    status: OrderStatus.Cancelled,
    createTime: "2024-03-14 16:10:00",
  },
  {
    id: 4,
    orderNo: "H202403150004",
    roomType: "豪华套房",
    roomNumber: "1001",
    guestName: "赵六",
    phone: "13600136000",
    checkInDate: "2024-03-18",
    checkOutDate: "2024-03-20",
    totalAmount: 2280,
    status: OrderStatus.Confirmed,
    createTime: "2024-03-14 16:45:00",
  },
  {
    id: 5,
    orderNo: "H202403150005",
    roomType: "标准双床房",
    roomNumber: "0501",
    guestName: "钱七",
    phone: "13500135000",
    checkInDate: "2024-03-19",
    checkOutDate: "2024-03-21",
    totalAmount: 980,
    status: OrderStatus.Pending,
    createTime: "2024-03-14 17:20:00",
  },
  {
    id: 6,
    orderNo: "H202403150006",
    roomType: "豪华大床房",
    roomNumber: "0602",
    guestName: "孙八",
    phone: "13400134000",
    checkInDate: "2024-03-20",
    checkOutDate: "2024-03-22",
    totalAmount: 1360,
    status: OrderStatus.Completed,
    createTime: "2024-03-14 18:00:00",
  },
  {
    id: 7,
    orderNo: "H202403150007",
    roomType: "商务套房",
    roomNumber: "1101",
    guestName: "周九",
    phone: "13300133000",
    checkInDate: "2024-03-21",
    checkOutDate: "2024-03-23",
    totalAmount: 1880,
    status: OrderStatus.Confirmed,
    createTime: "2024-03-14 18:30:00",
  },
  {
    id: 8,
    orderNo: "H202403150008",
    roomType: "总统套房",
    roomNumber: "3601",
    guestName: "吴十",
    phone: "13200132000",
    checkInDate: "2024-03-22",
    checkOutDate: "2024-03-24",
    totalAmount: 3880,
    status: OrderStatus.Pending,
    createTime: "2024-03-14 19:15:00",
  },
  {
    id: 9,
    orderNo: "H202403150009",
    roomType: "标准大床房",
    roomNumber: "0301",
    guestName: "郑十一",
    phone: "13100131000",
    checkInDate: "2024-03-23",
    checkOutDate: "2024-03-25",
    totalAmount: 880,
    status: OrderStatus.Confirmed,
    createTime: "2024-03-14 20:00:00",
  },
  {
    id: 10,
    orderNo: "H202403150010",
    roomType: "家庭套房",
    roomNumber: "0802",
    guestName: "王十二",
    phone: "13000130000",
    checkInDate: "2024-03-24",
    checkOutDate: "2024-03-26",
    totalAmount: 1960,
    status: OrderStatus.Cancelled,
    createTime: "2024-03-14 20:45:00",
  },
  {
    id: 11,
    orderNo: "H202403150011",
    roomType: "豪华大床房",
    roomNumber: "0603",
    guestName: "李十三",
    phone: "12900129000",
    checkInDate: "2024-03-25",
    checkOutDate: "2024-03-27",
    totalAmount: 1360,
    status: OrderStatus.Pending,
    createTime: "2024-03-14 21:30:00",
  },
  {
    id: 12,
    orderNo: "H202403150012",
    roomType: "商务双床房",
    roomNumber: "0902",
    guestName: "张十四",
    phone: "12800128000",
    checkInDate: "2024-03-26",
    checkOutDate: "2024-03-28",
    totalAmount: 1580,
    status: OrderStatus.Confirmed,
    createTime: "2024-03-14 22:15:00",
  },
  {
    id: 13,
    orderNo: "H202403150013",
    roomType: "豪华套房",
    roomNumber: "1002",
    guestName: "刘十五",
    phone: "12700127000",
    checkInDate: "2024-03-27",
    checkOutDate: "2024-03-29",
    totalAmount: 2280,
    status: OrderStatus.Completed,
    createTime: "2024-03-14 23:00:00",
  },
  {
    id: 14,
    orderNo: "H202403150014",
    roomType: "标准双床房",
    roomNumber: "0502",
    guestName: "陈十六",
    phone: "12600126000",
    checkInDate: "2024-03-28",
    checkOutDate: "2024-03-30",
    totalAmount: 980,
    status: OrderStatus.Pending,
    createTime: "2024-03-14 23:45:00",
  },
  {
    id: 15,
    orderNo: "H202403150015",
    roomType: "商务套房",
    roomNumber: "1102",
    guestName: "杨十七",
    phone: "12500125000",
    checkInDate: "2024-03-29",
    checkOutDate: "2024-03-31",
    totalAmount: 1880,
    status: OrderStatus.Confirmed,
    createTime: "2024-03-15 00:30:00",
  },
  {
    id: 16,
    orderNo: "H202403150016",
    roomType: "总统套房",
    roomNumber: "3602",
    guestName: "黄十八",
    phone: "12400124000",
    checkInDate: "2024-03-30",
    checkOutDate: "2024-04-01",
    totalAmount: 3880,
    status: OrderStatus.Cancelled,
    createTime: "2024-03-15 01:15:00",
  },
  {
    id: 17,
    orderNo: "H202403150017",
    roomType: "标准大床房",
    roomNumber: "0302",
    guestName: "赵十九",
    phone: "12300123000",
    checkInDate: "2024-03-31",
    checkOutDate: "2024-04-02",
    totalAmount: 880,
    status: OrderStatus.Pending,
    createTime: "2024-03-15 02:00:00",
  },
  {
    id: 18,
    orderNo: "H202403150018",
    roomType: "家庭套房",
    roomNumber: "0803",
    guestName: "吴二十",
    phone: "12200122000",
    checkInDate: "2024-04-01",
    checkOutDate: "2024-04-03",
    totalAmount: 1960,
    status: OrderStatus.Confirmed,
    createTime: "2024-03-15 02:45:00",
  },
  {
    id: 19,
    orderNo: "H202403150019",
    roomType: "豪华大床房",
    roomNumber: "0604",
    guestName: "郑二一",
    phone: "12100121000",
    checkInDate: "2024-04-02",
    checkOutDate: "2024-04-04",
    totalAmount: 1360,
    status: OrderStatus.Completed,
    createTime: "2024-03-15 03:30:00",
  },
  {
    id: 20,
    orderNo: "H202403150020",
    roomType: "商务双床房",
    roomNumber: "0903",
    guestName: "王二二",
    phone: "12000120000",
    checkInDate: "2024-04-03",
    checkOutDate: "2024-04-05",
    totalAmount: 1580,
    status: OrderStatus.Pending,
    createTime: "2024-03-15 04:15:00",
  },
];

// 订单状态选项
export const orderStatusOptions = [
  { value: OrderStatus.Pending, label: "待确认" },
  { value: OrderStatus.Confirmed, label: "已确认" },
  { value: OrderStatus.Cancelled, label: "已取消" },
  { value: OrderStatus.Completed, label: "已完成" },
];

// 楼层选项
export const floorOptions = [
  { value: "03", label: "3楼 - 标准房" },
  { value: "05", label: "5楼 - 标准双床房" },
  { value: "06", label: "6楼 - 豪华大床房" },
  { value: "08", label: "8楼 - 家庭套房" },
  { value: "09", label: "9楼 - 商务双床房" },
  { value: "10", label: "10楼 - 豪华套房" },
  { value: "11", label: "11楼 - 商务套房" },
  { value: "36", label: "36楼 - 总统套房" },
];

// 获取订单状态类型
export const getStatusType = (
  status: OrderStatus
): "success" | "warning" | "info" | "primary" | "danger" => {
  const statusMap: Record<OrderStatus, "success" | "warning" | "info" | "primary" | "danger"> = {
    [OrderStatus.Pending]: "warning",
    [OrderStatus.Confirmed]: "success",
    [OrderStatus.Cancelled]: "danger",
    [OrderStatus.Completed]: "info",
  };
  return statusMap[status] || "info";
};

// 获取订单状态文本
export const getStatusText = (status: OrderStatus): string => {
  const statusMap: Record<OrderStatus, string> = {
    [OrderStatus.Pending]: "待确认",
    [OrderStatus.Confirmed]: "已确认",
    [OrderStatus.Cancelled]: "已取消",
    [OrderStatus.Completed]: "已完成",
  };
  return statusMap[status] || "未知状态";
};

// 门票状态选项
export const ticketStatusOptions = [
  { label: "在售", value: TicketStatus.OnSale },
  { label: "下架", value: TicketStatus.OffSale },
  { label: "售罄", value: TicketStatus.SoldOut },
];

// 门票订单状态选项
export const ticketOrderStatusOptions = [
  { label: "待支付", value: TicketOrderStatus.Pending },
  { label: "已支付", value: TicketOrderStatus.Paid },
  { label: "已使用", value: TicketOrderStatus.Used },
  { label: "已退款", value: TicketOrderStatus.Refunded },
  { label: "已取消", value: TicketOrderStatus.Cancelled },
];

// 获取门票状态类型
export const getTicketStatusType = (status: TicketStatus) => {
  switch (status) {
    case TicketStatus.OnSale:
      return "success";
    case TicketStatus.OffSale:
      return "info";
    case TicketStatus.SoldOut:
      return "danger";
    default:
      return "info";
  }
};

// 获取门票订单状态类型
export const getTicketOrderStatusType = (status: TicketOrderStatus) => {
  switch (status) {
    case TicketOrderStatus.Pending:
      return "warning";
    case TicketOrderStatus.Paid:
      return "success";
    case TicketOrderStatus.Used:
      return "info";
    case TicketOrderStatus.Refunded:
      return "danger";
    case TicketOrderStatus.Cancelled:
      return "info";
    default:
      return "info";
  }
};

// 获取门票状态文本
export const getTicketStatusText = (status: TicketStatus) => {
  return ticketStatusOptions.find((item) => item.value === status)?.label || "未知状态";
};

// 获取门票订单状态文本
export const getTicketOrderStatusText = (status: TicketOrderStatus) => {
  return ticketOrderStatusOptions.find((item) => item.value === status)?.label || "未知状态";
};

// 示例门票数据
export const mockTickets: TicketInfo[] = [
  {
    id: 1,
    name: "梁祝文化园门票",
    description:
      "梁祝文化园（又称梁祝文化公园）位于浙江省宁波市，是中国首个以梁祝爱情传说为主题的大型文化公园，也是国家AAAA级旅游景区",
    price: 80,
    originalPrice: 100,
    status: TicketStatus.OnSale,
    stock: 1000,
    soldCount: 500,
    validityPeriod: 1,
    notice: "1. 需提前一天预约\n2. 节假日可能需要加收费用\n3. 儿童1.2米以下免票",
    images: ["/src/assets/tourist/001.jpg", "/src/assets/tourist/002.jpg"],
    address: "浙江省杭州市西湖区",
    openTime: "06:00-18:00",
    createTime: "2024-03-01 12:00:00",
    updateTime: "2024-03-01 12:00:00",
  },
  {
    id: 2,
    name: "浙江嵊泗三观长滩滨海度假区门票",
    description: "浙江嵊泗三观长滩滨海度假区是一个集休闲、娱乐和观光于一体的旅游胜地",
    price: 150,
    originalPrice: 180,
    status: TicketStatus.OnSale,
    stock: 800,
    soldCount: 200,
    validityPeriod: 2,
    notice: "1. 含游船票\n2. 需提前两天预约\n3. 70岁以上老人半价",
    images: ["/src/assets/tourist/003.jpg", "/src/assets/tourist/004.jpg"],

    address: "浙江省杭州市淳安县",
    openTime: "08:00-17:00",
    createTime: "2024-03-01 12:00:00",
    updateTime: "2024-03-01 12:00:00",
  },
  {
    id: 3,
    name: "梁圣君庙门票",
    description:
      "梁圣君庙，又称梁山伯庙、梁祝庙、“义忠王庙”，是一座承载着深厚文化底蕴与传奇色彩的庙宇",
    price: 150,
    originalPrice: 180,
    status: TicketStatus.OnSale,
    stock: 800,
    soldCount: 200,
    validityPeriod: 2,
    notice: "1. 需提前一天预约\n2. 节假日可能需要加收费用\n3. 儿童1.2米以下免票",
    images: ["/src/assets/tourist/005.jpg"],
    address: "浙江省杭州市淳安县",
    openTime: "08:00-17:00",
    createTime: "2024-03-01 12:00:00",
    updateTime: "2024-03-01 12:00:00",
  },
];

// 示例门票订单数据
export const mockTicketOrders: TicketOrderInfo[] = [
  {
    id: 1,
    orderNo: "T20240301001",
    ticketId: 1,
    ticketName: "西湖景区门票",
    quantity: 2,
    totalAmount: 160,
    status: TicketOrderStatus.Paid,
    visitorName: "张三",
    visitorPhone: "13800138000",
    visitDate: "2024-03-15",
    createTime: "2024-03-01 14:30:00",
    payTime: "2024-03-01 14:35:00",
  },
  {
    id: 2,
    orderNo: "T20240301002",
    ticketId: 2,
    ticketName: "千岛湖景区门票",
    quantity: 3,
    totalAmount: 450,
    status: TicketOrderStatus.Pending,
    visitorName: "李四",
    visitorPhone: "13900139000",
    visitDate: "2024-03-20",
    createTime: "2024-03-01 15:00:00",
  },
];
