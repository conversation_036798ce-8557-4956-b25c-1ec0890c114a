<template>
  <div class="app-container">
   <el-card>
    <!-- ********************** 用插槽显示筛选框 ********************** -->
    <template #header>
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
                <el-form-item label="申请id" prop="sqid">
                      <el-input
                          v-model="queryParams.sqid"
                          placeholder="申请id"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="rwid" prop="rwid">
                      <el-input
                          v-model="queryParams.rwid"
                          placeholder="rwid"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="wjid" prop="wjid">
                      <el-input
                          v-model="queryParams.wjid"
                          placeholder="wjid"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="bdid" prop="bdid">
                      <el-input
                          v-model="queryParams.bdid"
                          placeholder="bdid"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="htid" prop="htid">
                      <el-input
                          v-model="queryParams.htid"
                          placeholder="htid"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="询价，竞价表id，合并明细使用" prop="zxid">
                      <el-input
                          v-model="queryParams.zxid"
                          placeholder="询价，竞价表id，合并明细使用"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon><Search /></template>
            搜索
          </el-button>
          <el-button @click="handleResetQuery">
            <template #icon><Refresh /></template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="列表">
        <div>
          <el-button
              v-hasPerm="['cg:zxcgMxzx:add']"
              type="success"
              plain
              size="small"
              @click="formType.view = false; handleEdit()"
          >
            <template #icon><Plus /></template>
            新增
          </el-button>
          <el-button
              v-hasPerm="['cg:zxcgMxzx:delete']"
              type="danger"
              plain
              size="small"
              :disabled="removeIds.length === 0"
              @click="handleDelete()"
          >
            <template #icon><Delete /></template>
            删除
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
          ref="dataTableRef"
          v-loading="loading"
          :data="pageData"
          highlight-current-row
          border
          @selection-change="handleSelectionChange"
          @expand-change="handleExpandChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed />
        <el-table-column label="展开" type="expand" width="55">
          <template #default="props">
            <div style="padding: 0px 70px; background-color: #ffffff">
              <!-- <TransferPropertiesList
                :dcbm="props.row.dcbm"
                :guid="props.row.guid || ''"
              ></TransferPropertiesList> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
                <el-table-column
                    key="id"
                    label="主键，自增"
                    prop="id"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="mxid"
                    label="申请明细id"
                    prop="mxid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="guid"
                    label="guid"
                    prop="guid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="parentguid"
                    label="主表GUID"
                    prop="parentguid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sqid"
                    label="申请id"
                    prop="sqid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="type"
                    label="$fieldConfig.fieldComment"
                    prop="type"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysmx"
                    label="外键，自增 默认0"
                    prop="ysmx"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="mxbh"
                    label="明细编号"
                    prop="mxbh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="wpmc"
                    label="物品名称"
                    prop="wpmc"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cgml"
                    label="采购目录"
                    prop="cgml"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ppcs"
                    label="品牌厂商"
                    prop="ppcs"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xinghao"
                    label="型号"
                    prop="xinghao"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="gg"
                    label="规格"
                    prop="gg"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xxcs"
                    label="详细参数"
                    prop="xxcs"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ists"
                    label="是否特殊 进口、辐射、其它"
                    prop="ists"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="dw"
                    label="单位"
                    prop="dw"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sl"
                    label="数量"
                    prop="sl"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="price"
                    label="单价（元）"
                    prop="price"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysje"
                    label="金额（元）"
                    prop="ysje"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="time1"
                    label="开始时间"
                    prop="time1"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="time2"
                    label="结束时间"
                    prop="time2"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="notes"
                    label="其他说明"
                    prop="notes"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="gys"
                    label="供应商"
                    prop="gys"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="jgdm"
                    label="组织机构代码"
                    prop="jgdm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="gysfr"
                    label="法人"
                    prop="gysfr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="frsfz"
                    label="法人身份证"
                    prop="frsfz"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="gyslxr"
                    label="供应商联系人"
                    prop="gyslxr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="gystel"
                    label="联系电话"
                    prop="gystel"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="khyh"
                    label="资产类型（0资产，1非资产）"
                    prop="khyh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="khzh"
                    label="开户帐号"
                    prop="khzh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xsl"
                    label="采购数量"
                    prop="xsl"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cjjg"
                    label="成交价格(元)"
                    prop="cjjg"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="isba"
                    label="是否备案（0 否，1是）"
                    prop="isba"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="rwid"
                    label="rwid"
                    prop="rwid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="wtid"
                    label="wtid"
                    prop="wtid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="wmwtid"
                    label="wmwtid"
                    prop="wmwtid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="wjid"
                    label="wjid"
                    prop="wjid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="baid"
                    label="baid"
                    prop="baid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="bdid"
                    label="bdid"
                    prop="bdid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="htid"
                    label="htid"
                    prop="htid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="mxzt"
                    label="明细状态 0执行，1完成"
                    prop="mxzt"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="yt"
                    label="$fieldConfig.fieldComment"
                    prop="yt"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="syr"
                    label="使用人"
                    prop="syr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cfdd"
                    label="存放地点"
                    prop="cfdd"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xysl"
                    label="$fieldConfig.fieldComment"
                    prop="xysl"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sgly"
                    label="申购理由"
                    prop="sgly"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cjprice"
                    label="$fieldConfig.fieldComment"
                    prop="cjprice"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sfjk"
                    label="是否进口"
                    prop="sfjk"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysbm"
                    label="预算编码"
                    prop="ysbm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cfmx"
                    label="拆分明细"
                    prop="cfmx"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="bCode"
                    label="费用项"
                    prop="bCode"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="buCode"
                    label="预算项"
                    prop="buCode"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="mxordbh"
                    label="明细订单编号（冻结解冻）"
                    prop="mxordbh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zcyid"
                    label="关联政采云明细id"
                    prop="zcyid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ysjf"
                    label="$fieldConfig.fieldComment"
                    prop="ysjf"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="bazt"
                    label="明细备案状态：1备案，0未备案。采购备案修改明细即为备案"
                    prop="bazt"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zxid"
                    label="询价，竞价表id，合并明细使用"
                    prop="zxid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zcyddbh"
                    label="$fieldConfig.fieldComment"
                    prop="zcyddbh"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zcysbdz"
                    label="$fieldConfig.fieldComment"
                    prop="zcysbdz"
                    min-width="150"
                    align="center"
                />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button
                v-hasPerm="['cg:zxcgMxzx:edit']"
                type="primary"
                size="small"
                icon="Edit"
                link
                @click="formType.view = false;handleRowEdit(scope.row.id)"
            >
              编辑
            </el-button>
            <el-button
                v-hasPerm="['cg:zxcgMxzx:delete']"
                type="danger"
                size="small"
                icon="Delete"
                link
                @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
            <el-button
              v-if="check"
              type="primary"
              icon="DocumentChecked"
              size="small"
              link
              @click="hancleRowCheck"
            >
              审核
            </el-button>
            <el-button
              v-if="view"
              type="success"
              icon="Document"
              size="small"
              link
              @click="hancleRowView"
            >
              查看
            </el-button>
            <el-button
              v-if="view"
              type="primary"
              icon="Printer"
              size="small"
              link
              @click="formType.view = true;hancleRowPrint"
            >
              打印
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="handleQuery()"
      />
    </el-card>

    <!-- ZxcgMxzx表单弹窗 -->   
    <el-drawer 
      v-model="dialog.visible" 
      :title="dialog.title" 
      append-to-body 
      size="75%"
      :before-close="handleCloseDialog"

    >
<el-card>
        <template #header>
          <Title name="XX信息">
            <div>
              <el-button
                v-if="!formType.view"
                type="primary"
                @click="handleSubmit()"
              >
                保存
              </el-button>
              <el-button v-if="!formType.view" type="danger" @click="handleSubmit()">
                提交
              </el-button>
            </div>
          </Title>
        </template>



      <el-form ref="dataFormRef" :model="formData" :rules="rules" label-width="100px"  :inline="true"
          :disabled="formType.view">
                <el-form-item label="主键，自增" prop="id">
                      <el-input
                          v-model="formData.id"
                          placeholder="主键，自增"
                      />
                </el-form-item>
                <el-form-item label="申请明细id" prop="mxid">
                      <el-input
                          v-model="formData.mxid"
                          placeholder="申请明细id"
                      />
                </el-form-item>
                <el-form-item label="guid" prop="guid">
                      <el-input
                          v-model="formData.guid"
                          placeholder="guid"
                      />
                </el-form-item>
                <el-form-item label="主表GUID" prop="parentguid">
                      <el-input
                          v-model="formData.parentguid"
                          placeholder="主表GUID"
                      />
                </el-form-item>
                <el-form-item label="申请id" prop="sqid">
                      <el-input
                          v-model="formData.sqid"
                          placeholder="申请id"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="type">
                      <el-input
                          v-model="formData.type"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="外键，自增 默认0" prop="ysmx">
                      <el-input
                          v-model="formData.ysmx"
                          placeholder="外键，自增 默认0"
                      />
                </el-form-item>
                <el-form-item label="明细编号" prop="mxbh">
                      <el-input
                          v-model="formData.mxbh"
                          placeholder="明细编号"
                      />
                </el-form-item>
                <el-form-item label="物品名称" prop="wpmc">
                      <el-input
                          v-model="formData.wpmc"
                          placeholder="物品名称"
                      />
                </el-form-item>
                <el-form-item label="采购目录" prop="cgml">
                      <el-input
                          v-model="formData.cgml"
                          placeholder="采购目录"
                      />
                </el-form-item>
                <el-form-item label="品牌厂商" prop="ppcs">
                      <el-input
                          v-model="formData.ppcs"
                          placeholder="品牌厂商"
                      />
                </el-form-item>
                <el-form-item label="型号" prop="xinghao">
                      <el-input
                          v-model="formData.xinghao"
                          placeholder="型号"
                      />
                </el-form-item>
                <el-form-item label="规格" prop="gg">
                      <el-input
                          v-model="formData.gg"
                          placeholder="规格"
                      />
                </el-form-item>
                <el-form-item label="详细参数" prop="xxcs">
                      <el-input
                          v-model="formData.xxcs"
                          placeholder="详细参数"
                      />
                </el-form-item>
                <el-form-item label="是否特殊 进口、辐射、其它" prop="ists">
                      <el-input
                          v-model="formData.ists"
                          placeholder="是否特殊 进口、辐射、其它"
                      />
                </el-form-item>
                <el-form-item label="单位" prop="dw">
                      <el-input
                          v-model="formData.dw"
                          placeholder="单位"
                      />
                </el-form-item>
                <el-form-item label="数量" prop="sl">
                      <el-input
                          v-model="formData.sl"
                          placeholder="数量"
                      />
                </el-form-item>
                <el-form-item label="单价（元）" prop="price">
                      <el-input
                          v-model="formData.price"
                          placeholder="单价（元）"
                      />
                </el-form-item>
                <el-form-item label="金额（元）" prop="ysje">
                      <el-input
                          v-model="formData.ysje"
                          placeholder="金额（元）"
                      />
                </el-form-item>
                <el-form-item label="开始时间" prop="time1">
                      <el-input
                          v-model="formData.time1"
                          placeholder="开始时间"
                      />
                </el-form-item>
                <el-form-item label="结束时间" prop="time2">
                      <el-input
                          v-model="formData.time2"
                          placeholder="结束时间"
                      />
                </el-form-item>
                <el-form-item label="其他说明" prop="notes">
                      <el-input
                          v-model="formData.notes"
                          placeholder="其他说明"
                      />
                </el-form-item>
                <el-form-item label="供应商" prop="gys">
                      <el-input
                          v-model="formData.gys"
                          placeholder="供应商"
                      />
                </el-form-item>
                <el-form-item label="组织机构代码" prop="jgdm">
                      <el-input
                          v-model="formData.jgdm"
                          placeholder="组织机构代码"
                      />
                </el-form-item>
                <el-form-item label="法人" prop="gysfr">
                      <el-input
                          v-model="formData.gysfr"
                          placeholder="法人"
                      />
                </el-form-item>
                <el-form-item label="法人身份证" prop="frsfz">
                      <el-input
                          v-model="formData.frsfz"
                          placeholder="法人身份证"
                      />
                </el-form-item>
                <el-form-item label="供应商联系人" prop="gyslxr">
                      <el-input
                          v-model="formData.gyslxr"
                          placeholder="供应商联系人"
                      />
                </el-form-item>
                <el-form-item label="联系电话" prop="gystel">
                      <el-input
                          v-model="formData.gystel"
                          placeholder="联系电话"
                      />
                </el-form-item>
                <el-form-item label="资产类型（0资产，1非资产）" prop="khyh">
                      <el-input
                          v-model="formData.khyh"
                          placeholder="资产类型（0资产，1非资产）"
                      />
                </el-form-item>
                <el-form-item label="开户帐号" prop="khzh">
                      <el-input
                          v-model="formData.khzh"
                          placeholder="开户帐号"
                      />
                </el-form-item>
                <el-form-item label="采购数量" prop="xsl">
                      <el-input
                          v-model="formData.xsl"
                          placeholder="采购数量"
                      />
                </el-form-item>
                <el-form-item label="成交价格(元)" prop="cjjg">
                      <el-input
                          v-model="formData.cjjg"
                          placeholder="成交价格(元)"
                      />
                </el-form-item>
                <el-form-item label="是否备案（0 否，1是）" prop="isba">
                      <el-input
                          v-model="formData.isba"
                          placeholder="是否备案（0 否，1是）"
                      />
                </el-form-item>
                <el-form-item label="rwid" prop="rwid">
                      <el-input
                          v-model="formData.rwid"
                          placeholder="rwid"
                      />
                </el-form-item>
                <el-form-item label="wtid" prop="wtid">
                      <el-input
                          v-model="formData.wtid"
                          placeholder="wtid"
                      />
                </el-form-item>
                <el-form-item label="wmwtid" prop="wmwtid">
                      <el-input
                          v-model="formData.wmwtid"
                          placeholder="wmwtid"
                      />
                </el-form-item>
                <el-form-item label="wjid" prop="wjid">
                      <el-input
                          v-model="formData.wjid"
                          placeholder="wjid"
                      />
                </el-form-item>
                <el-form-item label="baid" prop="baid">
                      <el-input
                          v-model="formData.baid"
                          placeholder="baid"
                      />
                </el-form-item>
                <el-form-item label="bdid" prop="bdid">
                      <el-input
                          v-model="formData.bdid"
                          placeholder="bdid"
                      />
                </el-form-item>
                <el-form-item label="htid" prop="htid">
                      <el-input
                          v-model="formData.htid"
                          placeholder="htid"
                      />
                </el-form-item>
                <el-form-item label="明细状态 0执行，1完成" prop="mxzt">
                      <el-input
                          v-model="formData.mxzt"
                          placeholder="明细状态 0执行，1完成"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="yt">
                      <el-input
                          v-model="formData.yt"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="使用人" prop="syr">
                      <el-input
                          v-model="formData.syr"
                          placeholder="使用人"
                      />
                </el-form-item>
                <el-form-item label="存放地点" prop="cfdd">
                      <el-input
                          v-model="formData.cfdd"
                          placeholder="存放地点"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="xysl">
                      <el-input
                          v-model="formData.xysl"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="申购理由" prop="sgly">
                      <el-input
                          v-model="formData.sgly"
                          placeholder="申购理由"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="cjprice">
                      <el-input
                          v-model="formData.cjprice"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="是否进口" prop="sfjk">
                      <el-input
                          v-model="formData.sfjk"
                          placeholder="是否进口"
                      />
                </el-form-item>
                <el-form-item label="预算编码" prop="ysbm">
                      <el-input
                          v-model="formData.ysbm"
                          placeholder="预算编码"
                      />
                </el-form-item>
                <el-form-item label="拆分明细" prop="cfmx">
                      <el-input
                          v-model="formData.cfmx"
                          placeholder="拆分明细"
                      />
                </el-form-item>
                <el-form-item label="费用项" prop="bCode">
                      <el-input
                          v-model="formData.bCode"
                          placeholder="费用项"
                      />
                </el-form-item>
                <el-form-item label="预算项" prop="buCode">
                      <el-input
                          v-model="formData.buCode"
                          placeholder="预算项"
                      />
                </el-form-item>
                <el-form-item label="明细订单编号（冻结解冻）" prop="mxordbh">
                      <el-input
                          v-model="formData.mxordbh"
                          placeholder="明细订单编号（冻结解冻）"
                      />
                </el-form-item>
                <el-form-item label="关联政采云明细id" prop="zcyid">
                      <el-input
                          v-model="formData.zcyid"
                          placeholder="关联政采云明细id"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="ysjf">
                      <el-input
                          v-model="formData.ysjf"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="明细备案状态：1备案，0未备案。采购备案修改明细即为备案" prop="bazt">
                      <el-input
                          v-model="formData.bazt"
                          placeholder="明细备案状态：1备案，0未备案。采购备案修改明细即为备案"
                      />
                </el-form-item>
                <el-form-item label="询价，竞价表id，合并明细使用" prop="zxid">
                      <el-input
                          v-model="formData.zxid"
                          placeholder="询价，竞价表id，合并明细使用"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="zcyddbh">
                      <el-input
                          v-model="formData.zcyddbh"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="zcysbdz">
                      <el-input
                          v-model="formData.zcysbdz"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
      </el-form>
      <template #footer></template>
     </el-card>
    </ el-drawer>
  </div>
</template>

<script setup lang="ts">
  defineOptions({
    name: "ZxcgMxzx",
    inheritAttrs: false,
  });
  import { useUserStore } from "@/store";
  import ZxcgMxzxAPI, { ZxcgMxzxPageVO, ZxcgMxzxForm, ZxcgMxzxPageQuery } from "@/api/cg/zxcg-mxzx";

  // 获取路由参数,view/add/check
  const route = useRoute();
  const type = ref(route.query.type?.toString() || "view");
  //显示隐藏权限,用于v-if
  const add = type.value == "add";
  const view = type.value == "view";
  const check = type.value == "check";
  //页面参数
  const queryFormRef = ref(ElForm);
  const loading = ref(false);
  const removeIds = ref<number[]>([]);
  const total = ref(0);

  const queryParams = reactive<ZxcgMxzxPageQuery>({
    pageNum: 1,
    pageSize: 10,
  });

  // ZxcgMxzx表格数据
  const pageData = ref<ZxcgMxzxPageVO[]>([]);

  // 弹窗
  const dialog = reactive({
    title: "",
    visible: false,
  });



  /** 查询ZxcgMxzx */
  function handleQuery() {
    loading.value = true;
          ZxcgMxzxAPI.getPage(queryParams)
        .then((data) => {
          pageData.value = data.list;
          total.value = data.total;
        })
        .finally(() => {
          loading.value = false;
        });
  }

  /** 重置ZxcgMxzx查询 */
  function handleResetQuery() {
    queryFormRef.value!.resetFields();
    queryParams.pageNum = 1;
    handleQuery();
  }

  /** 行复选框选中记录选中ID集合 */
  function handleSelectionChange(selection: any) {
    removeIds.value = selection.map((item: any) => item.id);
  }

  /** 打开ZxcgMxzx弹窗 */
  function handleEdit(id?: number) {
    dialog.visible = true;
    if (id) {
      dialog.title = "修改ZxcgMxzx";
            ZxcgMxzxAPI.getFormData(id).then((data) => {
        Object.assign(formData, data);
      });
    } else {
      dialog.title = "新增ZxcgMxzx";
    }
  }

 

  /** 关闭ZxcgMxzx弹窗 */
  function handleCloseDialog() {
    dialog.visible = false;
    dataFormRef.value.resetFields();
    dataFormRef.value.clearValidate();
    Object.assign(formData, {});

  }

  /** 删除ZxcgMxzx */
  function handleDelete(id?: number) {
    const ids = [id || removeIds.value].join(",");
    if (!ids) {
      ElMessage.warning("请勾选删除项");
      return;
    }

    ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(
        () => {
          loading.value = true;
                ZxcgMxzxAPI.deleteByIds(ids)
              .then(() => {
                ElMessage.success("删除成功");
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        },
        () => {
          ElMessage.info("已取消删除");
        }
    );
  }

  onMounted(() => {
    handleQuery();
  });

  //--------------以下是form表单相关
const dataFormRef = ref(ElForm);

  // ZxcgMxzx表单数据
  const formData = reactive<ZxcgMxzxForm>({});

const formType = reactive({
  add: false,
  edit: false,
  view: false,
});

  // ZxcgMxzx表单校验规则
  const rules = reactive({
                      mxbh: [{ required: true, message: "请输入明细编号", trigger: "blur" }],
                      wpmc: [{ required: true, message: "请输入物品名称", trigger: "blur" }],
                      ysje: [{ required: true, message: "请输入金额（元）", trigger: "blur" }],
  });

   /** 提交ZxcgMxzx表单 */
  function handleSubmit() {
    dataFormRef.value.validate((valid: any) => {
      if (valid) {
        loading.value = true;
        const id = formData.id;
        if (id) {
                ZxcgMxzxAPI.update(id, formData)
              .then(() => {
                ElMessage.success("修改成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        } else {
                ZxcgMxzxAPI.add(formData)
              .then(() => {
                ElMessage.success("新增成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        }
      }
    });
  }
</script>
<style lang="scss" scoped>

</style>
