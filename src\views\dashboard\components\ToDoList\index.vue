<template>
  <div>
    <el-card>
      <template #header>
        <div class="flex-x-between">
          <div class="flex-y-center">待办工作</div>
          <el-button type="primary" size="small" plain @click="handleMoreToDoList">更多</el-button>
        </div>
      </template>

      <ScrollbarLoop :height="props.height" :datacount="pageData.groupToDoList.length">
        <el-table :data="pageData.groupToDoList" style="width: 100%" :show-header="false">
          <el-table-column prop="SXMC" />
          <el-table-column prop="CMUN" width="100" />
          <el-table-column fixed="right" width="100">
            <template #default="scope">
              <el-button type="primary" size="small" @click="hancleGoToDo(scope.row.PATH)">
                办理
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </ScrollbarLoop>
    </el-card>

    <!-- 弹出框 -->
    <el-dialog v-model="pageData.dv" title="待办事项清单" width="60%" height="60%">
      <el-card>
        <!-- ********************** 用插槽显示筛选框 ********************** -->
        <template #header>
          <el-form :model="params" :inline="true">
            <el-form-item props="SXMC" label="事项名称">
              <el-input v-model="params.sxmc" placeholder="请输入事项名称" clearable />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="search" @click="handleMoreToDoList">查询</el-button>
            </el-form-item>
          </el-form>
        </template>

        <el-table
          v-loading="pageData.loading"
          :data="pageData.toDoList"
          highlight-current-row
          :border="true"
          height="330px"
        >
          <el-table-column label="序号" type="index" width="80" align="center">
            <template #default="scope">
              <span>{{ (params.pageNum - 1) * params.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="SJMC" label="事件名称" />
          <el-table-column prop="SXMC" label="事项名称" />
          <el-table-column prop="FSSJ" label="创建时间" width="150" />
          <el-table-column label="操作" fixed="right" width="150">
            <template #default="scope">
              <el-button type="primary" size="small" @click="hancleGoToDo(scope.row.PATH)">
                办理
              </el-button>
              <el-button type="primary" size="small" @click="hancleDelToDo(scope.row.id)">
                清理
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- ********************** 翻页 ********************** -->
        <pagination
          v-if="pageData.total > 0"
          v-model:total="pageData.total"
          v-model:page="params.pageNum"
          v-model:limit="params.pageSize"
          @pagination="handleMoreToDoList"
        />
      </el-card>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import toDoListAPI from "@/api/properties/todolist";
import router from "@/router";
/*import { RecycleScroller } from 'vue-virtual-scroller'

export default {
  components: { RecycleScroller },
  computed: {
    visibleData() {
      // 返回当前可见区域的数据
    }
  }
}*/
//组件参数
const props = defineProps({
  guid: {
    type: String,
    required: true,
  },
  //模块高度，默认200
  height: {
    type: String,
    default: "200",
  },
});

const pageData = reactive({
  //页面列表
  groupToDoList: <any[]>[],
  //弹出框列表
  toDoList: <any[]>[],
  loading: false,
  //弹出框显隐
  dv: false,
  total: 0,
});

//更多按钮，查询待办列表
const params = reactive({
  pageNum: 1,
  pageSize: 10,
  sxmc: "",
  ucode: "",
});
//更多
const handleMoreToDoList = () => {
  toDoListAPI.getToDoList(params).then((res: any) => {
    pageData.toDoList = [...res.list];
    pageData.total = res.total;
    pageData.dv = true;
  });
};
//清除待办
const hancleDelToDo = (id: string) => {
  toDoListAPI.delToDoById({ id: id }).then(() => {
    ElMessage.success("清理成功");
  });
};
//待办跳转
const hancleGoToDo = (path: any) => {
  console.log(path);
  router.push(path);
};

onMounted(() => {
  //查询待办组合
  toDoListAPI.getGroupToDoList().then((res: any) => {
    pageData.groupToDoList.push(...res);

    // pageData.groupToDoList.push(...res);
    //console.log("条数");
    //console.log(res);
  });
});
</script>
<style lang="scss" scoped></style>
