<template>
  <div class="mobile-profile">
    <!-- 用户信息头部 -->
    <div class="profile-header">
      <div class="user-card">
        <div class="avatar-section">
          <div class="avatar-wrapper">
            <img :src="userProfile.avatar || defaultAvatar" alt="头像" class="avatar" />
            <div class="camera-icon" @click="handleAvatarClick">
              <svg-icon icon-class="camera" size="16px" />
            </div>
          </div>
        </div>
        <div class="user-info">
          <div class="nickname">{{ userProfile.nickname }}</div>
          <div class="username">{{ userProfile.username }}</div>
        </div>
        <div class="edit-icon" @click="handleEditProfile">
          <svg-icon icon-class="edit" size="16px" />
        </div>
      </div>
    </div>

    <!-- 功能菜单 -->
    <div class="menu-section">
      <!-- 账户信息 -->
      <div class="menu-group">
        <div class="menu-item" @click="handleMenuClick('account')">
          <div class="menu-icon">
            <svg-icon icon-class="user" size="20px" />
          </div>
          <div class="menu-content">
            <div class="menu-title">账户信息</div>
            <div class="menu-desc">个人资料、联系方式</div>
          </div>
          <div class="menu-arrow">
            <svg-icon icon-class="arrow-right" size="16px" />
          </div>
        </div>

        <div class="menu-item" @click="handleMenuClick('security')">
          <div class="menu-icon">
            <svg-icon icon-class="lock" size="20px" />
          </div>
          <div class="menu-content">
            <div class="menu-title">安全设置</div>
            <div class="menu-desc">密码修改、手机绑定</div>
          </div>
          <div class="menu-arrow">
            <svg-icon icon-class="arrow-right" size="16px" />
          </div>
        </div>
      </div>

      <!-- 应用设置 -->
      <div class="menu-group">
        <div class="menu-item" @click="handleMenuClick('notification')">
          <div class="menu-icon">
            <svg-icon icon-class="bell" size="20px" />
          </div>
          <div class="menu-content">
            <div class="menu-title">消息通知</div>
            <div class="menu-desc">推送设置</div>
          </div>
          <div class="menu-arrow">
            <svg-icon icon-class="arrow-right" size="16px" />
          </div>
        </div>

        <div class="menu-item" @click="handleMenuClick('theme')">
          <div class="menu-icon">
            <svg-icon icon-class="theme" size="20px" />
          </div>
          <div class="menu-content">
            <div class="menu-title">主题设置</div>
            <div class="menu-desc">{{ isDark ? '深色模式' : '浅色模式' }}</div>
          </div>
          <div class="menu-switch">
            <div class="switch" :class="{ active: isDark }" @click.stop="toggleTheme">
              <div class="switch-thumb"></div>
            </div>
          </div>
        </div>

        <div class="menu-item" @click="handleMenuClick('language')">
          <div class="menu-icon">
            <svg-icon icon-class="language" size="20px" />
          </div>
          <div class="menu-content">
            <div class="menu-title">语言设置</div>
            <div class="menu-desc">简体中文</div>
          </div>
          <div class="menu-arrow">
            <svg-icon icon-class="arrow-right" size="16px" />
          </div>
        </div>
      </div>

      <!-- 其他 -->
      <div class="menu-group">
        <div class="menu-item" @click="handleMenuClick('about')">
          <div class="menu-icon">
            <svg-icon icon-class="info" size="20px" />
          </div>
          <div class="menu-content">
            <div class="menu-title">关于应用</div>
            <div class="menu-desc">版本 {{ defaultSettings.version }}</div>
          </div>
          <div class="menu-arrow">
            <svg-icon icon-class="arrow-right" size="16px" />
          </div>
        </div>

        <div class="menu-item" @click="handleMenuClick('help')">
          <div class="menu-icon">
            <svg-icon icon-class="question" size="20px" />
          </div>
          <div class="menu-content">
            <div class="menu-title">帮助与反馈</div>
            <div class="menu-desc">使用帮助、问题反馈</div>
          </div>
          <div class="menu-arrow">
            <svg-icon icon-class="arrow-right" size="16px" />
          </div>
        </div>
      </div>
    </div>

    <!-- 退出登录 -->
    <div class="logout-section">
      <button class="logout-button" @click="handleLogout">
        退出登录
      </button>
    </div>

    <!-- 编辑资料弹窗 -->
    <div v-if="showEditDialog" class="edit-dialog-overlay" @click="closeEditDialog">
      <div class="edit-dialog" @click.stop>
        <div class="dialog-header">
          <div class="dialog-title">编辑资料</div>
          <div class="dialog-close" @click="closeEditDialog">
            <svg-icon icon-class="close" size="20px" />
          </div>
        </div>
        <div class="dialog-content">
          <div class="form-item">
            <label>昵称</label>
            <input v-model="editForm.nickname" type="text" placeholder="请输入昵称" />
          </div>
          <div class="form-item">
            <label>邮箱</label>
            <input v-model="editForm.email" type="email" placeholder="请输入邮箱" />
          </div>
          <div class="form-item">
            <label>手机号</label>
            <input v-model="editForm.mobile" type="tel" placeholder="请输入手机号" />
          </div>
        </div>
        <div class="dialog-footer">
          <button class="cancel-button" @click="closeEditDialog">取消</button>
          <button class="confirm-button" @click="handleSaveProfile">保存</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

import { useUserStore, useSettingsStore } from '@/store'
import UserAPI from '@/api/system/user'
import defaultSettings from '@/settings'
import { ThemeEnum } from '@/enums/ThemeEnum'

defineOptions({
  name: 'MobileProfile',
})

const router = useRouter()
const userStore = useUserStore()
const settingsStore = useSettingsStore()

const defaultAvatar = ref(new URL("../../../assets/logo.png", import.meta.url).href)
const showEditDialog = ref(false)

const userProfile = ref({
  avatar: '',
  nickname: '',
  username: '',
  email: '',
  mobile: '',
  deptName: '',
  roleNames: ''
})

const editForm = reactive({
  nickname: '',
  email: '',
  mobile: ''
})

// 是否为深色模式
const isDark = computed(() => settingsStore.theme === ThemeEnum.DARK)

// 处理头像点击
const handleAvatarClick = () => {
  ElMessage.info('头像上传功能开发中')
}

// 处理编辑资料
const handleEditProfile = () => {
  editForm.nickname = userProfile.value.nickname
  editForm.email = userProfile.value.email
  editForm.mobile = userProfile.value.mobile
  showEditDialog.value = true
}

// 关闭编辑弹窗
const closeEditDialog = () => {
  showEditDialog.value = false
}

// 保存资料
const handleSaveProfile = async () => {
  try {
    await UserAPI.updateProfile(editForm)
    ElMessage.success('资料更新成功')
    closeEditDialog()
    loadUserProfile()
  } catch (error) {
    ElMessage.error('资料更新失败')
  }
}

// 切换主题
const toggleTheme = () => {
  const newTheme = isDark.value ? ThemeEnum.LIGHT : ThemeEnum.DARK
  settingsStore.changeSetting({ key: 'theme', value: newTheme })
}

// 处理菜单点击
const handleMenuClick = (type: string) => {
  switch (type) {
    case 'account':
      ElMessage.info('账户信息页面开发中')
      break
    case 'security':
      ElMessage.info('安全设置页面开发中')
      break
    case 'notification':
      ElMessage.info('消息通知设置开发中')
      break
    case 'language':
      ElMessage.info('语言设置开发中')
      break
    case 'about':
      ElMessage.info('关于应用页面开发中')
      break
    case 'help':
      ElMessage.info('帮助与反馈页面开发中')
      break
  }
}

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await userStore.logout()
    ElMessage.success('已退出登录')
    router.push('/mobile/login')
  } catch (error) {
    // 用户取消
  }
}

// 加载用户资料
const loadUserProfile = async () => {
  try {
    const data = await UserAPI.getUserProfile()
    userProfile.value = data
  } catch (error) {
    console.error('加载用户资料失败:', error)
  }
}

onMounted(() => {
  loadUserProfile()
})
</script>

<style lang="scss" scoped>
.mobile-profile {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 16px 30px;

  .user-card {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);

    .avatar-section {
      margin-right: 16px;

      .avatar-wrapper {
        position: relative;
        width: 60px;
        height: 60px;

        .avatar {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          object-fit: cover;
        }

        .camera-icon {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 20px;
          height: 20px;
          background: var(--el-color-primary);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          cursor: pointer;
        }
      }
    }

    .user-info {
      flex: 1;
      color: white;

      .nickname {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 4px;
      }

      .username {
        font-size: 14px;
        opacity: 0.8;
      }
    }

    .edit-icon {
      color: white;
      cursor: pointer;
      padding: 8px;
    }
  }
}

.menu-section {
  padding: 16px;
}

.menu-group {
  background: white;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f5f5f5;
  }

  .menu-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f7ff;
    border-radius: 8px;
    margin-right: 12px;
    color: var(--el-color-primary);
  }

  .menu-content {
    flex: 1;

    .menu-title {
      font-size: 16px;
      color: #333;
      margin-bottom: 2px;
    }

    .menu-desc {
      font-size: 12px;
      color: #999;
    }
  }

  .menu-arrow {
    color: #ccc;
  }

  .menu-switch {
    .switch {
      width: 44px;
      height: 24px;
      background: #ddd;
      border-radius: 12px;
      position: relative;
      cursor: pointer;
      transition: background-color 0.2s;

      &.active {
        background: var(--el-color-primary);
      }

      .switch-thumb {
        width: 20px;
        height: 20px;
        background: white;
        border-radius: 50%;
        position: absolute;
        top: 2px;
        left: 2px;
        transition: transform 0.2s;
      }

      &.active .switch-thumb {
        transform: translateX(20px);
      }
    }
  }
}

.logout-section {
  padding: 16px;

  .logout-button {
    width: 100%;
    height: 48px;
    background: #ff4d4f;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:active {
      background: #ff7875;
    }
  }
}

.edit-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.edit-dialog {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;

    .dialog-title {
      font-size: 18px;
      font-weight: 500;
    }

    .dialog-close {
      cursor: pointer;
      color: #999;
    }
  }

  .dialog-content {
    padding: 20px;

    .form-item {
      margin-bottom: 20px;

      label {
        display: block;
        font-size: 14px;
        color: #333;
        margin-bottom: 8px;
      }

      input {
        width: 100%;
        height: 44px;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        padding: 0 12px;
        font-size: 16px;
        outline: none;

        &:focus {
          border-color: var(--el-color-primary);
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid #f0f0f0;

    button {
      flex: 1;
      height: 40px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      cursor: pointer;
    }

    .cancel-button {
      background: #f5f5f5;
      color: #666;
    }

    .confirm-button {
      background: var(--el-color-primary);
      color: white;
    }
  }
}
</style>
