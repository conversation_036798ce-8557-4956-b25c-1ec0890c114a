<!-- 领用单 -->
<template>
  <el-card>
    <!-- ********************** 用插槽显示筛选框 ********************** -->
    <template #header>
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
        <el-form-item label="资产信息" prop="code">
          <el-input v-model="queryParams.code" placeholder="请输入资产编号或资产编号" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon><Search /></template>
            搜索
          </el-button>
        </el-form-item>
      </el-form>
    </template>

    <Title name="资产清单">
      <el-button
        plain
        type="primary"
        icon="Pointer"
        :disabled="seletctIds.length === 0"
        @click="handleConfirm"
      >
        批量选择
      </el-button>
    </Title>
    <!-- ********************** 列表内容 ********************** -->
    <el-table
      v-loading="loading"
      stripe
      :data="pageData"
      highlight-current-row
      :border="true"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="50"
        align="center"
        fixed
        :selectable="(row: any) => row.locName == ''"
      />
      <el-table-column label="序号" type="index" width="55" align="center" fixed>
        <template #default="scope">
          <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="资产编号" prop="zcbh" width="120" />
      <el-table-column label="资产名称" prop="zcmc" width="200" />
      <el-table-column label="金额(元)" prop="je" width="100" />
      <el-table-column label="数量" prop="sl" min-width="80" />
      <el-table-column label="资产状态" prop="locName" width="100">
        <template #default="scope">
          <span>{{ scope.row.locName === "" ? "正常" : scope.row.locName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="使用人" prop="syrname" width="80" />
      <el-table-column label="使用部门" prop="sybmname" width="100" />

      <!-- ********************** 操作列 **********************
         按钮：查询View；编辑Edit；删除Delete -->
      <el-table-column label="操作" fixed="right" width="100">
        <template #default="scope">
          <el-button
            type="primary"
            icon="View"
            size="small"
            link
            @click="handleRowView(scope.row.guid)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- ********************** 翻页 ********************** -->
    <pagination
      v-if="total > 0"
      v-model:total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="handleQuery"
    />
  </el-card>

  <!-- 查看资产弹窗 -->
  <el-drawer v-model="itemVisible" append-to-body size="70%">
    <CardPanel :key="itemGuid" :guid="itemGuid" />
  </el-drawer>
</template>

<script setup lang="ts">
import bookApi from "@/api/properties/book";
import pubAPI from "@/api/properties/pub";
//————————————————————————————————————————————暴露的方法,和请求参数
//查询类型，新增【add】，编辑【edit】，查询【view】
const props = defineProps({
  handleReturnConfirm: {
    type: Function,
    required: true,
  },
  dcode: {
    type: String,
  },
  syr: {
    type: String,
  },
});
//——————————————————————————————————————————————————查询相关
const loading = ref(false);
const total = ref(0);
const pageData = ref<any[]>([]);
const queryParams = reactive<any>({
  pageNum: 1,
  pageSize: 12,
  code: "",
  type: "ly",
});
const handleQuery = () => {
  loading.value = true;
  pubAPI
    .getAdjustPage(queryParams)
    .then((res: any) => {
      pageData.value = res.list;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
};

//——————————————————————————————————————————————————操作相关
const seletctIds = ref<number[]>([]);
// 选中项发生变化
function handleSelectionChange(selection: any[]) {
  seletctIds.value = selection.map((item) => item.guid);
  console.log(seletctIds.value);
}
const handleConfirm = () => {
  props.handleReturnConfirm(seletctIds.value);
  handleQuery();
};

//——————————————————————————————————————————————————弹窗相关参数
const itemVisible = ref(false);
const itemGuid = ref("");
const handleRowView = (guid: string) => {
  itemGuid.value = guid;
  itemVisible.value = true;
};

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped></style>
