<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form :model="queryParams" :inline="true" label-width="auto">
          <el-form-item label="资源类型：" prop="resourceType">
            <el-radio-group v-model="queryParams.type" @change="handleRadioChange">
              <el-radio value="ly">领用</el-radio>
              <el-radio value="jy">借用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="资产信息：" prop="assetName">
            <el-input v-model="queryParams.code" placeholder="请输入资产名称或资产编号" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button type="success" @click="handleGoReceive">我的领用单</el-button>
            <el-button type="success" @click="handleGoLend">我的借用单</el-button>
          </el-form-item>
        </el-form>
      </template>

      <div v-loading="loading" class="grid-container">
        <div v-for="(asset, index) in pageData" :key="index" class="grid-item">
          <el-card class="asset-card">
            <div class="card-flex" @click="() => ((itemVisible = true), (itemGuid = asset.guid))">
              <el-image :src="img" alt="资产图片" style="width: 120px; height: 120px" />
              <div class="card-content">
                <div class="details">编号：{{ asset.zcbh }}</div>
                <div class="details">名称：{{ asset.zcmc }}</div>
                <div class="details">规格：{{ asset.ggxh }}</div>
                <div class="details">品牌：{{ asset.pp }}</div>
                <div class="details">购置时间：{{ format(asset.qdrq, "YYYY-MM-DD") }}</div>
              </div>
            </div>
            <template #footer>
              <div style="text-align: center">
                <!-- loc_name为空显示领用借用按钮 -->
                <el-button
                  v-if="asset.locName == ''"
                  type="danger"
                  icon="Plus"
                  @click="handleAdd(asset.guid, queryParams.type)"
                >
                  {{ queryParams.type == "ly" ? "加入领用" : "加入借用" }}
                </el-button>

                <el-button
                  v-if="asset.locName != ''"
                  icon="Search"
                  link
                  type="primary"
                  @click="handleViewDetail"
                >
                  {{ asset.by2 + "人:" + asset.djrname }}
                </el-button>

                <el-button
                  v-if="asset.by5 == '1'"
                  type="danger"
                  @click="handleRelease(asset.by3, asset.guid)"
                >
                  释放
                </el-button>
              </div>
            </template>
          </el-card>
        </div>
      </div>
      <pagination
        v-if="total > 12"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <el-drawer v-model="itemVisible" append-to-body size="70%">
      <CardPanel :key="itemGuid" :guid="itemGuid" />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { reactive } from "vue";
import pubAPI, { pubAdjustQuery } from "@/api/properties/pub";
import lendAPI from "@/api/properties/lend";
import receiveAPI from "@/api/properties/receive";

import img from "@/assets/images/back.png";
import { format } from "@/utils/day";
import { useRouter } from "vue-router";
const router = useRouter();

const total = ref(0);
const loading = ref(false);
const itemVisible = ref(false);
const itemGuid = ref("");
const pageData = ref<any[]>([]);
const route = useRoute();
const type = ref(route.query.type?.toString() || "jy");
const queryParams = reactive<pubAdjustQuery>({
  pageNum: 1,
  pageSize: 12,
  code: "",
  type: type.value,
});
const handleQuery = () => {
  loading.value = true;
  pubAPI
    .getAdjustPage(queryParams)
    .then((res) => {
      pageData.value = res.list;
      total.value = res.total;
    })
    .catch((error) => {
      console.error("数据获取失败:", error);
      ElMessage.error("数据获取失败");
      pageData.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
};

const handleRadioChange = () => {
  handleQuery();
};
const handleAdd = (guid: string, type?: string) => {
  ElMessageBox.confirm("确定要" + (type == "ly" ? "领用" : "借用") + "吗？", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      switch (type) {
        case "ly":
          receiveAPI
            .add(guid)
            .then(() => {
              ElMessageBox.confirm("加入领用成功，是否前往提交", {
                confirmButtonText: "确定",
                cancelButtonText: "取消，继续选择",
                type: "warning",
              }).then(() => {
                router.push({ path: "/use/receive/add", query: { type: "add" } });
              });
            })
            .catch(() => {})
            .finally(() => {});

          break;
        case "jy":
          lendAPI
            .add(guid)
            .then(() => {
              ElMessageBox.confirm("加入借用成功，是否前往提交", {
                confirmButtonText: "确定",
                cancelButtonText: "取消，继续选择",
                type: "warning",
              }).then(() => {
                router.push({ path: "/use/lend/add", query: { type: "add" } });
              });
            })
            .catch(() => {})
            .finally(() => {});
          break;
        default:
          break;
      }
    })
    .catch(() => {});
};

const handleGoReceive = () => {
  ElMessageBox.confirm("确定前往我的领用单页面？", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      router.push({ path: "/use/receive/add", query: { type: "add" } });
    })
    .catch(() => {});
};

const handleGoLend = () => {
  ElMessageBox.confirm("确定前往我的借用单页面？", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      router.push({ path: "/use/lend/add", query: { type: "add" } });
    })
    .catch(() => {});
};

const handleViewDetail = () => {
  //alert("123");
};

const handleRelease = (syguid: string, zcguid: string) => {
  ElMessageBox.confirm(
    "该操作会删除用户当前的领用单或借用单，使资产恢复至可被领用或借用的状态，确定要操作？",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      pubAPI.release(syguid, zcguid).then(() => {
        ElMessage.success("操作成功！");
      });
    })
    .catch(() => {});
};

onMounted(() => {
  handleQuery();
});
</script>

<style scoped>
.flex-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.filter-container {
  padding: 20px;
  background-color: #fff;
}

.grid-container {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 20px;
  padding: 20px;
}

.grid-item {
  width: 100%;
  height: 250px;
}

.asset-card {
  padding: 10px;
  height: 100%;
}

img {
  width: 150px;
  height: 150px;
  object-fit: cover;
  margin-right: 10px;
}

.card-content {
  flex: 1;
  padding: 0 10px;
}

.card-flex {
  display: flex;
  flex-direction: row;
}

.details {
  overflow: hidden;
  white-space: nowrap;
}
.el-card__footer {
  padding: 0px;
}
</style>
