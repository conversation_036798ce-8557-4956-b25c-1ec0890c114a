<template>
  <div>
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item label="资产编号" prop="zcbh">
            <el-input v-model="queryParams.zcbh" placeholder="资产编号" />
          </el-form-item>
          <el-form-item label="资产名称" prop="zcmc">
            <el-input v-model="queryParams.zcmc" placeholder="资产名称" />
          </el-form-item>
          <el-form-item label="使用部门" prop="sybm">
            <DDLDeptList v-model="queryParams.sybm" />
          </el-form-item>
          <el-form-item label="保管员" prop="syr">
            <DDLUserList
              :key="queryParams.sybm"
              v-model="queryParams.syr"
              :dcode="queryParams.sybm"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon><Search /></template>
              搜索
            </el-button>
            <!-- <el-button @click="handleResetQuery">
              <template #icon><Refresh /></template>
              重置
            </el-button>  -->
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="待处置资产列表">
        <div>
          <el-button
            v-if="add"
            :disabled="selectedValues.length === 0"
            type="success"
            plain
            @click="dialogVisible = true"
          >
            <template #icon><Plus /></template>
            处置申请
          </el-button>

          <el-button
            v-if="edit"
            :disabled="selectedValues.length === 0"
            type="danger"
            plain
            @click="handleEditDispose"
          >
            <template #icon><Plus /></template>
            选择
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        stripe
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          :selectable="(row: any) => row.locName == ''"
          width="55"
          align="center"
          fixed
        />
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="资产编号" prop="zcbh" width="130" align="center" fixed />
        <el-table-column label="资产名称" prop="zcmc" width="200" align="center" fixed />

        <el-table-column label="规格型号" prop="ggxh" width="150" align="center" />
        <el-table-column label="数量" prop="sl" width="55" align="center" />
        <el-table-column label="原值(元)" prop="je" width="100" align="center" />
        <el-table-column label="使用部门" prop="sybmname" width="150" align="center" />
        <el-table-column label="保管员" prop="syrname" width="100" align="center" />
        <el-table-column label="原值(元)" prop="je" width="100" align="center" />
        <el-table-column label="预计使用年限(月)" prop="yjsynx" width="100" align="center" />
        <el-table-column label="已使用年限(月)" prop="ljsyys" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.ljsyys != scope.row.ljsyys ? 'success' : 'danger'">
              {{ scope.row.ljsyys }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="保管员" prop="syrname" width="100" align="center" />
        <el-table-column label="使用部门" prop="sybmname" align="center" />
        <el-table-column label="资产状态" prop="locName" width="150">
          <template #default="scope">
            <span>{{ scope.row.locName === "" ? "正常" : scope.row.locName }}</span>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :page-size="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <el-dialog v-model="dialogVisible" width="40%" title="请选择处置方式">
      <el-form :model="disposeForm" style="margin: 30px">
        <el-form-item label="处置方式" prop="czlx">
          <DDLYcode v-model="addParams.czlx" ycode="0102" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAddDispose">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ElLoading } from "element-plus";
import disposeAPI, { addParam } from "@/api/properties/dispose";
import { useRouter } from "vue-router";
//————————————————————————————————————————————暴露的方法,和请求参数
const router = useRouter();
//页面参数
const queryFormRef = ref(ElForm);

const props = defineProps({
  handleReturnConfirm: {
    type: Function,
    required: true,
  },
  //两个枚举值，add/edit，add用于创建受理单时候的列表页面，edit用于编辑受理单时的添加明细
  type: {
    type: String,
    required: true,
  },
});
const add = props.type == "add";
const edit = props.type == "edit";
//——————————————————————————————————————————————————查询相关
//请求列表数据
const loading = ref(false);
const total = ref(0);
const pageData = ref<any[]>([]);
const queryParams = reactive<any>({
  pageNum: 1,
  pageSize: 20,
});
const handleQuery = () => {
  loading.value = true;
  console.log(queryParams);
  disposeAPI
    .getWaitDisposePage(queryParams)
    .then((res) => {
      pageData.value = res.list;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
};

//——————————————————————————————————————————————————操作相关
const selectedValues = ref<number[]>([]);
/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  selectedValues.value = selection.map((item: any) => item.guid);
}

//编辑受理单
const handleEditDispose = () => {
  // editClearParams.Disposeguids = selectedValues.value.join(",");
  // editClearParams.ClearanceGuid = props.guid;
  // console.log("editClearParams", editClearParams);
  // ElMessageBox.confirm("确认添加处置申请至受理单？", {
  //   confirmButtonText: "确定",
  //   cancelButtonText: "取消",
  //   type: "warning",
  // }).then(() => {
  //   clearAPI.addClearDetail(editClearParams).then(() => {
  //     ElMessage.success("操作成功");
  //     // 跳转到index页面并添加路由参数type为add
  //     // router.push({ path: "/dispose/edit", query: { type: "add" } });
  //     handleQuery();
  //   });
  // });
  props.handleReturnConfirm(selectedValues.value);
  handleQuery();
};

//——————————————————————————————————————————————————弹窗相关参数
const dialogVisible = ref(false);
const disposeForm = reactive({});
const addParams = reactive<addParam>({
  zcguid: "",
  czlx: "",
});
//创建处置单
const handleAddDispose = () => {
  const num = selectedValues.value.length;
  addParams.zcguid = selectedValues.value.join(",");
  console.log(addParams);
  ElMessageBox.confirm("已选" + num + "条资产，是否创建处置单？", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: "处理中",
    });

    disposeAPI
      .addDisposeByDetails(addParams)
      .then((res) => {
        if (res) {
          //res是新处置单的guid
          ElMessage.success("处置单创建成功，请前往编辑");
          // 跳转到index页面并添加路由参数type为add
          router.push({ path: "/dispose/edit", query: { type: "add" } });
        }
      })
      .finally(() => {
        loading.close();
      });
  });
};

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.el-form {
  width: auto;
}
</style>
