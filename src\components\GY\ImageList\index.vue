<template>
  <div style="margin: 10px">
    <Title name="图片列表" />
    <el-image
      v-for="url in urls"
      style="width: 300px; height: 300px"
      :src="url"
      :zoom-rate="1.2"
      :max-scale="7"
      :min-scale="0.2"
      :preview-src-list="previewList"
      :initial-index="4"
      fit="cover"
      @click="handleClick(url)"
    />
  </div>
</template>

<script lang="ts" setup>
import UniFjsAPI, { fjVo } from "@/api/system/uniFjs";

const props = defineProps({
  guid: {
    type: String,
    required: true,
  },
});

const pageData = ref<fjVo[]>([]);

//图片数据源
const urls = ref<string[]>([]);
// "https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg",
// "https://fuss10.elemecdn.com/1/34/19aa98b1fcb2781c4fba33d850549jpeg.jpeg",
// "https://fuss10.elemecdn.com/0/6f/e35ff375812e6b0020b6b4e8f9583jpeg.jpeg",
// "https://fuss10.elemecdn.com/9/bb/e27858e973f5d7d3904835f46abbdjpeg.jpeg",
// "https://fuss10.elemecdn.com/d/e6/c4d93a3805b3ce3f323f7974e6f78jpeg.jpeg",
// "https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg",
// "https://fuss10.elemecdn.com/2/11/6535bcfb26e4c79b48ddde44f4b6fjpeg.jpeg",
//预览数据源
const previewList = ref<string[]>([]);

const handleClick = (url: string) => {
  previewList.value = [url];
};

onMounted(() => {
  UniFjsAPI.getFjList({
    guid: props.guid,
    fjcode: "",
  }).then((res) => {
    res.list.forEach((item) => {
      if (".jpeg,jpg,.png,.gif,.bmp".includes(item.filekide)) urls.value.push(item.url);
    });
  });
});
</script>

<style scoped>
.el-image {
  margin: 10px;
}
</style>
