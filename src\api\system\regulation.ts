import request from "@/utils/request";

/**
 * 规章制度分页查询参数
 */
export interface RegulationPageQuery {
  /** 当前页码 */
  pageNum: number;
  /** 每页显示条数 */
  pageSize: number;
  /** 标题 */
  title?: string;
  /** 类型 */
  type?: string;
  /** 发布状态 */
  publishStatus?: number;
}

/**
 * 规章制度分页视图对象
 */
export interface RegulationPageVO {
  /** 规章制度ID */
  id: number;
  /** 标题 */
  title: string;
  /** 内容 */
  content: string;
  /** 类型 */
  type: string;
  /** 发布状态(0-未发布, 1-已发布, -1-已撤回) */
  publishStatus: number;
  /** 发布人ID */
  publisherId: number;
  /** 发布人名称 */
  publisherName: string;
  /** 创建时间 */
  createTime: string;
  /** 发布时间 */
  publishTime: string;
  /** 撤回时间 */
  revokeTime: string;
  /** 附件GUID */
  fileGuid: string;
}

/**
 * 规章制度表单对象
 */
export interface RegulationForm {
  /** 规章制度ID */
  id?: number;
  /** 标题 */
  title: string;
  /** 内容 */
  content: string;
  /** 类型 */
  type: string;
  /** 附件GUID */
  fileGuid?: string;
}

/**
 * 规章制度API
 */
const RegulationAPI = {
  /**
   * 获取规章制度分页列表
   *
   * @param queryParams 查询参数
   * @returns 规章制度分页列表
   */
  getPage(queryParams: RegulationPageQuery) {
    return request<any, PageResult<RegulationPageVO>>({
      url: "/api/v1/regulations/page",
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 获取规章制度表单数据
   *
   * @param id 规章制度ID
   * @returns 规章制度表单数据
   */
  getFormData(id: number) {
    return request<any, RegulationForm>({
      url: `/api/v1/regulations/${id}/form`,
      method: "get",
    });
  },

  /**
   * 添加规章制度
   *
   * @param data 规章制度表单数据
   * @returns 响应结果
   */
  add(data: RegulationForm) {
    return request({
      url: "/api/v1/regulations",
      method: "post",
      data: data,
    });
  },

  /**
   * 修改规章制度
   *
   * @param id 规章制度ID
   * @param data 规章制度表单数据
   * @returns 响应结果
   */
  update(id: number, data: RegulationForm) {
    return request({
      url: `/api/v1/regulations/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 删除规章制度
   *
   * @param ids 规章制度ID，多个以逗号分隔
   * @returns 响应结果
   */
  deleteByIds(ids: string) {
    return request({
      url: `/api/v1/regulations/${ids}`,
      method: "delete",
    });
  },

  /**
   * 发布规章制度
   *
   * @param id 规章制度ID
   * @returns 响应结果
   */
  publish(id: number) {
    return request({
      url: `/api/v1/regulations/${id}/publish`,
      method: "put",
    });
  },

  /**
   * 撤回规章制度
   *
   * @param id 规章制度ID
   * @returns 响应结果
   */
  revoke(id: number) {
    return request({
      url: `/api/v1/regulations/${id}/revoke`,
      method: "put",
    });
  },

  /**
   * 获取规章制度详情
   *
   * @param id 规章制度ID
   * @returns 规章制度详情
   */
  getDetail(id: number) {
    return request<any, RegulationPageVO>({
      url: `/api/v1/regulations/${id}`,
      method: "get",
    });
  },
};

export default RegulationAPI;
