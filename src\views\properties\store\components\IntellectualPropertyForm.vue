<template>
  <!-- 知识产权 -->
  <el-form
    :model="formData"
    :rules="rules"
    ref="formRef"
    label-width="120px"
    :inline="true"
    :disabled="!props.editable"
  >
    <el-form-item label="采购申请人" prop="cgsqr" style="width: 100%">
      <table-select
        v-model="formData.cgsqr"
        :text="text"
        :select-config="selectConfig"
        @confirm-click="handleConfirm"
      ></table-select>
    </el-form-item>

    <el-form-item label="采购组织形式">
      <DDLXcode v-model="formData.cgzzxs" xcode="020202" clearable />
    </el-form-item>

    <el-form-item label="采购申请日期" prop="cgsqsj">
      <el-date-picker
        v-model="formData.cgsqsj"
        type="date"
        value-format="YYYY-MM-DD HH:mm:ss"
        placeholder="请选择采购申请日期"
      />
    </el-form-item>

    <el-form-item label="采购方式">
      <DDLXcode v-model="formData.by3" xcode="02024A" clearable />
    </el-form-item>

    <el-form-item label="供应商负责人">
      <el-input v-model="formData.by4" placeholder="请输入供应商负责人" />
    </el-form-item>

    <el-form-item label="供应商名称" prop="gysmc">
      <el-input v-model="formData.gysmc" placeholder="请输入供应商名称" />
    </el-form-item>

    <el-form-item label="使用方向">
      <DDLXcode v-model="formData.syfx" xcode="020204" clearable />
    </el-form-item>

    <el-form-item label="合同编号" prop="htbh">
      <el-input v-model="formData.htbh" placeholder="请输入合同编号" />
    </el-form-item>

    <el-form-item label="注册登记机关">
      <el-input v-model="formData.zcdjjg" placeholder="请输入注册登记机关" />
    </el-form-item>

    <el-form-item label="注册登记时间">
      <el-date-picker
        v-model="formData.zcdjsj"
        type="date"
        value-format="YYYY-MM-DD HH:mm:ss"
        placeholder="请选择注册登记时间"
      />
    </el-form-item>

    <el-form-item label="产权拥有人">
      <el-input v-model="formData.fmr" placeholder="请输入产权拥有人" />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import storeAPI, { ZcinfoDifferentForm } from "@/api/properties/store";
import { ElLoading } from "element-plus";
import selectConfig from "./config/select";
import UserAPI from "@/api/system/user";
// 组件属性定义
const props = defineProps({
  guid: {
    type: String,
  },
  zclxbh: {
    type: String,
  },
  editable: {
    type: Boolean,
    required: true,
  },
});
// 表单数据
const formData = reactive<ZcinfoDifferentForm>({
  zclxbh: props.zclxbh || "",
});

// 表单验证规则
const rules = reactive({
  cgsqr: [{ required: true, message: "请选择采购申请人", trigger: "blur" }],
  cgsqsj: [{ required: true, message: "请选择采购申请日期", trigger: "change" }],
  gysmc: [{ required: true, message: "请输入供应商名称", trigger: "blur" }],
  htbh: [{ required: true, message: "请输入合同编号", trigger: "blur" }],
});

// 表单引用
const formRef = ref(ElForm);
// 下拉框选项
const purchaseOrgOptions = ref([
  { value: "1", label: "集中采购" },
  { value: "2", label: "分散采购" },
  { value: "3", label: "自行采购" },
]);

const purchaseMethodOptions = ref([
  { value: "1", label: "公开招标" },
  { value: "2", label: "邀请招标" },
  { value: "3", label: "竞争性谈判" },
  { value: "4", label: "单一来源采购" },
]);

const useDirectionOptions = ref([
  { value: "1", label: "办公使用" },
  { value: "2", label: "生产使用" },
  { value: "3", label: "研发使用" },
  { value: "4", label: "其他" },
]);

// 监听值变化
watch(
  () => [props.guid, props.zclxbh],
  (val) => {
    console.log("props:", val);
    if (val && val[0] != "" && val[1] != undefined) {
      console.log("props:", val, val[0], val[1]);
      //获取表单数据
      storeAPI.getFormDataDiff({ guid: val[0], zclxbh: val[1] }).then((res) => {
        Object.assign(formData, res);
      });
      formData.zclxbh = val[1];
      formData.guid = val[0];
    }
  },
  { deep: true, immediate: true }
);
const selectedUser = ref<any>({});
function handleConfirm(data: any[]) {
  selectedUser.value = data[0];
  formData.cgsqr = selectedUser.value.username;
}
const text = computed(() => {
  console.log("text:", selectedUser.value);
  return selectedUser.value && selectedUser.value.nickname ? `${selectedUser.value.nickname}` : "";
});

watch(
  () => formData.cgsqr,
  (val) => {
    console.log("formData.cgsqr", val);
    if (val && val != "") {
      UserAPI.getFormDatabycode(val).then((res) => {
        console.log("User", res);
        selectedUser.value.nickname = res.nickname || "";
        selectedUser.value.username = res.username || "";
      });
    }
  }
);

// 字段禁用状态
const disabledFields = ref({
  cgsqrTB: false,
});

// 处理使用方向变更
const handleSyfxChange = (value) => {
  console.log("使用方向变更:", value);
};

// 选择用户
const selectUser = () => {
  console.log("打开用户选择弹窗");
  // 实际项目中实现用户选择逻辑
};

// 重置表单
const resetForm = () => {
  formRef.value.resetFields();
};

// 提交表单
const submitForm = () => {
  return new Promise((resolve, reject) => {
    formRef.value?.validate((valid) => {
      if (valid) {
        // 显示加载状态
        const loading = ElLoading.service({
          lock: true,
          text: "正在保存...",
          background: "rgba(0, 0, 0, 0.7)",
        });

        storeAPI
          .updateDiff(formData)
          .then((res) => {
            // 关闭加载状态
            loading.close();
            // 显示成功消息
            ElMessage({
              type: "success",
              message: "保存成功",
            });
            console.log("表单提交成功:", res);
            resolve(res);
          })
          .catch((error) => {
            // 关闭加载状态
            loading.close();
            // 显示错误消息
            ElMessage({
              type: "error",
              message: "保存失败: " + (error.message || "未知错误"),
            });
            console.error("表单提交失败:", error);
            reject(error);
          });
      } else {
        ElMessage({
          type: "warning",
          message: "请填写必填项",
        });
        reject(new Error("表单验证失败"));
        return false;
      }
    });
  });
};

// 表单验证方法
const validateForm = () => {
  return new Promise((resolve, reject) => {
    formRef.value?.validate((valid) => {
      if (valid) {
        resolve(true);
      } else {
        reject(new Error("表单验证失败"));
      }
    });
  });
};

// 导出方法供父组件使用
defineExpose({
  submitForm,
  resetForm,
  validate: validateForm,
});
</script>

<style scoped>
.el-form-item {
  width: 330px;
}
</style>
