<!-- cursor测试:1 -->
<template>
  <div>
    <Title name="领用单信息">
      <div v-if="editable">
        <el-button type="primary" @click="handleSave()">保存</el-button>
        <el-button type="danger" @click="handleSubmit()">提交</el-button>
      </div>
    </Title>
    <el-form
      ref="dataFormRef"
      :model="formData"
      :rules="rules"
      label-width="auto"
      :inline="true"
      :disabled="!editable"
    >
      <el-form-item label="领用单号" props="sqdh">
        <el-input v-model="formData.sqdh" :disabled="true" placeholder="系统自动生成" />
      </el-form-item>
      <el-form-item label="申请人" prop="djr">
        <DDLUserList
          :key="formData.djbmbm"
          v-model="formData.djr"
          :disabled="true"
          :dcode="formData.djbmbm"
        />
      </el-form-item>
      <el-form-item label="申请部门" prop="djbmbm">
        <DDLDeptList v-model="formData.djbmbm" :disabled="true" />
      </el-form-item>
      <br />
      <el-form-item label="申请日期" props="djsj">
        <el-date-picker
          v-model="formData.djsj"
          type="date"
          placeholder="系统自动生成"
          :disabled="true"
        />
      </el-form-item>
      <el-form-item label="使用部门" prop="sybm">
        <!-- 部门选择下拉框 -->
        <DDLDeptList v-model="formData.sybm" />
      </el-form-item>
      <el-form-item label="使用部门资产管理员" prop="bmzcgly">
        <DDLUserList
          :key="formData.sybm"
          v-model="formData.bmzcgly"
          rcode="0202"
          :dcode="formData.sybm"
        />
      </el-form-item>
      <br />
      <el-form-item label="领用说明" props="ytsm" style="width: 800px">
        <el-input
          v-model="formData.ytsm"
          type="textarea"
          maxlength="200"
          show-word-limit
          :rows="5"
        />
      </el-form-item>
      <br />
      <el-form-item label="备注" props="notes" style="width: 800px">
        <el-input
          v-model="formData.notes"
          type="textarea"
          maxlength="200"
          show-word-limit
          :rows="5"
        />
      </el-form-item>
      <el-form-item label="是否以旧换新" prop="by2">
        <el-select v-model="formData.by2">
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="使用部门资产管理员" prop="by3">
        <DDLUserList
          :key="formData.sybm"
          v-model="formData.by3"
          rcode="0202"
          :dcode="formData.sybm"
        />
      </el-form-item>
    </el-form>
    <Title name="领用资产清单">
      <div>
        <el-button
          v-if="editable"
          plain
          type="danger"
          icon="plus"
          @click="handleAddreceivePorperties"
        >
          添加领用资产
        </el-button>
      </div>
    </Title>

    <el-table v-loading="loading" :data="pageData" highlight-current-row :border="true" stripe>
      <!-- <el-table-column type="selection" width="50" align="center" fixed v-if="add || edit" /> -->
      <el-table-column label="序号" type="index" width="55" align="center" fixed>
        <template #default="scope">
          <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="资产编号" prop="zcbh" width="120" />
      <el-table-column label="资产名称" prop="zcmc" width="200" />
      <el-table-column label="金额(元)" prop="je" width="100" />
      <el-table-column label="数量" prop="sl" min-width="80" />
      <el-table-column label="使用人" prop="syrname" width="100" />
      <el-table-column label="使用部门" prop="sybmname" width="200" />
      <!-- ********************** 操作列 **********************
         按钮：查询View；编辑Edit；删除Delete -->
      <el-table-column label="操作" fixed="right" width="150">
        <template #default="scope">
          <el-button
            type="primary"
            icon="View"
            size="small"
            link
            @click="handleRowView(scope.row.zcGuid, scope.row.rkguid)"
          >
            查看
          </el-button>
          <el-button
            v-if="editable"
            type="danger"
            icon="Delete"
            size="small"
            link
            @click="handleRowDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- ********************** 翻页 ********************** -->
    <pagination
      v-if="total > 10"
      v-model:total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="handlePageQuery"
    />

    <!-- 选择资产弹窗 -->
    <el-drawer v-model="choosedialog.visible" :title="choosedialog.title" append-to-body size="65%">
      <receiveChoose
        :key="keyId"
        :dcode="formData.sybm"
        :handleReturnConfirm="handleReturnConfirm"
      />
    </el-drawer>
    <!-- 查看资产弹窗 -->
    <el-drawer v-model="itemVisible" append-to-body size="70%">
      <CardPanel :key="itemGuid" :guid="itemGuid" :rkguid="itemRkGuid" />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "receiveEdit",
});
import { ElLoading } from "element-plus";
import { useUserStore } from "@/store";
import receiveAPI, {
  receiveForm,
  receiveDetailPageVO,
  receiveDetailsPageQuery,
} from "@/api/properties/receive";
import receiveChoose from "@/views/properties/use/receive/components/ReceiveChoose.vue";

//————————————————————————————————————————————暴露的方法,和请求参数
//组件参数
const props = defineProps({
  id: {
    type: Number,
  },
  guid: {
    type: String,
    required: true,
  },
  //保存成功后需要关闭抽屉并刷新数据
  RefreshFatherDrawer: {
    type: Function,
    required: true,
  },
  //是否可编辑
  editable: {
    type: Boolean,
  },
  dcbm: {
    type: String,
    required: true,
  },
});
//暴露的方法
const handleReturnConfirm = (zcguid: string[]) => {
  if (!zcguid.length) {
    ElMessage.warning("请选择要领用的资产");
    return;
  }
  const param = { syGuid: props.guid || "", zcGuid: zcguid.join(",") };
  loading.value = true;
  const dialogloading = ElLoading.service({
    lock: true,
    text: "处理中",
  });
  receiveAPI
    .addDetail(param)
    .then(() => {
      ElMessage.success("添加成功");
      handlePageQuery();
      choosedialog.visible = false;
      keyId.value++;
    })
    .catch((error) => {
      console.error("添加领用资产失败:", error);
      ElMessage.error("添加领用资产失败");
    })
    .finally(() => {
      loading.value = false;
      dialogloading.close();
    });
};

//——————————————————————————————————————————————————form查询相关

const dcbmDisabled = ref(false);
const netcode = ref("");
//初始化一些用户的信息
const { nickname, dcode } = useUserStore().userInfo;
//表单
const formData = reactive<receiveForm>({
  guid: "",
  sqdh: "",
  djsj: new Date().toISOString().split("T")[0],
  djbmbm: dcode,
  sqlx: "1",
  sjzt: "0",
  by2: "",
  bmzcgly: "",
  djr: nickname,
});
const dataFormRef = ref<InstanceType<typeof ElForm>>();
const rules = reactive({
  sybm: [{ required: true, message: "使用部门不能为空", trigger: "blur" }],
  bmzcgly: [{ required: true, message: "使用部门资产管理员不能为空", trigger: "blur" }],
  by2: [{ required: true, message: "是否以旧换新不能为空", trigger: "blur" }],
});
/** 初始化领用信息 */
function handleFormQuery(guid?: string) {
  if (guid) {
    receiveAPI
      .getFormData(guid)
      .then((data) => {
        Object.assign(formData, data);
        netcode.value = formData.netcode || "";
      })
      .catch((error) => {
        console.error("获取领用数据失败:", error);
        ElMessage.error("获取领用数据失败");
      });
  } else {
    // 进去前给个新的GUID
    formData.guid = props.guid;
  }
}

//——————————————————————————————————————————————————form操作相关
const handleSave = async () => {
  dataFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const submitData = { ...formData };
      const id = formData.id;
      const apiCall = id ? receiveAPI.update(id, submitData) : receiveAPI.addreceive(submitData);
      await apiCall
        .then(async (res) => {
          ElMessage.success("领用单保存成功");
          //保存成功后，重新用id去查询一次数据
          if (!id) {
            handleFormQuery(formData.guid);
          }
          props.RefreshFatherDrawer();
        })
        .catch((error) => {
          ElMessage.error("操作失败，失败原因：" + error.message);
        })
        .finally(() => {});
    }
  });
};
/** 提交领用表单 */
const handleSubmit = async () => {
  ElMessageBox.confirm("确定提交审核吗?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    //第一层confirm
    .then(async () => {
      await handleSave().then(async () => {
        //第二层保存完成
        const submitData = { ...formData };
        const dialogloading = ElLoading.service({
          lock: true,
          text: "处理中",
        });
        await receiveAPI
          .submit(submitData.guid || "")
          .then(() => {
            //第三层提交完成
            ElMessage.success("领用单提交成功");
            props.RefreshFatherDrawer(true);
          })
          .finally(() => {
            dialogloading.close();
          })
          .catch((error) => {
            ElMessage.error(error.message);
          });
      });
    })
    .catch(() => {});
};

//——————————————————————————————————————————————————list查询相关

const loading = ref(false);

const total = ref(0);
// 领用表格数据
const pageData = ref<receiveDetailPageVO[]>([]);
//请求参数
const queryParams = reactive<receiveDetailsPageQuery>({
  pageNum: 1,
  pageSize: 10,
  syguid: props.guid,
});
//列表查询
const handlePageQuery = () => {
  if (!props.guid) {
    pageData.value = [];
    total.value = 0;
    return;
  }
  loading.value = true;
  receiveAPI
    .getDetailsPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
      dcbmDisabled.value = total.value > 0;
    })
    .catch((error) => {
      console.error("获取领用资产列表失败:", error);
      ElMessage.error("获取领用资产列表失败");
      pageData.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
};
//——————————————————————————————————————————————————list操作相关
const removeIds = ref<number[]>([]);
//删除项
const handleRowDelete = (id?: number) => {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }
  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      receiveAPI
        .deleteDetailsByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handlePageQuery();
          keyId.value++;
        })
        .catch((error) => {
          console.error("删除领用资产失败:", error);
          ElMessage.error("删除领用资产失败");
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
};

//—————————————————————————————————————————————资产选择弹窗
const keyId = ref(0);
// 弹窗显隐
const choosedialog = reactive({
  title: "资产领用明细",
  visible: false,
});
const handleAddreceivePorperties = async () => {
  await dataFormRef.value?.validate();
  const api = handleSave();
  await api.then(() => {
    choosedialog.visible = true;
  });
};

//—————————————————————————————————————————————资产查看弹窗
const itemVisible = ref(false);
const itemGuid = ref("");
const itemRkGuid = ref("");
const handleRowView = (guid: string, rkguid: string) => {
  itemGuid.value = guid;
  itemRkGuid.value = rkguid;
  itemVisible.value = true;
};

onMounted(() => {
  handleFormQuery(props.guid);
  handlePageQuery();
});
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}

/* 自定义对话框样式 */
.custom-dialog {
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  background-color: #ffffff;
  .el-dialog__header {
    background-color: #409eff;
    color: #ffffff;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    .el-dialog__title {
      color: #ffffff;
    }
  }
  .el-dialog__body {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.9);
  }
}
</style>
