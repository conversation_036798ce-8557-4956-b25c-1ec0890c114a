<!-- 调拨单 -->
<template>
  <div class="app-container">
    <Title name="处置资产清单">
      <div>
        <el-button
          plain
          type="danger"
          icon="close"
          :disabled="selectedIds.length === 0"
          @click="handleAfter"
        >
          退回
        </el-button>
      </div>
    </Title>

    <el-table
      v-loading="loading"
      :data="pageData"
      highlight-current-row
      :border="true"
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" align="center" fixed />
      <el-table-column type="index" label="序号" width="55" />
      <el-table-column label="资产编号" prop="zcbh" width="120" />
      <el-table-column label="资产名称" prop="zcmc" width="200" />
      <el-table-column label="金额(元)" prop="je" width="100" />
      <el-table-column label="数量" prop="sl" min-width="80" />
      <el-table-column label="使用人" prop="syrname" width="200" />
      <el-table-column label="使用部门" prop="sybmname" width="300" />
      <!-- ********************** 操作列 **********************
         按钮：查询View；编辑Edit；删除Delete -->
      <el-table-column label="操作" fixed="right" width="150">
        <template #default="scope">
          <el-button
            v-hasPerm="'sys:user:password:reset'"
            type="primary"
            icon="Document"
            size="small"
            link
            @click="hancleRowView(scope.row.zcGuid)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- ********************** 翻页 ********************** -->
    <pagination
      v-if="total > 15"
      v-model:total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="handleQuery"
    />

    <!-- 查看资产弹窗 -->
    <el-drawer v-model="itemVisible" append-to-body size="70%">
      <CardPanel :key="itemGuid" :guid="itemGuid" />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import disposeAPI from "@/api/properties/dispose";
import clearAPI from "@/api/properties/clearance";

const props = defineProps({
  //是否可编辑
  guid: {
    type: String,
    required: true,
  },
});
const loading = ref(false);
const selectedIds = ref<number[]>([]);
const total = ref(0);
// 表格数据
const pageData = ref<any[]>([]);
//请求参数
const queryParams = reactive<any>({
  pageNum: 1,
  pageSize: 15,
  czGuid: props.guid,
  hxzt: "02024001",
});

const handleQuery = () => {
  //guid为空是新增
  if (props.guid != "") {
    loading.value = true;
    disposeAPI
      .getDetailsPage(queryParams)
      .then((data) => {
        pageData.value = data.list;
        total.value = data.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }
};

//—————————————————————————————————————————————打开弹窗选择资产

// 弹窗显隐
const choosedialog = reactive({
  title: "",
  visible: false,
});

//------------------------------打开弹窗查看资产信息
const itemVisible = ref(false);
const itemGuid = ref("");
const hancleRowView = (guid: string) => {
  itemGuid.value = guid;
  itemVisible.value = true;
};
/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  selectedIds.value = selection.map((item: any) => item.id);
}
//多选退回
const params = {
  ClearanceDetailsids: "",
};
const handleAfter = () => {
  params.ClearanceDetailsids = selectedIds.value.join(",");
  console.log("params", params);
  ElMessageBox.confirm("确定要退回选中的资产数据吗？退回后资产会******", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    clearAPI.afterDisposeDetailPage(params).then((res) => {
      //res是新处置单的guid
      ElMessage.success("操作成功");
      // 跳转到index页面并添加路由参数type为add
      // router.push({ path: "/dispose/edit", query: { type: "add" } });
      handleQuery();
    });
  });
};

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped></style>
@/api/properties/clearance
