<template>
  <div class="app-container">
    <!-- 补充 handleReturnConfirm 属性 -->
    <DisposeChoose type="add" :handleReturnConfirm="handleReturnConfirm" />
  </div>
</template>

<script setup lang="ts">
import DisposeChoose from "@/views/properties/dispose/components/DisposeChoose.vue";

//这个方法用于编辑处置单时添加资产的，新增处置单时必须要这个方法
function handleReturnConfirm() {}
</script>
<style lang="scss" scoped>
.el-form {
  width: auto;
}
</style>
