<template>
  <div class="chat-interface">
    <el-card class="chat-container">
      <template #header>
        <div class="chat-header">
          <div v-if="currentAgent" class="agent-info">
            <el-avatar :src="currentAgent.avatar" :alt="currentAgent.name">
              {{ currentAgent.name.charAt(0) }}
            </el-avatar>
            <div class="agent-details">
              <h3>{{ currentAgent.name }}</h3>
              <p>{{ currentAgent.description }}</p>
            </div>
          </div>
          <div class="chat-actions">
            <el-button v-if="sessionId" type="primary" plain size="small" @click="endChat">
              结束会话
            </el-button>
          </div>
        </div>
      </template>

      <!-- 聊天消息区域 -->
      <div ref="messagesContainer" class="chat-messages">
        <template v-if="messages.length > 0">
          <div
            v-for="(message, index) in messages"
            :key="message.id"
            :class="['message-item', message.type === 'user' ? 'user-message' : 'agent-message']"
          >
            <div class="message-avatar">
              <el-avatar v-if="message.type === 'user'" icon="el-icon-user" />
              <el-avatar v-else :src="currentAgent?.avatar" :alt="currentAgent?.name">
                {{ currentAgent?.name?.charAt(0) }}
              </el-avatar>
            </div>
            <div class="message-content">
              <div class="message-sender">
                {{ message.type === "user" ? "我" : currentAgent?.name }}
              </div>
              <div class="message-text">
                <template v-for="(content, cIndex) in message.content" :key="cIndex">
                  <div v-if="content.type === 'text'" v-html="formatMessage(content.value)" />
                  <el-image
                    v-else-if="content.type === 'image'"
                    :src="content.value"
                    :preview-src-list="[content.value]"
                    fit="contain"
                    class="message-image"
                  />
                </template>
              </div>
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>
          </div>
        </template>
        <div v-else class="empty-messages">
          <el-empty description="开始与智能体对话吧" />
        </div>
        <div v-if="loading" class="loading-indicator">
          <el-icon class="is-loading"><Loading /></el-icon>
          智能体正在思考中...
        </div>
      </div>

      <!-- 消息输入区域 -->
      <div class="chat-input">
        <el-input
          v-model="userInput"
          type="textarea"
          :rows="2"
          :disabled="loading || !sessionId"
          placeholder="输入您的问题..."
          @keyup.enter.ctrl="sendMessage"
        />
        <div class="input-actions">
          <span class="input-tip">按 Ctrl + Enter 发送</span>
          <el-button
            type="primary"
            :disabled="loading || !userInput.trim() || !sessionId"
            @click="sendMessage"
          >
            发送
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch } from "vue";
import { ElMessage } from "element-plus";
import { Loading } from "@element-plus/icons-vue";
import CozeAPI, {
  Agent,
  ChatMessage,
  MessageType,
  ContentType,
  ChatSession,
} from "@/views/ai/api/index";
import dayjs from "dayjs";

defineOptions({
  name: "ChatInterface",
});

// 定义组件属性
const props = defineProps({
  // 智能体ID
  agentId: {
    type: String,
    default: "",
  },
});

// 定义事件
const emit = defineEmits(["session-created", "session-ended"]);

// 状态变量
const sessionId = ref<string>("");
const currentAgent = ref<Agent | null>(null);
const messages = ref<ChatMessage[]>([]);
const userInput = ref<string>("");
const loading = ref<boolean>(false);
const messagesContainer = ref<HTMLElement | null>(null);

// 初始化会话
const initChat = async (agentId: string) => {
  if (!agentId) {
    ElMessage.warning("请选择一个智能体");
    return;
  }

  try {
    loading.value = true;
    const response = await CozeAPI.initChat(agentId);
    sessionId.value = response.sessionId;
    currentAgent.value = {
      id: agentId,
      name: response.session.title || "智能助手",
      avatar: "",
    };
    messages.value = response.session.messages || [];
    emit("session-created", sessionId.value);
  } catch (error) {
    console.error("初始化会话失败:", error);
    ElMessage.error("初始化会话失败，请重试");
  } finally {
    loading.value = false;
  }
};

// 发送消息
const sendMessage = async () => {
  if (!sessionId.value) {
    ElMessage.warning("请先初始化会话");
    return;
  }

  if (!userInput.value.trim()) {
    ElMessage.warning("请输入消息内容");
    return;
  }

  try {
    // 添加用户消息到列表
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      sessionId: sessionId.value,
      type: MessageType.USER,
      senderId: "user",
      senderName: "我",
      content: [
        {
          type: ContentType.TEXT,
          value: userInput.value.trim(),
        },
      ],
      timestamp: Date.now(),
    };

    messages.value.push(userMessage);

    // 清空输入框
    const sentMessage = userInput.value;
    userInput.value = "";

    // 滚动到底部
    await scrollToBottom();

    // 显示加载状态
    loading.value = true;

    // 发送请求到服务器
    const response = await CozeAPI.sendMessage(sessionId.value, sentMessage);

    // 添加智能体回复到列表
    messages.value.push(response.reply);
  } catch (error) {
    console.error("发送消息失败:", error);
    ElMessage.error("发送消息失败，请重试");
  } finally {
    loading.value = false;
    // 滚动到底部
    await scrollToBottom();
  }
};

// 结束会话
const endChat = async () => {
  if (!sessionId.value) return;

  try {
    await CozeAPI.endChat(sessionId.value);
    ElMessage.success("会话已结束");
    sessionId.value = "";
    messages.value = [];
    emit("session-ended");
  } catch (error) {
    console.error("结束会话失败:", error);
    ElMessage.error("结束会话失败，请重试");
  }
};

// 格式化消息文本（处理换行和链接）
const formatMessage = (text: string) => {
  // 将换行符转换为<br>
  let formatted = text.replace(/\n/g, "<br>");

  // 将URL转换为可点击的链接
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  formatted = formatted.replace(urlRegex, '<a href="$1" target="_blank">$1</a>');

  return formatted;
};

// 格式化时间
const formatTime = (timestamp: number) => {
  return dayjs(timestamp).format("YYYY-MM-DD HH:mm:ss");
};

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick();
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

// 监听agentId变化，初始化会话
watch(
  () => props.agentId,
  (newAgentId) => {
    if (newAgentId && !sessionId.value) {
      initChat(newAgentId);
    }
  }
);

// 组件挂载时，如果有agentId则初始化会话
onMounted(() => {
  if (props.agentId) {
    initChat(props.agentId);
  }
});
</script>

<style lang="scss" scoped>
.chat-interface {
  height: 100%;
  display: flex;
  flex-direction: column;

  .chat-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 0;
      overflow: hidden;
    }
  }

  .chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .agent-info {
      display: flex;
      align-items: center;

      .agent-details {
        margin-left: 12px;

        h3 {
          margin: 0;
          font-size: 16px;
        }

        p {
          margin: 4px 0 0;
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }

  .chat-messages {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    background-color: #f5f7fa;

    .message-item {
      display: flex;
      margin-bottom: 16px;

      &.user-message {
        flex-direction: row-reverse;

        .message-content {
          margin-right: 12px;
          margin-left: 0;
          align-items: flex-end;

          .message-text {
            background-color: #95ec69;
            border-radius: 8px 0 8px 8px;
          }

          .message-sender,
          .message-time {
            text-align: right;
          }
        }
      }

      &.agent-message .message-content {
        margin-left: 12px;

        .message-text {
          background-color: #ffffff;
          border-radius: 0 8px 8px 8px;
        }
      }

      .message-content {
        display: flex;
        flex-direction: column;
        max-width: 70%;

        .message-sender {
          font-size: 12px;
          margin-bottom: 4px;
          color: var(--el-text-color-secondary);
        }

        .message-text {
          padding: 10px 12px;
          word-break: break-word;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

          :deep(a) {
            color: var(--el-color-primary);
            text-decoration: underline;
          }
        }

        .message-image {
          max-width: 100%;
          max-height: 200px;
          margin: 8px 0;
          border-radius: 4px;
        }

        .message-time {
          font-size: 11px;
          margin-top: 4px;
          color: var(--el-text-color-secondary);
        }
      }
    }

    .empty-messages {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
    }

    .loading-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px;
      color: var(--el-text-color-secondary);

      .el-icon {
        margin-right: 8px;
      }
    }
  }

  .chat-input {
    padding: 16px;
    border-top: 1px solid var(--el-border-color-lighter);

    .input-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;

      .input-tip {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}
</style>
