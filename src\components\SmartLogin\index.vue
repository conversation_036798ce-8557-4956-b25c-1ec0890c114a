<template>
  <div class="smart-login">
    111
    <!-- 设备切换按钮（调试模式显示） -->
    <div v-if="showDeviceSwitch" class="device-switch">
      <div class="switch-container">
        <button class="switch-btn" :class="{ active: isMobile }" @click="toggleDevice">
          {{ isMobile ? "📱 移动端" : "💻 PC端" }}
        </button>
        <span class="switch-info">点击切换</span>
      </div>
    </div>

    <!-- 根据设备类型显示对应的登录页面 -->
    <div v-if="!isMobile" class="pc-login-container">
      <!-- PC端登录内容将通过路由加载 -->
      <router-view />
    </div>

    <div v-else class="mobile-login-container">
      <!-- 移动端登录内容将通过路由加载 -->
      <router-view />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store";
import { DeviceEnum } from "@/enums/DeviceEnum";

const route = useRoute();
const appStore = useAppStore();

// 是否为移动端
const isMobile = computed(() => appStore.device === DeviceEnum.MOBILE);

// 是否显示设备切换按钮（URL参数控制）
const showDeviceSwitch = computed(() => {
  return "true";
});

// 切换设备类型（仅用于调试）
const toggleDevice = () => {
  if (route.query.debug === "true") {
    // 在调试模式下，可以手动切换设备类型
    const newDevice = isMobile.value ? DeviceEnum.DESKTOP : DeviceEnum.MOBILE;
    appStore.toggleDevice(newDevice);
  }
};
</script>

<style lang="scss" scoped>
.smart-login {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.device-switch {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;

  .switch-container {
    display: flex;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 4px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
  }

  .switch-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: none;
    background: transparent;
    border-radius: 6px;
    font-size: 12px;
    color: #666;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background: rgba(64, 128, 255, 0.1);
      color: var(--el-color-primary);
    }

    &.active {
      background: var(--el-color-primary);
      color: white;
      box-shadow: 0 2px 4px rgba(64, 128, 255, 0.3);
    }
  }
}

// 移动端适配
@media screen and (max-width: 992px) {
  .device-switch {
    top: 10px;
    right: 10px;

    .switch-container {
      padding: 2px;
    }

    .switch-btn {
      padding: 6px 8px;
      font-size: 11px;
    }
  }
}

// PC端样式
@media screen and (min-width: 993px) {
  .smart-login {
    background: #f5f5f5;
  }
}
</style>
