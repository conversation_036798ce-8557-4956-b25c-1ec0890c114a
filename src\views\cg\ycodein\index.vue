<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item label="$fieldConfig.fieldComment" prop="id">
            <el-input
              v-model="queryParams.id"
              placeholder="$fieldConfig.fieldComment"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item label="序号PK" prop="guid">
            <el-input
              v-model="queryParams.guid"
              placeholder="序号PK"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item label="我方编码" prop="xcode">
            <el-input
              v-model="queryParams.xcode"
              placeholder="我方编码"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item label="对方编码" prop="ycode">
            <el-input
              v-model="queryParams.ycode"
              placeholder="对方编码"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item
            label=" 类别  1：采购目录
 2：采购方式
  3：采购类型
  4：资金类型
   5：付款方式
  6：法律条款
  8：附件类型
  9：资金来源
   10:费用项数据"
            prop="lb"
          >
            <el-input
              v-model="queryParams.lb"
              placeholder=" 类别  1：采购目录
 2：采购方式
  3：采购类型
  4：资金类型
   5：付款方式
  6：法律条款
  8：附件类型
  9：资金来源
   10:费用项数据"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item label="状态，’1’ 正常  ‘0’停用" prop="enabled">
            <el-input
              v-model="queryParams.enabled"
              placeholder="状态，’1’ 正常  ‘0’停用"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon><Search /></template>
              搜索
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon><Refresh /></template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="列表">
        <div>
          <el-button
            v-hasPerm="['cg:sysYcodein:add']"
            type="success"
            plain
            size="small"
            @click="
              formType.view = false;
              handleEdit();
            "
          >
            <template #icon><Plus /></template>
            新增
          </el-button>
          <el-button
            v-hasPerm="['cg:sysYcodein:delete']"
            type="danger"
            plain
            size="small"
            :disabled="removeIds.length === 0"
            @click="handleDelete()"
          >
            <template #icon><Delete /></template>
            删除
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
        @expand-change="handleExpandChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed />
        <el-table-column label="展开" type="expand" width="55">
          <template #default="props">
            <div style="padding: 0px 70px; background-color: #ffffff">
              <!-- <TransferPropertiesList
                :dcbm="props.row.dcbm"
                :guid="props.row.guid || ''"
              ></TransferPropertiesList> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          key="id"
          label="$fieldConfig.fieldComment"
          prop="id"
          min-width="150"
          align="center"
        />
        <el-table-column key="year" label="年度" prop="year" min-width="150" align="center" />
        <el-table-column key="guid" label="序号PK" prop="guid" min-width="150" align="center" />
        <el-table-column key="xcode" label="我方编码" prop="xcode" min-width="150" align="center" />
        <el-table-column key="ycode" label="对方编码" prop="ycode" min-width="150" align="center" />
        <el-table-column
          key="lb"
          label=" 类别  1：采购目录
 2：采购方式
  3：采购类型
  4：资金类型
   5：付款方式
  6：法律条款
  8：附件类型
  9：资金来源
   10:费用项数据"
          prop="lb"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="isbncode"
          label="标准编码"
          prop="isbncode"
          min-width="150"
          align="center"
        />
        <el-table-column key="name" label="名称" prop="name" min-width="150" align="center" />
        <el-table-column
          key="levelnum"
          label="级数"
          prop="levelnum"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="isleaf"
          label="是否底级"
          prop="isleaf"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="parentGuid"
          label="上级序号"
          prop="parentGuid"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="enabled"
          label="状态，’1’ 正常  ‘0’停用"
          prop="enabled"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="zfcode"
          label="支付编码"
          prop="zfcode"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="payflag"
          label="支付标识 1表示资金发送给银行 0表示资金不发送银行"
          prop="payflag"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="updatetime"
          label="更新时间"
          prop="updatetime"
          min-width="150"
          align="center"
        />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button
              v-hasPerm="['cg:sysYcodein:edit']"
              type="primary"
              size="small"
              icon="Edit"
              link
              @click="
                formType.view = false;
                handleRowEdit(scope.row.id);
              "
            >
              编辑
            </el-button>
            <el-button
              v-hasPerm="['cg:sysYcodein:delete']"
              type="danger"
              size="small"
              icon="Delete"
              link
              @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
            <el-button
              v-if="check"
              type="primary"
              icon="DocumentChecked"
              size="small"
              link
              @click="hancleRowCheck"
            >
              审核
            </el-button>
            <el-button
              v-if="view"
              type="success"
              icon="Document"
              size="small"
              link
              @click="hancleRowView()"
            >
              查看
            </el-button>
            <el-button
              v-if="view"
              type="primary"
              icon="Printer"
              size="small"
              link
              @click="
                formType.view = true;
                hancleRowPrint();
              "
            >
              打印
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- sys_ycodein配置表表单弹窗 -->
    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      append-to-body
      size="75%"
      :before-close="handleCloseDialog"
    >
      <el-card>
        <template #header>
          <Title name="XX信息">
            <div>
              <el-button v-if="!formType.view" type="primary" @click="handleSubmit()">
                保存
              </el-button>
              <el-button v-if="!formType.view" type="danger" @click="handleSubmit()">
                提交
              </el-button>
            </div>
          </Title>
        </template>

        <el-form
          ref="dataFormRef"
          :model="formData"
          :rules="rules"
          label-width="100px"
          :inline="true"
          :disabled="formType.view"
        >
          <el-form-item label="$fieldConfig.fieldComment" prop="id">
            <el-input v-model="formData.id" placeholder="$fieldConfig.fieldComment" />
          </el-form-item>
          <el-form-item label="年度" prop="year">
            <el-input v-model="formData.year" placeholder="年度" />
          </el-form-item>
          <el-form-item label="序号PK" prop="guid">
            <el-input v-model="formData.guid" placeholder="序号PK" />
          </el-form-item>
          <el-form-item label="我方编码" prop="xcode">
            <el-input v-model="formData.xcode" placeholder="我方编码" />
          </el-form-item>
          <el-form-item label="对方编码" prop="ycode">
            <el-input v-model="formData.ycode" placeholder="对方编码" />
          </el-form-item>
          <el-form-item
            label=" 类别  1：采购目录
 2：采购方式
  3：采购类型
  4：资金类型
   5：付款方式
  6：法律条款
  8：附件类型
  9：资金来源
   10:费用项数据"
            prop="lb"
          >
            <el-input
              v-model="formData.lb"
              placeholder=" 类别  1：采购目录
 2：采购方式
  3：采购类型
  4：资金类型
   5：付款方式
  6：法律条款
  8：附件类型
  9：资金来源
   10:费用项数据"
            />
          </el-form-item>
          <el-form-item label="标准编码" prop="isbncode">
            <el-input v-model="formData.isbncode" placeholder="标准编码" />
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input v-model="formData.name" placeholder="名称" />
          </el-form-item>
          <el-form-item label="级数" prop="levelnum">
            <el-input v-model="formData.levelnum" placeholder="级数" />
          </el-form-item>
          <el-form-item label="是否底级" prop="isleaf">
            <el-input v-model="formData.isleaf" placeholder="是否底级" />
          </el-form-item>
          <el-form-item label="上级序号" prop="parentGuid">
            <el-input v-model="formData.parentGuid" placeholder="上级序号" />
          </el-form-item>
          <el-form-item label="状态，’1’ 正常  ‘0’停用" prop="enabled">
            <el-input v-model="formData.enabled" placeholder="状态，’1’ 正常  ‘0’停用" />
          </el-form-item>
          <el-form-item label="支付编码" prop="zfcode">
            <el-input v-model="formData.zfcode" placeholder="支付编码" />
          </el-form-item>
          <el-form-item label="支付标识 1表示资金发送给银行 0表示资金不发送银行" prop="payflag">
            <el-input
              v-model="formData.payflag"
              placeholder="支付标识 1表示资金发送给银行 0表示资金不发送银行"
            />
          </el-form-item>
          <el-form-item label="更新时间" prop="updatetime">
            <el-input v-model="formData.updatetime" placeholder="更新时间" />
          </el-form-item>
        </el-form>
        <template #footer></template>
      </el-card>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "SysYcodein",
  inheritAttrs: false,
});
import { useUserStore } from "@/store";
import SysYcodeinAPI, {
  SysYcodeinPageVO,
  SysYcodeinForm,
  SysYcodeinPageQuery,
} from "@/api/cg/sys-ycodein";

// 获取路由参数,view/add/check
const route = useRoute();
const type = ref(route.query.type?.toString() || "view");
//显示隐藏权限,用于v-if
const add = type.value == "add";
const view = type.value == "view";
const check = type.value == "check";
//页面参数
const queryFormRef = ref(ElForm);
const loading = ref(false);
const removeIds = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<SysYcodeinPageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// sys_ycodein配置表表格数据
const pageData = ref<SysYcodeinPageVO[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

/** 查询sys_ycodein配置表 */
function handleQuery() {
  loading.value = true;
  SysYcodeinAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置sys_ycodein配置表查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  removeIds.value = selection.map((item: any) => item.id);
}

/** 打开sys_ycodein配置表弹窗 */
function handleEdit(id?: number) {
  dialog.visible = true;
  if (id) {
    dialog.title = "修改sys_ycodein配置表";
    SysYcodeinAPI.getFormData(id).then((data) => {
      Object.assign(formData, data);
    });
  } else {
    dialog.title = "新增sys_ycodein配置表";
  }
}

/** 关闭sys_ycodein配置表弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  Object.assign(formData, {});
}

/** 删除sys_ycodein配置表 */
function handleDelete(id?: number) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      SysYcodeinAPI.deleteByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

onMounted(() => {
  handleQuery();
});

//--------------以下是form表单相关
const dataFormRef = ref(ElForm);

// sys_ycodein配置表表单数据
const formData = reactive<SysYcodeinForm>({});

const formType = reactive({
  add: false,
  edit: false,
  view: false,
});

// sys_ycodein配置表表单校验规则
const rules = reactive({
  ycode: [{ required: true, message: "请输入对方编码", trigger: "blur" }],
  lb: [
    {
      required: true,
      message:
        "请输入 类别  1：采购目录2：采购方式3：采购类型4：资金类型5：付款方式6：法律条款8：附件类型9：资金来源10:费用项数据",
      trigger: "blur",
    },
  ],
});

/** 提交sys_ycodein配置表表单 */
function handleSubmit() {
  dataFormRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      const id = formData.id;
      if (id) {
        SysYcodeinAPI.update(id, formData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        SysYcodeinAPI.add(formData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}
</script>
<style lang="scss" scoped></style>
