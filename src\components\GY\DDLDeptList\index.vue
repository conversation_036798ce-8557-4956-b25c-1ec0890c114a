<!-- 部门选择组件 -->
<template>
  <el-tree-select
    v-model="_modelValue"
    :placeholder="placeholder"
    :data="deptOptions"
    :loading="loading"
    :disabled="disabled"
    :size="size"
    :clearable="clearable"
    filterable
    check-strictly
    :render-after-expand="false"
    @change="handleChange"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import DeptAPI from "@/api/system/dept";

interface Props {
  /** 选中的部门ID */
  modelValue?: string;
  /** 占位文本 */
  placeholder?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 组件尺寸 */
  size?: "large" | "default" | "small";
  /** 是否可清空 */
  clearable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  placeholder: "请选择所属部门",
  disabled: false,
  size: "default",
  clearable: true,
});

// 下拉框数据
const deptOptions = ref<OptionType[]>([]);
// 加载状态
const loading = ref(false);
// 缓存时间 (ms)
const CACHE_DURATION = 5 * 60 * 1000;
// 上次加载时间
const lastLoadTime = ref<number>(0);

// 双向绑定
const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
  (e: "change", value: string): void;
}>();

const _modelValue = computed({
  get: () => props.modelValue,
  set: (val) => emit("update:modelValue", val || ""),
});

// 值变化事件
const handleChange = (value: string) => {
  emit("change", value);
};

// 加载部门数据
const loadDeptOptions = async () => {
  // 检查缓存是否有效
  const now = Date.now();
  if (deptOptions.value?.length && now - lastLoadTime.value < CACHE_DURATION) {
    return;
  }

  loading.value = true;
  try {
    deptOptions.value = await DeptAPI.getOptionsCode();
    lastLoadTime.value = now;
  } catch (error) {
    console.error("Failed to load department data:", error);
    ElMessage.error("部门数据加载失败");
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadDeptOptions();
});

defineExpose({
  reload: loadDeptOptions,
});
</script>
