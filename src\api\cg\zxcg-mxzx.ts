import request from "@/utils/request";

const ZXCGMXZX_BASE_URL = "/api/v1/zxcgMxzxs";

const ZxcgMxzxAPI = {
  /** 获取ZxcgMxzx分页数据 */
  getPage(queryParams?: ZxcgMxzxPageQuery) {
    return request<any, PageResult<ZxcgMxzxPageVO[]>>({
      url: `${ZXCGMXZX_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取ZxcgMxzx表单数据
   *
   * @param id ZxcgMxzxID
   * @returns ZxcgMxzx表单数据
   */
  getFormData(id: number) {
    return request<any, ZxcgMxzxForm>({
      url: `${ZXCGMXZX_BASE_URL}/${id}/form`,
      method: "get",
    });
  },

  /** 添加ZxcgMxzx*/
  add(data: ZxcgMxzxForm) {
    return request({
      url: `${ZXCGMXZX_BASE_URL}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新ZxcgMxzx
   *
   * @param id ZxcgMxzxID
   * @param data ZxcgMxzx表单数据
   */
  update(id: number, data: ZxcgMxzxForm) {
    return request({
      url: `${ZXCGMXZX_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 批量删除ZxcgMxzx，多个以英文逗号(,)分割
   *
   * @param ids ZxcgMxzxID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${ZXCGMXZX_BASE_URL}/${ids}`,
      method: "delete",
    });
  },
};

export default ZxcgMxzxAPI;

/** ZxcgMxzx分页查询参数 */
export interface ZxcgMxzxPageQuery extends PageQuery {
  /** 申请id */
  sqid?: number;
  /** rwid */
  rwid?: number;
  /** wjid */
  wjid?: number;
  /** bdid */
  bdid?: number;
  /** htid */
  htid?: number;
  /** 询价，竞价表id，合并明细使用 */
  zxid?: number;
}

/** ZxcgMxzx表单对象 */
export interface ZxcgMxzxForm {
  /** 主键，自增 */
  id?: number;
  /** 申请明细id */
  mxid?: number;
  /** guid */
  guid?: string;
  /** 主表GUID */
  parentguid?: string;
  /** 申请id */
  sqid?: number;
  type?: string;
  /** 外键，自增 默认0 */
  ysmx?: number;
  /** 明细编号 */
  mxbh?: string;
  /** 物品名称 */
  wpmc?: string;
  /** 采购目录 */
  cgml?: string;
  /** 品牌厂商 */
  ppcs?: string;
  /** 型号 */
  xinghao?: string;
  /** 规格 */
  gg?: string;
  /** 详细参数 */
  xxcs?: string;
  /** 是否特殊 进口、辐射、其它 */
  ists?: string;
  /** 单位 */
  dw?: string;
  /** 数量 */
  sl?: number;
  /** 单价（元） */
  price?: number;
  /** 金额（元） */
  ysje?: number;
  /** 开始时间 */
  time1?: Date;
  /** 结束时间 */
  time2?: Date;
  /** 其他说明 */
  notes?: string;
  /** 供应商 */
  gys?: string;
  /** 组织机构代码 */
  jgdm?: string;
  /** 法人 */
  gysfr?: string;
  /** 法人身份证 */
  frsfz?: string;
  /** 供应商联系人 */
  gyslxr?: string;
  /** 联系电话 */
  gystel?: string;
  /** 资产类型（0资产，1非资产） */
  khyh?: string;
  /** 开户帐号 */
  khzh?: string;
  /** 采购数量 */
  xsl?: number;
  /** 成交价格(元) */
  cjjg?: number;
  /** 是否备案（0 否，1是） */
  isba?: string;
  /** rwid */
  rwid?: number;
  /** wtid */
  wtid?: number;
  /** wmwtid */
  wmwtid?: number;
  /** wjid */
  wjid?: number;
  /** baid */
  baid?: number;
  /** bdid */
  bdid?: number;
  /** htid */
  htid?: number;
  /** 明细状态 0执行，1完成 */
  mxzt?: string;
  yt?: string;
  /** 使用人 */
  syr?: string;
  /** 存放地点 */
  cfdd?: string;
  xysl?: number;
  /** 申购理由 */
  sgly?: string;
  cjprice?: number;
  /** 是否进口 */
  sfjk?: number;
  /** 预算编码 */
  ysbm?: string;
  /** 拆分明细 */
  cfmx?: string;
  /** 费用项 */
  bCode?: string;
  /** 预算项 */
  buCode?: string;
  /** 明细订单编号（冻结解冻） */
  mxordbh?: string;
  /** 关联政采云明细id */
  zcyid?: number;
  ysjf?: string;
  /** 明细备案状态：1备案，0未备案。采购备案修改明细即为备案 */
  bazt?: string;
  /** 询价，竞价表id，合并明细使用 */
  zxid?: number;
  zcyddbh?: string;
  zcysbdz?: string;
}

/** ZxcgMxzx分页对象 */
export interface ZxcgMxzxPageVO {
  /** 主键，自增 */
  id?: number;
  /** 申请明细id */
  mxid?: number;
  /** guid */
  guid?: string;
  /** 主表GUID */
  parentguid?: string;
  /** 申请id */
  sqid?: number;
  type?: string;
  /** 外键，自增 默认0 */
  ysmx?: number;
  /** 明细编号 */
  mxbh?: string;
  /** 物品名称 */
  wpmc?: string;
  /** 采购目录 */
  cgml?: string;
  cgmlname?: string;
  /** 品牌厂商 */
  ppcs?: string;
  /** 型号 */
  xinghao?: string;
  /** 规格 */
  gg?: string;
  /** 详细参数 */
  xxcs?: string;
  /** 是否特殊 进口、辐射、其它 */
  ists?: string;
  /** 单位 */
  dw?: string;
  dwname?: string;
  /** 数量 */
  sl?: number;
  /** 单价（元） */
  price?: number;
  /** 金额（元） */
  ysje?: number;
  /** 开始时间 */
  time1?: Date;
  /** 结束时间 */
  time2?: Date;
  /** 其他说明 */
  notes?: string;
  /** 供应商 */
  gys?: string;
  /** 组织机构代码 */
  jgdm?: string;
  /** 法人 */
  gysfr?: string;
  /** 法人身份证 */
  frsfz?: string;
  /** 供应商联系人 */
  gyslxr?: string;
  /** 联系电话 */
  gystel?: string;
  /** 资产类型（0资产，1非资产） */
  khyh?: string;
  /** 开户帐号 */
  khzh?: string;
  /** 采购数量 */
  xsl?: number;
  /** 成交价格(元) */
  cjjg?: number;
  /** 是否备案（0 否，1是） */
  isba?: string;
  /** rwid */
  rwid?: number;
  /** wtid */
  wtid?: number;
  /** wmwtid */
  wmwtid?: number;
  /** wjid */
  wjid?: number;
  /** baid */
  baid?: number;
  /** bdid */
  bdid?: number;
  /** htid */
  htid?: number;
  /** 明细状态 0执行，1完成 */
  mxzt?: string;
  yt?: string;
  /** 使用人 */
  syr?: string;
  /** 存放地点 */
  cfdd?: string;
  xysl?: number;
  /** 申购理由 */
  sgly?: string;
  cjprice?: number;
  /** 是否进口 */
  sfjk?: number;
  /** 预算编码 */
  ysbm?: string;
  /** 拆分明细 */
  cfmx?: string;
  /** 费用项 */
  bCode?: string;
  /** 预算项 */
  buCode?: string;
  /** 明细订单编号（冻结解冻） */
  mxordbh?: string;
  /** 关联政采云明细id */
  zcyid?: number;
  ysjf?: string;
  /** 明细备案状态：1备案，0未备案。采购备案修改明细即为备案 */
  bazt?: string;
  /** 询价，竞价表id，合并明细使用 */
  zxid?: number;
  zcyddbh?: string;
  zcysbdz?: string;
}
