<template>
  <div class="mobile-filter-panel">
    <!-- 筛选项 -->
    <div v-for="filter in filters" :key="filter.key" class="filter-item">
      <div class="filter-label">{{ filter.label }}</div>
      
      <!-- 单选筛选 -->
      <div v-if="filter.type === 'radio'" class="filter-options">
        <div
          v-for="option in filter.options"
          :key="option.value"
          class="filter-option"
          :class="{ active: filter.value === option.value }"
          @click="updateFilter(filter.key, option.value)"
        >
          {{ option.label }}
        </div>
      </div>
      
      <!-- 多选筛选 -->
      <div v-else-if="filter.type === 'checkbox'" class="filter-options">
        <div
          v-for="option in filter.options"
          :key="option.value"
          class="filter-option checkbox"
          :class="{ active: isSelected(filter.value, option.value) }"
          @click="toggleCheckbox(filter.key, option.value)"
        >
          <div class="checkbox-icon">
            <svg-icon v-if="isSelected(filter.value, option.value)" icon-class="check" size="12px" />
          </div>
          {{ option.label }}
        </div>
      </div>
      
      <!-- 日期范围筛选 -->
      <div v-else-if="filter.type === 'daterange'" class="filter-date-range">
        <div class="date-input-group">
          <input
            v-model="filter.value[0]"
            type="date"
            class="date-input"
            placeholder="开始日期"
            @change="updateFilter(filter.key, filter.value)"
          />
          <span class="date-separator">至</span>
          <input
            v-model="filter.value[1]"
            type="date"
            class="date-input"
            placeholder="结束日期"
            @change="updateFilter(filter.key, filter.value)"
          />
        </div>
      </div>
      
      <!-- 数值范围筛选 -->
      <div v-else-if="filter.type === 'numberrange'" class="filter-number-range">
        <div class="number-input-group">
          <input
            v-model="filter.value[0]"
            type="number"
            class="number-input"
            :placeholder="filter.placeholder?.[0] || '最小值'"
            @input="updateFilter(filter.key, filter.value)"
          />
          <span class="number-separator">-</span>
          <input
            v-model="filter.value[1]"
            type="number"
            class="number-input"
            :placeholder="filter.placeholder?.[1] || '最大值'"
            @input="updateFilter(filter.key, filter.value)"
          />
        </div>
      </div>
      
      <!-- 下拉选择筛选 -->
      <div v-else-if="filter.type === 'select'" class="filter-select">
        <select
          v-model="filter.value"
          class="select-input"
          @change="updateFilter(filter.key, filter.value)"
        >
          <option value="">{{ filter.placeholder || '请选择' }}</option>
          <option
            v-for="option in filter.options"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </option>
        </select>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface FilterOption {
  label: string
  value: any
}

interface FilterConfig {
  key: string
  label: string
  type: 'radio' | 'checkbox' | 'daterange' | 'numberrange' | 'select'
  value: any
  options?: FilterOption[]
  placeholder?: string | string[]
}

interface Props {
  filters: FilterConfig[]
}

interface Emits {
  (e: 'update', key: string, value: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 更新筛选条件
const updateFilter = (key: string, value: any) => {
  emit('update', key, value)
}

// 检查是否选中
const isSelected = (filterValue: any[], optionValue: any) => {
  if (!Array.isArray(filterValue)) return false
  return filterValue.includes(optionValue)
}

// 切换多选项
const toggleCheckbox = (key: string, value: any) => {
  const filter = props.filters.find(f => f.key === key)
  if (!filter) return
  
  if (!Array.isArray(filter.value)) {
    filter.value = []
  }
  
  const index = filter.value.indexOf(value)
  if (index > -1) {
    filter.value.splice(index, 1)
  } else {
    filter.value.push(value)
  }
  
  updateFilter(key, [...filter.value])
}
</script>

<style lang="scss" scoped>
.mobile-filter-panel {
  background: white;
}

.filter-item {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-option {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background: #f5f5f5;
  border-radius: 16px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
  
  &.active {
    background: var(--el-color-primary);
    color: white;
  }
  
  &.checkbox {
    gap: 6px;
  }
}

.checkbox-icon {
  width: 14px;
  height: 14px;
  border: 1px solid #ddd;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  
  .filter-option.active & {
    border-color: white;
    background: white;
    color: var(--el-color-primary);
  }
}

.filter-date-range,
.filter-number-range {
  .date-input-group,
  .number-input-group {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .date-separator,
  .number-separator {
    font-size: 12px;
    color: #999;
  }
}

.date-input,
.number-input {
  flex: 1;
  height: 36px;
  padding: 0 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  
  &:focus {
    border-color: var(--el-color-primary);
  }
}

.filter-select {
  .select-input {
    width: 100%;
    height: 36px;
    padding: 0 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    outline: none;
    
    &:focus {
      border-color: var(--el-color-primary);
    }
  }
}
</style>
