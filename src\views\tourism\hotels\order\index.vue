<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-card class="search-card" :body-style="{ padding: '24px' }">
      <el-form :model="searchForm" inline class="search-form">
        <div class="form-item-group">
          <el-form-item label="订单号">
            <el-input v-model="searchForm.orderNo" placeholder="请输入订单号" clearable />
          </el-form-item>
          <el-form-item label="订单状态">
            <el-select v-model="searchForm.status" placeholder="请选择订单状态" clearable>
              <el-option
                v-for="item in orderStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="楼层">
            <el-select v-model="searchForm.floor" placeholder="请选择楼层" clearable>
              <el-option
                v-for="item in floorOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="form-item-group">
          <el-form-item label="预订日期" class="date-range-item">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item class="search-buttons">
            <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="resetForm">重置</el-button>
            <el-button type="success" :icon="Download" @click="handleExport">导出</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-card>

    <!-- 列表视图 -->
    <el-card class="table-card" :body-style="{ padding: '20px' }">
      <div class="table-container" :element-loading-text="'加载中...'" element-loading-background="rgba(255, 255, 255, 0.7)">
        <el-table :data="currentPageData" border style="width: 100%" :loading="loading">
          <!-- 表格列定义 -->
          <el-table-column prop="orderNo" label="订单号" width="150" />
          <el-table-column prop="roomType" label="房型" width="120" />
          <el-table-column prop="roomNumber" label="房间号" width="100" />
          <el-table-column prop="guestName" label="客人姓名" width="120" />
          <el-table-column prop="phone" label="联系电话" width="120" />
          <el-table-column prop="checkInDate" label="入住日期" width="120" />
          <el-table-column prop="checkOutDate" label="退房日期" width="120" />
          <el-table-column prop="totalAmount" label="总金额" width="120">
            <template #default="{ row }">
              ¥{{ row.totalAmount.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180" />
          <el-table-column label="操作" fixed="right" width="200">
            <template #default="{ row }">
              <el-button :icon="View" link type="primary" @click="handleView(row)">查看</el-button>
              <el-button v-if="row.status === OrderStatus.Pending" link type="success" @click="handleConfirm(row)">确认</el-button>
              <el-button v-if="row.status === OrderStatus.Confirmed" link type="info" @click="handleComplete(row)">完成</el-button>
              <el-button v-if="row.status === OrderStatus.Pending || row.status === OrderStatus.Confirmed" link type="danger" @click="handleCancel(row)">取消</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="page.current"
            v-model:page-size="page.size"
            :total="page.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="订单详情"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单号">{{ currentOrder.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="房型">{{ currentOrder.roomType }}</el-descriptions-item>
        <el-descriptions-item label="房间号">{{ currentOrder.roomNumber }}</el-descriptions-item>
        <el-descriptions-item label="客人姓名">{{ currentOrder.guestName }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ currentOrder.phone }}</el-descriptions-item>
        <el-descriptions-item label="入住日期">{{ currentOrder.checkInDate }}</el-descriptions-item>
        <el-descriptions-item label="退房日期">{{ currentOrder.checkOutDate }}</el-descriptions-item>
        <el-descriptions-item label="总金额">¥{{ currentOrder.totalAmount?.toFixed(2) }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(currentOrder.status!)">
            {{ getStatusText(currentOrder.status!) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentOrder.createTime }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ currentOrder.remark || '-' }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Download, View } from '@element-plus/icons-vue'
import { 
  OrderStatus, 
  RoomOrderInfo, 
  orderList as initialOrderList,
  orderStatusOptions,
  floorOptions,
  getStatusType,
  getStatusText
} from '@/views/tourism/data'

// 搜索表单
const searchForm = reactive({
  orderNo: '',
  status: undefined as undefined | OrderStatus,
  dateRange: [] as string[],
  floor: undefined as undefined | string
})

// 分页配置
const page = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 加载状态
const loading = ref(false)
const dialogVisible = ref(false)
const currentOrder = ref<Partial<RoomOrderInfo>>({})

// 订单数据
const orderList = ref<RoomOrderInfo[]>(initialOrderList)

// 计算属性：当前页的订单数据
const currentPageData = computed(() => {
  const filteredOrders = orderList.value.filter(order => {
    // 订单号筛选
    if (searchForm.orderNo && !order.orderNo.includes(searchForm.orderNo)) {
      return false
    }
    // 状态筛选
    if (searchForm.status !== undefined && searchForm.status !== null && order.status !== searchForm.status) {
      return false
    }
    // 楼层筛选
    if (searchForm.floor && order.roomNumber.substring(0, 2) !== searchForm.floor) {
      return false
    }
    // 日期范围筛选
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      const [startDate, endDate] = searchForm.dateRange
      const orderStart = new Date(order.checkInDate)
      const orderEnd = new Date(order.checkOutDate)
      const filterStart = new Date(startDate)
      const filterEnd = new Date(endDate)
      
      if (orderStart > filterEnd || orderEnd < filterStart) {
        return false
      }
    }
    return true
  })

  // 更新总数
  page.total = filteredOrders.length

  // 计算分页
  const start = (page.current - 1) * page.size
  const end = start + page.size
  return filteredOrders.slice(start, end)
})

// 搜索
const handleSearch = () => {
  loading.value = true
  // 重置页码到第一页
  page.current = 1
  setTimeout(() => {
    loading.value = false
  }, 300)
}

// 重置表单
const resetForm = () => {
  searchForm.orderNo = ''
  searchForm.status = undefined
  searchForm.dateRange = []
  searchForm.floor = undefined
  page.current = 1
}

// 导出订单
const handleExport = () => {
  const exportData = currentPageData.value.map(order => ({
    订单号: order.orderNo,
    房间类型: order.roomType,
    房间号: order.roomNumber,
    入住人: order.guestName,
    联系电话: order.phone,
    入住日期: order.checkInDate,
    退房日期: order.checkOutDate,
    订单金额: order.totalAmount,
    订单状态: getStatusText(order.status),
    创建时间: order.createTime
  }))
  
  // 这里添加导出逻辑，可以使用第三方库如 xlsx
  console.log('导出数据:', exportData)
  ElMessage.success('订单数据导出成功')
}

// 查看详情
const handleView = (row: RoomOrderInfo) => {
  currentOrder.value = row
  dialogVisible.value = true
}

// 确认订单
const handleConfirm = async (row: RoomOrderInfo) => {
  try {
    loading.value = true
    // 检查房间可用性
    if (checkRoomAvailability(row.roomNumber, row.checkInDate, row.checkOutDate, row.id)) {
      // 更新订单状态
      row.status = OrderStatus.Confirmed
      ElMessage.success('订单确认成功')
    } else {
      ElMessage.error('该房间在所选时间段内已被预订')
    }
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    loading.value = false
  }
}

// 取消订单
const handleCancel = async (row: RoomOrderInfo) => {
  try {
    await ElMessageBox.confirm(
      `是否确认取消订单号为 ${row.orderNo} 的订单？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    loading.value = true
    row.status = OrderStatus.Cancelled
    ElMessage.success('订单已取消')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 完成订单
const handleComplete = async (row: RoomOrderInfo) => {
  try {
    loading.value = true
    row.status = OrderStatus.Completed
    ElMessage.success('订单已完成')
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    loading.value = false
  }
}

// 检查房间是否在指定日期范围内已被预订
const checkRoomAvailability = (roomNumber: string, startDate: string, endDate: string, excludeOrderId?: number) => {
  return orderList.value.some(order => {
    // 排除当前订单（用于编辑订单时）
    if (excludeOrderId && order.id === excludeOrderId) {
      return false
    }
    
    // 只检查已确认的订单（状态为2）和待确认的订单（状态为1）
    if (order.roomNumber === roomNumber && (order.status === 1 || order.status === 2)) {
      // 检查日期是否重叠
      const orderStart = new Date(order.checkInDate)
      const orderEnd = new Date(order.checkOutDate)
      const newStart = new Date(startDate)
      const newEnd = new Date(endDate)
      
      return (newStart < orderEnd && newEnd > orderStart)
    }
    return false
  })
}

// 分页处理
const handleSizeChange = (val: number) => {
  page.size = val
  page.current = 1 // 切换每页显示数量时重置到第一页
}

const handleCurrentChange = (val: number) => {
  page.current = val
}

// 初始化
handleSearch()
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);

  .search-card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    background-color: #fff;

    .search-form {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .form-item-group {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: flex-start;

        @media screen and (max-width: 1400px) {
          flex-direction: column;
          gap: 12px;
        }
      }

      :deep(.el-form-item) {
        margin: 0;

        .el-input,
        .el-select {
          width: 240px;
        }

        &.date-range-item {
          .el-date-editor {
            width: 400px;
          }
        }

        &.search-buttons {
          margin-left: auto;
          display: flex;
          gap: 8px;

          @media screen and (max-width: 1400px) {
            margin-left: 0;
            width: 100%;
            display: flex;
            justify-content: flex-end;
          }

          .el-button {
            margin: 0;
            min-width: 88px;
            
            &:not(:first-child) {
              margin-left: 8px;
            }
          }
        }

        .el-form-item__label {
          font-weight: 500;
          color: #606266;
          padding-right: 12px;
        }
      }
    }
  }

  .table-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .table-container {
      :deep(.el-table) {
        border-radius: 8px;
        overflow: hidden;

        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: 600;
          height: 48px;
        }

        td {
          padding: 12px 0;
        }

        .el-table__row {
          transition: all 0.3s;

          &:hover {
            background-color: #f5f7fa;
          }
        }

        .el-tag {
          border-radius: 4px;
          padding: 2px 8px;
          
          &.el-tag--warning {
            background-color: #fdf6ec;
            border-color: #faecd8;
            color: #e6a23c;
          }
          
          &.el-tag--success {
            background-color: #f0f9eb;
            border-color: #e1f3d8;
            color: #67c23a;
          }
          
          &.el-tag--danger {
            background-color: #fef0f0;
            border-color: #fde2e2;
            color: #f56c6c;
          }
          
          &.el-tag--info {
            background-color: #f4f4f5;
            border-color: #e9e9eb;
            color: #909399;
          }
        }

        .operation-column {
          .el-button {
            padding: 4px 8px;
            
            &.el-button--primary {
              color: #409eff;
            }
            
            &.el-button--success {
              color: #67c23a;
            }
            
            &.el-button--danger {
              color: #f56c6c;
            }
            
            &.el-button--info {
              color: #909399;
            }
          }
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      padding: 10px 20px;
      text-align: right;
      background-color: #fff;
      border-top: 1px solid #f0f0f0;
    }
  }
}

// 对话框样式
:deep(.el-dialog) {
  border-radius: 8px;
  overflow: hidden;

  .el-dialog__header {
    margin: 0;
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #f5f7fa;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .el-dialog__body {
    padding: 24px;
  }

  .el-descriptions {
    .el-descriptions__label {
      font-weight: 600;
      color: #606266;
    }

    .el-descriptions__content {
      color: #303133;
    }
  }

  .dialog-footer {
    margin-top: 24px;
    text-align: right;
  }
}
</style>

