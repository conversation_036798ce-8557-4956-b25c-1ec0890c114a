<template>
  <div class="app-container">
   <el-card>
    <!-- ********************** 用插槽显示筛选框 ********************** -->
    <template #header>
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
                <el-form-item label="$fieldConfig.fieldComment" prop="code">
                      <el-input
                          v-model="queryParams.code"
                          placeholder="$fieldConfig.fieldComment"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="fhbs">
                      <el-input
                          v-model="queryParams.fhbs"
                          placeholder="$fieldConfig.fieldComment"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="是否定点(1-定点 0-非定点)" prop="sfdd">
                      <el-input
                          v-model="queryParams.sfdd"
                          placeholder="是否定点(1-定点 0-非定点)"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
                <el-form-item label="去掉尾号0的短编码" prop="dcode">
                      <el-input
                          v-model="queryParams.dcode"
                          placeholder="去掉尾号0的短编码"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon><Search /></template>
            搜索
          </el-button>
          <el-button @click="handleResetQuery">
            <template #icon><Refresh /></template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="列表">
        <div>
          <el-button
              v-hasPerm="['cg:sysCgml:add']"
              type="success"
              plain
              size="small"
              @click="formType.view = false; handleEdit()"
          >
            <template #icon><Plus /></template>
            新增
          </el-button>
          <el-button
              v-hasPerm="['cg:sysCgml:delete']"
              type="danger"
              plain
              size="small"
              :disabled="removeIds.length === 0"
              @click="handleDelete()"
          >
            <template #icon><Delete /></template>
            删除
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
          ref="dataTableRef"
          v-loading="loading"
          :data="pageData"
          highlight-current-row
          border
          @selection-change="handleSelectionChange"
          @expand-change="handleExpandChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed />
        <el-table-column label="展开" type="expand" width="55">
          <template #default="props">
            <div style="padding: 0px 70px; background-color: #ffffff">
              <!-- <TransferPropertiesList
                :dcbm="props.row.dcbm"
                :guid="props.row.guid || ''"
              ></TransferPropertiesList> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
                <el-table-column
                    key="id"
                    label="$fieldConfig.fieldComment"
                    prop="id"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="guid"
                    label="$fieldConfig.fieldComment"
                    prop="guid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="code"
                    label="$fieldConfig.fieldComment"
                    prop="code"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ycode"
                    label="省财对应采购目录"
                    prop="ycode"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="name"
                    label="$fieldConfig.fieldComment"
                    prop="name"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="remark"
                    label="$fieldConfig.fieldComment"
                    prop="remark"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sort"
                    label="$fieldConfig.fieldComment"
                    prop="sort"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="flag"
                    label="$fieldConfig.fieldComment"
                    prop="flag"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="role"
                    label="$fieldConfig.fieldComment"
                    prop="role"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xebz"
                    label="$fieldConfig.fieldComment"
                    prop="xebz"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="xzje"
                    label="$fieldConfig.fieldComment"
                    prop="xzje"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cwcode"
                    label="$fieldConfig.fieldComment"
                    prop="cwcode"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cwname"
                    label="$fieldConfig.fieldComment"
                    prop="cwname"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="cgfs"
                    label="$fieldConfig.fieldComment"
                    prop="cgfs"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zzxs"
                    label="$fieldConfig.fieldComment"
                    prop="zzxs"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="fhbs"
                    label="$fieldConfig.fieldComment"
                    prop="fhbs"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zssblb"
                    label="中山竞价设备类别"
                    prop="zssblb"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="zsjfkm"
                    label="中山竞价经费科目"
                    prop="zsjfkm"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sfjc"
                    label="是否集采(1-集采 0-非集采)"
                    prop="sfjc"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="sfdd"
                    label="是否定点(1-定点 0-非定点)"
                    prop="sfdd"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ddje"
                    label="定点采购的金额大于多少要上传调研报告"
                    prop="ddje"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="dcode"
                    label="去掉尾号0的短编码"
                    prop="dcode"
                    min-width="150"
                    align="center"
                />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button
                v-hasPerm="['cg:sysCgml:edit']"
                type="primary"
                size="small"
                icon="Edit"
                link
                @click="formType.view = false;handleRowEdit(scope.row.id)"
            >
              编辑
            </el-button>
            <el-button
                v-hasPerm="['cg:sysCgml:delete']"
                type="danger"
                size="small"
                icon="Delete"
                link
                @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
            <el-button
              v-if="check"
              type="primary"
              icon="DocumentChecked"
              size="small"
              link
              @click="hancleRowCheck"
            >
              审核
            </el-button>
            <el-button
              v-if="view"
              type="success"
              icon="Document"
              size="small"
              link
              @click="hancleRowView"
            >
              查看
            </el-button>
            <el-button
              v-if="view"
              type="primary"
              icon="Printer"
              size="small"
              link
              @click="formType.view = true;hancleRowPrint"
            >
              打印
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="handleQuery()"
      />
    </el-card>

    <!-- 采购目录表单弹窗 -->   
    <el-drawer 
      v-model="dialog.visible" 
      :title="dialog.title" 
      append-to-body 
      size="75%"
      :before-close="handleCloseDialog"

    >
<el-card>
        <template #header>
          <Title name="XX信息">
            <div>
              <el-button
                v-if="!formType.view"
                type="primary"
                @click="handleSubmit()"
              >
                保存
              </el-button>
              <el-button v-if="!formType.view" type="danger" @click="handleSubmit()">
                提交
              </el-button>
            </div>
          </Title>
        </template>



      <el-form ref="dataFormRef" :model="formData" :rules="rules" label-width="100px"  :inline="true"
          :disabled="formType.view">
                <el-form-item label="$fieldConfig.fieldComment" prop="id">
                      <el-input
                          v-model="formData.id"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="guid">
                      <el-input
                          v-model="formData.guid"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="code">
                      <el-input
                          v-model="formData.code"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="省财对应采购目录" prop="ycode">
                      <el-input
                          v-model="formData.ycode"
                          placeholder="省财对应采购目录"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="name">
                      <el-input
                          v-model="formData.name"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="remark">
                      <el-input
                          v-model="formData.remark"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="sort">
                      <el-input
                          v-model="formData.sort"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="flag">
                      <el-input
                          v-model="formData.flag"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="role">
                      <el-input
                          v-model="formData.role"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="xebz">
                      <el-input
                          v-model="formData.xebz"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="xzje">
                      <el-input
                          v-model="formData.xzje"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="cwcode">
                      <el-input
                          v-model="formData.cwcode"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="cwname">
                      <el-input
                          v-model="formData.cwname"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="cgfs">
                      <el-input
                          v-model="formData.cgfs"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="zzxs">
                      <el-input
                          v-model="formData.zzxs"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="$fieldConfig.fieldComment" prop="fhbs">
                      <el-input
                          v-model="formData.fhbs"
                          placeholder="$fieldConfig.fieldComment"
                      />
                </el-form-item>
                <el-form-item label="中山竞价设备类别" prop="zssblb">
                      <el-input
                          v-model="formData.zssblb"
                          placeholder="中山竞价设备类别"
                      />
                </el-form-item>
                <el-form-item label="中山竞价经费科目" prop="zsjfkm">
                      <el-input
                          v-model="formData.zsjfkm"
                          placeholder="中山竞价经费科目"
                      />
                </el-form-item>
                <el-form-item label="是否集采(1-集采 0-非集采)" prop="sfjc">
                      <el-input
                          v-model="formData.sfjc"
                          placeholder="是否集采(1-集采 0-非集采)"
                      />
                </el-form-item>
                <el-form-item label="是否定点(1-定点 0-非定点)" prop="sfdd">
                      <el-input
                          v-model="formData.sfdd"
                          placeholder="是否定点(1-定点 0-非定点)"
                      />
                </el-form-item>
                <el-form-item label="定点采购的金额大于多少要上传调研报告" prop="ddje">
                      <el-input
                          v-model="formData.ddje"
                          placeholder="定点采购的金额大于多少要上传调研报告"
                      />
                </el-form-item>
                <el-form-item label="去掉尾号0的短编码" prop="dcode">
                      <el-input
                          v-model="formData.dcode"
                          placeholder="去掉尾号0的短编码"
                      />
                </el-form-item>
      </el-form>
      <template #footer></template>
     </el-card>
    </ el-drawer>
  </div>
</template>

<script setup lang="ts">
  defineOptions({
    name: "SysCgml",
    inheritAttrs: false,
  });
  import { useUserStore } from "@/store";
  import SysCgmlAPI, { SysCgmlPageVO, SysCgmlForm, SysCgmlPageQuery } from "@/api/cg/sys-cgml";

  // 获取路由参数,view/add/check
  const route = useRoute();
  const type = ref(route.query.type?.toString() || "view");
  //显示隐藏权限,用于v-if
  const add = type.value == "add";
  const view = type.value == "view";
  const check = type.value == "check";
  //页面参数
  const queryFormRef = ref(ElForm);
  const loading = ref(false);
  const removeIds = ref<number[]>([]);
  const total = ref(0);

  const queryParams = reactive<SysCgmlPageQuery>({
    pageNum: 1,
    pageSize: 10,
  });

  // 采购目录表格数据
  const pageData = ref<SysCgmlPageVO[]>([]);

  // 弹窗
  const dialog = reactive({
    title: "",
    visible: false,
  });



  /** 查询采购目录 */
  function handleQuery() {
    loading.value = true;
          SysCgmlAPI.getPage(queryParams)
        .then((data) => {
          pageData.value = data.list;
          total.value = data.total;
        })
        .finally(() => {
          loading.value = false;
        });
  }

  /** 重置采购目录查询 */
  function handleResetQuery() {
    queryFormRef.value!.resetFields();
    queryParams.pageNum = 1;
    handleQuery();
  }

  /** 行复选框选中记录选中ID集合 */
  function handleSelectionChange(selection: any) {
    removeIds.value = selection.map((item: any) => item.id);
  }

  /** 打开采购目录弹窗 */
  function handleEdit(id?: number) {
    dialog.visible = true;
    if (id) {
      dialog.title = "修改采购目录";
            SysCgmlAPI.getFormData(id).then((data) => {
        Object.assign(formData, data);
      });
    } else {
      dialog.title = "新增采购目录";
    }
  }

 

  /** 关闭采购目录弹窗 */
  function handleCloseDialog() {
    dialog.visible = false;
    dataFormRef.value.resetFields();
    dataFormRef.value.clearValidate();
    Object.assign(formData, {});

  }

  /** 删除采购目录 */
  function handleDelete(id?: number) {
    const ids = [id || removeIds.value].join(",");
    if (!ids) {
      ElMessage.warning("请勾选删除项");
      return;
    }

    ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(
        () => {
          loading.value = true;
                SysCgmlAPI.deleteByIds(ids)
              .then(() => {
                ElMessage.success("删除成功");
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        },
        () => {
          ElMessage.info("已取消删除");
        }
    );
  }

  onMounted(() => {
    handleQuery();
  });

  //--------------以下是form表单相关
const dataFormRef = ref(ElForm);

  // 采购目录表单数据
  const formData = reactive<SysCgmlForm>({});

const formType = reactive({
  add: false,
  edit: false,
  view: false,
});

  // 采购目录表单校验规则
  const rules = reactive({
                      guid: [{ required: true, message: "请输入${fieldConfig.fieldComment}", trigger: "blur" }],
                      code: [{ required: true, message: "请输入${fieldConfig.fieldComment}", trigger: "blur" }],
                      ycode: [{ required: true, message: "请输入省财对应采购目录", trigger: "blur" }],
                      name: [{ required: true, message: "请输入${fieldConfig.fieldComment}", trigger: "blur" }],
                      sort: [{ required: true, message: "请输入${fieldConfig.fieldComment}", trigger: "blur" }],
                      flag: [{ required: true, message: "请输入${fieldConfig.fieldComment}", trigger: "blur" }],
                      dcode: [{ required: true, message: "请输入去掉尾号0的短编码", trigger: "blur" }],
  });

   /** 提交采购目录表单 */
  function handleSubmit() {
    dataFormRef.value.validate((valid: any) => {
      if (valid) {
        loading.value = true;
        const id = formData.id;
        if (id) {
                SysCgmlAPI.update(id, formData)
              .then(() => {
                ElMessage.success("修改成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        } else {
                SysCgmlAPI.add(formData)
              .then(() => {
                ElMessage.success("新增成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        }
      }
    });
  }
</script>
<style lang="scss" scoped>

</style>
