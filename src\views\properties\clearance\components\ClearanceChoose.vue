<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item label="处置单号" prop="czbh">
            <el-input v-model="queryParams.czbh" placeholder="处置单号" />
          </el-form-item>
          <el-form-item label="处置方式" prop="czlx">
            <DDLYcode v-model="queryParams.czlx" ycode="0102" />
          </el-form-item>
          <el-form-item label="申请部门" prop="djbm">
            <DDLDeptList v-model="queryParams.djbm" />
          </el-form-item>
          <el-form-item label="申请人" prop="djr">
            <el-input v-model="queryParams.djr" placeholder="申请人" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon>
                <Search />
              </template>
              搜索
            </el-button>
            <!-- <el-button @click="handleResetQuery">
              <template #icon>
                <Refresh />
              </template>
              重置
            </el-button> -->
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="待受理处置单列表">
        <div>
          <el-button
            v-if="add"
            :disabled="selectedValues.length === 0"
            type="success"
            plain
            @click="handleAddDispose"
          >
            <template #icon><Plus /></template>
            创建受理单
          </el-button>

          <el-button
            v-if="edit"
            :disabled="selectedValues.length === 0"
            type="danger"
            plain
            @click="handleEditDispose"
          >
            <template #icon><Plus /></template>
            选择
          </el-button>
        </div>
      </Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        stripe
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" fixed />
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="处置单号" prop="czbh" width="130" align="center" fixed />
        <el-table-column label="处置方式" prop="czlxname" width="150" align="center" fixed />
        <el-table-column label="处置金额（元）" prop="czje" width="150" align="center" />
        <el-table-column label="资产数量" prop="czsl" width="100" align="center" />
        <el-table-column label="申请部门" prop="djbmname" width="150" align="center" />
        <el-table-column label="申请人" prop="djrname" width="150" align="center" />
        <el-table-column label="申请时间" prop="djsj" width="150" align="center">
          <template #default="scope">
            <span>{{ format(scope.row.djsj, "YYYY-MM-DD") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="处置说明" prop="czyy" width="300" align="center" />

        <el-table-column label="操作" fixed="right" width="180">
          <template #default="scope">
            <el-button
              type="success"
              icon="Document"
              size="small"
              @click="handleRowView(scope.row)"
            >
              查看
            </el-button>

            <el-button
              v-if="add"
              type="danger"
              icon="Close"
              size="small"
              @click="handleOpenRowAfter(scope.row)"
            >
              退回
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :page-size="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 查看处置单详情 -->
    <el-drawer v-model="dialogDisposeVisible" :with-header="false" append-to-body size="75%">
      <DisposeView
        :key="itemGuid + '1'"
        :guid="itemGuid"
        :editable="false"
        :netcode="itemNetcode"
        :checkstatus="1"
        :RefreshFatherDrawer="handleCloseDialog"
      />
    </el-drawer>

    <!-- 退回明细 -->
    <el-drawer
      v-model="dialogAfterVisible"
      :with-header="false"
      append-to-body
      size="75%"
      @close="handleQuery()"
    >
      <ClearanceAfter :key="itemGuid + 2" :guid="itemGuid" />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import clearAPI from "@/api/properties/clearance";
import { useRouter } from "vue-router";
import DisposeView from "@/views/properties/dispose/components/DisposeView.vue";
import ClearanceAfter from "./ClearanceAfter.vue";
import { format } from "@/utils/day";

const props = defineProps({
  //两个枚举值，add/edit，add用于创建受理单时候的列表页面，edit用于编辑受理单时的添加明细
  type: {
    type: String,
    required: true,
  },
  guid: {
    type: String,
  },
});
const add = props.type == "add";
const edit = props.type == "edit";

// 获取路由参数,view/add/check
const router = useRouter();
//页面参数
const queryFormRef = ref(ElForm);
const loading = ref(false);
const selectedValues = ref<number[]>([]);
const total = ref(0);
//校验项
// const rules = reactive({
//   czlx: [{ required: true, message: "处置类型不能为空", trigger: "change" }],
// });
//请求列表数据
const pageData = ref<any[]>([]);
const queryParams = reactive<any>({
  pageNum: 1,
  pageSize: 20,
});
const handleQuery = () => {
  loading.value = true;
  console.log(queryParams);
  clearAPI
    .getWaitClearPage(queryParams)
    .then((res) => {
      pageData.value = res.list;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
};

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  selectedValues.value = selection.map((item: any) => item.guid);
}

const addClearParams = reactive<any>({
  czguid: "",
});
//创建处置单
const handleAddDispose = () => {
  addClearParams.czguid = selectedValues.value.join(",");
  console.log("addClearParams", addClearParams);
  ElMessageBox.confirm("该操作将受理处置申请，并创建核销单，确认受理？", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    clearAPI.addClear(addClearParams).then((res) => {
      //res是新处置单的guid
      ElMessage.success("核销受理单创建成功，请前往编辑");
      // 跳转到index页面并添加路由参数type为add
      // router.push({ path: "/dispose/edit", query: { type: "add" } });
      handleQuery();
    });
  });
};

const editClearParams = reactive<any>({
  ClearanceGuid: "",
  Disposeguids: "",
});
//编辑受理单
const handleEditDispose = () => {
  editClearParams.Disposeguids = selectedValues.value.join(",");
  editClearParams.ClearanceGuid = props.guid;
  console.log("editClearParams", editClearParams);
  ElMessageBox.confirm("确认添加处置申请至受理单？", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    clearAPI.addClearDetail(editClearParams).then(() => {
      ElMessage.success("操作成功");
      // 跳转到index页面并添加路由参数type为add
      // router.push({ path: "/dispose/edit", query: { type: "add" } });
      handleQuery();
    });
  });
};

//打开退回弹框
const itemGuid = ref("");
const itemNetcode = ref("");
const dialogDisposeVisible = ref(false);
const handleRowView = (row: any) => {
  itemGuid.value = row.guid;
  itemNetcode.value = row.netcode;
  dialogDisposeVisible.value = true;
};
const dialogAfterVisible = ref(false);
const handleOpenRowAfter = (row: any) => {
  itemGuid.value = row.guid;
  dialogAfterVisible.value = true;
};

const handleCloseDialog = () => {
  dialogDisposeVisible.value = false;
  handleQuery();
};

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.el-form {
  width: auto;
}
</style>
