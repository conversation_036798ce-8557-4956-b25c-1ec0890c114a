<template>
  <el-cascader
    v-model="_modelValue"
    class="cascader"
    style="width: 100%"
    :options="options"
    :props="cascaderProps"
    filterable
    clearable
  />
</template>
<script setup lang="ts">
//教育财政分类的下拉框多选
import { jycodes } from "@/assets/JYCode";
import { czcodes } from "@/assets/CZCode";

const props = defineProps({
  // 修改 type 为 Array 并使用函数返回默认值
  modelValue: {
    type: Array,
    default: () => [],
  },
  multiple: { type: Boolean, default: true },
  type: {
    type: String,
    required: true,
  },
});

const cascaderProps = {
  multiple: props.multiple,
  emitPath: false,
  checkStrictly: true,
};

//子传父;
const emit = defineEmits(["update:modelValue"]);
const _modelValue = computed({
  get: () => {
    return props.modelValue;
  },
  set: (val) => {
    emit("update:modelValue", val);
  },
});

const options = shallowRef<CascaderOption[]>([]);

watchEffect(() => {
  options.value = props.type === "JY" ? [...jycodes.value] : [...czcodes.value];
});
</script>
<style scoped></style>
