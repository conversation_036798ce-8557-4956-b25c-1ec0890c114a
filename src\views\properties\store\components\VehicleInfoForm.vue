<template>
  <!-- 车辆信息 -->
  <el-form :model="formData" :rules="rules" label-width="120px" :inline="true" ref="formRef"
    :disabled="!props.editable">
    <el-form-item label="车辆用途" required prop="clyt">
      <DDLXcode v-model="formData.clyt" xcode="020212" clearable />
    </el-form-item>
    <el-form-item label="品牌">
      <el-input v-model="formData.pp" placeholder="请输入品牌" />
    </el-form-item>
    <el-form-item label="规格型号">
      <el-input v-model="formData.ggxh" placeholder="请输入规格型号" />
    </el-form-item>
    <el-form-item label="排气量(升)">
      <el-input v-model="formData.pql" placeholder="请输入排气量" />
    </el-form-item>
    <el-form-item label="车牌号">
      <el-input v-model="formData.cph" placeholder="请输入车牌号" />
    </el-form-item>
    <el-form-item label="发动机号码">
      <el-input v-model="formData.fdjhm" placeholder="请输入发动机号码" />
    </el-form-item>
    <el-form-item label="识别号码(车架号)">
      <el-input v-model="formData.cjh" placeholder="请输入识别号码" />
    </el-form-item>
    <el-form-item label="车辆产地" required prop="clcd">
      <DDLXcode v-model="formData.clcd" xcode="020213" clearable />
    </el-form-item>
    <el-form-item label="车身颜色">
      <el-input v-model="formData.csys" placeholder="请输入车身颜色" />
    </el-form-item>
    <el-form-item label="车辆行驶证所有人">
      <el-input v-model="formData.clxszsyr" placeholder="请输入车辆行驶证所有人" />
    </el-form-item>
    <el-form-item label="编制情况" required prop="bzqk">
      <DDLXcode v-model="formData.bzqk" xcode="020220" clearable />
    </el-form-item>
    <el-form-item label="产权形式" required prop="cqxs">
      <DDLXcode v-model="formData.cqxs" xcode="020206" clearable />
    </el-form-item>
    <el-form-item label="发证时间">
      <el-date-picker v-model="formData.fzsj" type="date" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择发证时间" />
    </el-form-item>
    <el-form-item label="权属证号">
      <el-input v-model="formData.qszh" placeholder="请输入权属证号" />
    </el-form-item>
    <el-form-item label="供应商名称">
      <el-input v-model="formData.gysmc" placeholder="请输入供应商名称" />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { defineProps, reactive, onMounted, ref, watch } from "vue";
import { ElLoading, type FormInstance, type FormRules } from 'element-plus';
import storeAPI, { ZcinfoDifferentForm } from "@/api/properties/store";

// 组件属性定义
const props = defineProps({
  guid: {
    type: String
  },
  zclxbh: {
    type: String
  },
  editable: {
    type: Boolean,
    required: true
  },
})

const formRef = ref<FormInstance>();
// 表单数据
const formData = reactive<ZcinfoDifferentForm>({
  zclxbh: props.zclxbh || '',
})

const rules = reactive<FormRules>({
  clyt: [{ required: true, message: "请选择车辆用途", trigger: "change" }],
  clcd: [{ required: true, message: "请选择车辆产地", trigger: "change" }],
  bzqk: [{ required: true, message: "请选择编制情况", trigger: "change" }],
  cqxs: [{ required: true, message: "请选择产权形式", trigger: "change" }],
});

// 监听值变化
watch(
  () => [props.guid, props.zclxbh],
  (val) => {
    console.log('props:', val)
    if (val && val[0] != '' && val[1] != undefined) {

      console.log('props:', val, val[0], val[1])
      //获取表单数据
      storeAPI.getFormDataDiff({ guid: val[0], zclxbh: val[1] }).then((res) => {
        Object.assign(formData, res)
      })
      formData.zclxbh = val[1]
      formData.guid = val[0]
    }
  },
  { deep: true, immediate: true }
)


// 提交表单
const submitForm = () => {
  return new Promise((resolve, reject) => {
    formRef.value?.validate((valid) => {
      if (valid) {
        // 显示加载状态
        const loading = ElLoading.service({
          lock: true,
          text: '正在保存...',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        storeAPI.updateDiff(formData).then((res) => {
          // 关闭加载状态
          loading.close()
          // 显示成功消息
          ElMessage({
            type: 'success',
            message: '保存成功'
          })
          console.log('表单提交成功:', res)
          resolve(res)
        }).catch(error => {
          // 关闭加载状态
          loading.close()
          // 显示错误消息
          ElMessage({
            type: 'error',
            message: '保存失败: ' + (error.message || '未知错误')
          })
          console.error('表单提交失败:', error)
          reject(error)
        })
      } else {
        ElMessage({
          type: 'warning',
          message: '请填写必填项'
        })
        reject(new Error('表单验证失败'))
        return false
      }
    })
  })
}


// 表单验证方法
const validateForm = () => {
  return new Promise((resolve, reject) => {
    formRef.value?.validate((valid) => {
      if (valid) {
        resolve(true)
      } else {
        reject(new Error('表单验证失败'))
      }
    })
  })
}


// 导出方法供父组件使用
defineExpose({
  submitForm,
  validate: validateForm
})

</script>

<style scoped>
.el-form-item {
  width: 330px;
}
</style>