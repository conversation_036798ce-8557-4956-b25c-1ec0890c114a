<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-card class="search-card" :body-style="{ padding: '24px' }">
      <el-form :model="searchForm" inline class="search-form">
        <div class="form-item-group">
          <el-form-item label="景点名称">
            <el-input v-model="searchForm.name" placeholder="请输入景点名称" clearable />
          </el-form-item>
          <el-form-item label="门票状态">
            <el-select v-model="searchForm.status" placeholder="请选择门票状态" clearable>
              <el-option
                v-for="item in ticketStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="价格区间">
            <el-input-number
              v-model="searchForm.minPrice"
              :min="0"
              placeholder="最低价"
              style="width: 120px"
            />
            <span class="separator">-</span>
            <el-input-number
              v-model="searchForm.maxPrice"
              :min="0"
              placeholder="最高价"
              style="width: 120px"
            />
          </el-form-item>
        </div>
        <div class="form-item-group">
          <el-form-item class="search-buttons">
            <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="resetForm">重置</el-button>
            <el-button type="success" :icon="Plus" @click="handleAdd">新增门票</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-card>

    <!-- 列表视图 -->
    <el-card class="table-card" :body-style="{ padding: '20px' }">
      <div class="table-container">
        <el-table :data="currentPageData" border style="width: 100%" :loading="loading">
          <el-table-column prop="name" label="景点名称" min-width="180" />
          <el-table-column prop="price" label="门票价格" width="120">
            <template #default="{ row }">
              <div class="price-info">
                <span class="current-price">¥{{ row.price }}</span>
                <span v-if="row.originalPrice > row.price" class="original-price">
                  ¥{{ row.originalPrice }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="stock" label="库存" width="100" />
          <el-table-column prop="soldCount" label="已售" width="100" />
          <el-table-column prop="validityPeriod" label="有效期" width="120">
            <template #default="{ row }">{{ row.validityPeriod }}天</template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getTicketStatusType(row.status)">
                {{ getTicketStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间" width="180" />
          <el-table-column label="操作" fixed="right" width="250">
            <template #default="{ row }">
              <el-button :icon="View" link type="primary" @click="handleView(row)">查看</el-button>
              <el-button :icon="Edit" link type="primary" @click="handleEdit(row)">编辑</el-button>
              <el-button
                v-if="row.status === TicketStatus.OffSale"
                link
                type="success"
                @click="handleStatusChange(row, TicketStatus.OnSale)"
              >
                上架
              </el-button>
              <el-button
                v-if="row.status === TicketStatus.OnSale"
                link
                type="info"
                @click="handleStatusChange(row, TicketStatus.OffSale)"
              >
                下架
              </el-button>
              <el-button
                v-if="row.status === TicketStatus.OffSale"
                link
                type="danger"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="page.current"
            v-model:page-size="page.size"
            :total="page.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'view' ? '景点详情' : dialogType === 'add' ? '新增门票' : '编辑门票'"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="ticket-form"
      >
        <el-form-item label="景点名称" prop="name">
          <el-input v-model="formData.name" :disabled="dialogType === 'view'" />
        </el-form-item>
        <el-form-item label="景点描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            :disabled="dialogType === 'view'"
          />
        </el-form-item>
        <el-form-item label="门票价格" prop="price">
          <el-input-number
            v-model="formData.price"
            :min="0"
            :precision="2"
            :disabled="dialogType === 'view'"
          />
        </el-form-item>
        <el-form-item label="原价" prop="originalPrice">
          <el-input-number
            v-model="formData.originalPrice"
            :min="0"
            :precision="2"
            :disabled="dialogType === 'view'"
          />
        </el-form-item>
        <el-form-item label="库存" prop="stock">
          <el-input-number v-model="formData.stock" :min="0" :disabled="dialogType === 'view'" />
        </el-form-item>
        <el-form-item label="有效期" prop="validityPeriod">
          <el-input-number
            v-model="formData.validityPeriod"
            :min="1"
            :disabled="dialogType === 'view'"
          >
            <template #suffix>天</template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="景点地址" prop="address">
          <el-input v-model="formData.address" :disabled="dialogType === 'view'" />
        </el-form-item>
        <el-form-item label="开放时间" prop="openTime">
          <el-input v-model="formData.openTime" :disabled="dialogType === 'view'" />
        </el-form-item>
        <el-form-item label="使用须知" prop="notice">
          <el-input
            v-model="formData.notice"
            type="textarea"
            :rows="4"
            :disabled="dialogType === 'view'"
          />
        </el-form-item>
        <el-form-item label="景点图片" prop="images">
          <el-upload
            v-if="dialogType !== 'view'"
            action="/api/upload"
            list-type="picture-card"
            :file-list="imageList"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :on-success="handleUploadSuccess"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
          <div v-else class="image-preview">
            <el-image
              v-for="(url, index) in formData.images"
              :key="index"
              :src="url"
              :preview-src-list="formData.images"
              fit="cover"
              class="preview-image"
            />
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button v-if="dialogType !== 'view'" type="primary" @click="handleSubmit">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 图片预览 -->
    <el-dialog v-model="previewVisible" width="800px">
      <img w-full :src="previewUrl" alt="Preview Image" style="max-width: 100%" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search, Refresh, Plus, View, Edit } from "@element-plus/icons-vue";
import {
  TicketStatus,
  TicketInfo,
  ticketStatusOptions,
  getTicketStatusType,
  getTicketStatusText,
  mockTickets,
} from "@/views/tourism/data";

// 搜索表单
const searchForm = reactive({
  name: "",
  status: undefined as undefined | TicketStatus,
  minPrice: undefined as undefined | number,
  maxPrice: undefined as undefined | number,
});

// 分页配置
const page = reactive({
  current: 1,
  size: 10,
  total: 0,
});

// 表单数据
const formData = reactive<Partial<TicketInfo>>({});
const formRef = ref();
const formRules = {
  name: [{ required: true, message: "请输入景点名称", trigger: "blur" }],
  description: [{ required: true, message: "请输入景点描述", trigger: "blur" }],
  price: [{ required: true, message: "请输入门票价格", trigger: "blur" }],
  originalPrice: [{ required: true, message: "请输入原价", trigger: "blur" }],
  stock: [{ required: true, message: "请输入库存", trigger: "blur" }],
  validityPeriod: [{ required: true, message: "请输入有效期", trigger: "blur" }],
  address: [{ required: true, message: "请输入景点地址", trigger: "blur" }],
  openTime: [{ required: true, message: "请输入开放时间", trigger: "blur" }],
  notice: [{ required: true, message: "请输入使用须知", trigger: "blur" }],
  images: [{ required: true, message: "请上传景点图片", trigger: "change" }],
};

// 状态变量
const loading = ref(false);
const dialogVisible = ref(false);
const dialogType = ref<"add" | "edit" | "view">("add");
const previewVisible = ref(false);
const previewUrl = ref("");

// 定义图片列表的接口
interface UploadFile {
  name: string;
  url: string;
}

const imageList = ref<UploadFile[]>([]);

// 数据列表
const ticketList = ref<TicketInfo[]>(mockTickets);

// 计算当前页数据
const currentPageData = computed(() => {
  const filteredData = ticketList.value.filter((item) => {
    if (searchForm.name && !item.name.includes(searchForm.name)) {
      return false;
    }
    if (searchForm.status !== undefined && item.status !== searchForm.status) {
      return false;
    }
    if (searchForm.minPrice !== undefined && item.price < searchForm.minPrice) {
      return false;
    }
    if (searchForm.maxPrice !== undefined && item.price > searchForm.maxPrice) {
      return false;
    }
    return true;
  });

  page.total = filteredData.length;
  const start = (page.current - 1) * page.size;
  const end = start + page.size;
  return filteredData.slice(start, end);
});

// 搜索
const handleSearch = () => {
  loading.value = true;
  page.current = 1;
  setTimeout(() => {
    loading.value = false;
  }, 300);
};

// 重置表单
const resetForm = () => {
  searchForm.name = "";
  searchForm.status = undefined;
  searchForm.minPrice = undefined;
  searchForm.maxPrice = undefined;
  page.current = 1;
  handleSearch();
};

// 新增门票
const handleAdd = () => {
  dialogType.value = "add";
  Object.assign(formData, {
    name: "",
    description: "",
    price: 0,
    originalPrice: 0,
    stock: 0,
    validityPeriod: 1,
    status: TicketStatus.OffSale,
    notice: "",
    images: [],
    address: "",
    openTime: "",
  });
  imageList.value = [];
  dialogVisible.value = true;
};

// 查看详情
const handleView = (row: TicketInfo) => {
  dialogType.value = "view";
  Object.assign(formData, row);
  dialogVisible.value = true;
};

// 编辑门票
const handleEdit = (row: TicketInfo) => {
  dialogType.value = "edit";
  Object.assign(formData, row);
  imageList.value = row.images.map((url) => ({
    url,
    name: url.split("/").pop() || url,
  }));
  dialogVisible.value = true;
};

// 修改状态
const handleStatusChange = async (row: TicketInfo, status: TicketStatus) => {
  try {
    await ElMessageBox.confirm(
      `确定要${status === TicketStatus.OnSale ? "上架" : "下架"}该门票吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );
    loading.value = true;
    row.status = status;
    ElMessage.success(`${status === TicketStatus.OnSale ? "上架" : "下架"}成功`);
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("操作失败");
    }
  } finally {
    loading.value = false;
  }
};

// 删除门票
const handleDelete = async (row: TicketInfo) => {
  try {
    await ElMessageBox.confirm("确定要删除该门票吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    loading.value = true;
    const index = ticketList.value.findIndex((item) => item.id === row.id);
    if (index !== -1) {
      ticketList.value.splice(index, 1);
      ElMessage.success("删除成功");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
    }
  } finally {
    loading.value = false;
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.value = true;

    if (dialogType.value === "add") {
      const newTicket: TicketInfo = {
        id: ticketList.value.length + 1,
        ...(formData as any),
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        soldCount: 0,
      };
      ticketList.value.unshift(newTicket);
      ElMessage.success("添加成功");
    } else {
      const index = ticketList.value.findIndex((item) => item.id === formData.id);
      if (index !== -1) {
        ticketList.value[index] = {
          ...ticketList.value[index],
          ...(formData as any),
          updateTime: new Date().toISOString(),
        };
        ElMessage.success("更新成功");
      }
    }
    dialogVisible.value = false;
  } catch (error) {
    ElMessage.error("请填写完整的表单信息");
  } finally {
    loading.value = false;
  }
};

// 图片上传相关方法
const handlePictureCardPreview = (file: any) => {
  previewUrl.value = file.url;
  previewVisible.value = true;
};

const handleRemove = (file: any) => {
  const index = imageList.value.indexOf(file);
  if (index !== -1) {
    imageList.value.splice(index, 1);
    formData.images = imageList.value.map((item) => item.url);
  }
};

const handleUploadSuccess = (response: any, file: any) => {
  imageList.value.push({
    name: file.name,
    url: response.url,
  });
  formData.images = imageList.value.map((item) => item.url);
};

// 分页处理
const handleSizeChange = (val: number) => {
  page.size = val;
  page.current = 1;
};

const handleCurrentChange = (val: number) => {
  page.current = val;
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);

  .search-card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .search-form {
      .form-item-group {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: flex-start;

        .separator {
          margin: 0 8px;
          color: #909399;
        }

        :deep(.el-form-item) {
          margin: 0;

          .el-input,
          .el-select {
            width: 240px;
          }
        }

        .search-buttons {
          margin-left: auto;

          .el-button {
            margin: 0;

            &:not(:first-child) {
              margin-left: 8px;
            }
          }
        }
      }
    }
  }

  .table-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .table-container {
      :deep(.el-table) {
        .price-info {
          .current-price {
            color: #f56c6c;
            font-weight: bold;
          }

          .original-price {
            margin-left: 8px;
            color: #909399;
            text-decoration: line-through;
            font-size: 12px;
          }
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      padding: 10px 20px;
      text-align: right;
    }
  }
}

.ticket-form {
  :deep(.el-upload--picture-card) {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }

  .image-preview {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    .preview-image {
      width: 100px;
      height: 100px;
      object-fit: cover;
      border-radius: 4px;
    }
  }
}
</style>
