import request from "@/utils/request";

const GWC_HALL_BASE_URL = "/api/v1/gwc/hall";

const GWC_SYB_BASE_URL = "/api/v1/gwcSybs";

const GWC_SYMXB_BASE_URL = "/api/v1/gwcSymxbs";

/**
 * 公物仓资产查询参数
 */
export interface GwcHallQuery extends PageQuery {
  /** 资产名称 */
  searchKeyword?: string;

  rklx?: string;
}

/**
 * 公物仓资产信息
 */
export interface GwcHallVO {
  id: number;
  /** 唯一标识符 */
  guid: string;
  /** 使用人 */
  syr: string;
  /** 图片路径 */
  tpj: string;
  /** 已使用天数 */
  ysyqs: number;
  /** 资产编号 */
  zcbh: string;
  /** 资产名称 */
  zcmc: string;
  /** 品牌 */
  pp: string;
  /** 规格型号 */
  ggxh: string;
  /** 确定日期 */
  qdrq: string;
  /** 使用部门 */
  sybm: string;
  /** 公物仓状态 */
  gwczt: string;
  /** 入库状态 */
  rkzt: string;
  /** 入仓时间 */
  rcsj: string;
  /** 使用性质 */
  syxz: string;
  /** 资产类型编号 */
  zclxbh: string;
  /** 资产金额 */
  je: number;
}

/** 公物仓列表分页对象 */
export interface gwcPageVO {
  /** ID */
  xh?: number;
  /** Guid */
  guid?: string;
  /** 明细的资产名称示例 */
  zcmc?: string;
  /** 申请类型 1入仓 2 出仓 3 领用 4 借用 */
  sqlx?: string;
  /** 申请单号 */
  sqdh?: string;
  /** 申请人 */
  sqr?: string;
  /** 使用金额 */
  syje?: string;
  /** 使用数量 */
  sysl?: string;
  /** 用途说明 */
  ytsm?: string;
  /** 登记部门编码 */
  djbmbm?: string;
  /** 登记部门名称 */
  djbmbmname?: string;
  /** 登记人 */
  djr?: string;
  /** 登记时间 */
  djsj?: Date;
  /** 流程编码 */
  netcode?: string;
  /** 数据状态 */
  sjzt?: string;
  /** 表单编码 */
  frmcode?: string;
  /** 数据状态名称 */
  nodemc?: string;
  /** 备注 */
  notes?: string;
  /** 备用1 ,原保管员的工号,用在调剂和借用 */
  by1?: string;
  /** 备用2 借用的借用天数 */
  by2?: string;
  /** 备用3 借用的延期天数总和*/
  by3?: string;
  /** 备用4 开始借用日期 就是出借人出借确认的时间 */
  by4?: string;
  /** 备用5 */
  by5?: string;
  /** 备用6   */
  by6?: string;
  /** 审结时间 */
  sjsj?: Date;
}

/** 公物仓列表分页对象 */
export interface gwcform {
  /** ID */
  xh?: number;
  /** Guid */
  guid?: string;
  /** 明细的资产名称示例 */
  zcmc?: string;
  /** 申请类型 1入仓 2 出仓 3 领用 4 借用 */
  sqlx?: string;
  /** 申请单号 */
  sqdh?: string;
  /** 申请人 */
  sqr?: string;
  /** 使用金额 */
  syje?: string;
  /** 使用数量 */
  sysl?: string;
  /** 用途说明 */
  ytsm?: string;
  /** 登记部门编码 */
  djbmbm?: string;
  /** 登记部门名称 */
  djbmbmname?: string;
  /** 登记人 */
  djr?: string;
  /** 登记时间 */
  djsj?: string;
  /** 流程编码 */
  netcode?: string;
  /** 数据状态 */
  sjzt?: string;
  /** 表单编码 */
  frmcode?: string;
  /** 数据状态名称 */
  nodemc?: string;
  /** 备注 */
  notes?: string;
  /** 备用1 ,原保管员的工号,用在调剂和借用 */
  by1?: string;
  /** 备用2 借用的借用天数 */
  by2?: string;
  /** 备用3 借用的延期天数总和*/
  by3?: string;
  /** 备用4 开始借用日期 就是出借人出借确认的时间 */
  by4?: string;
  /** 备用5 */
  by5?: string;
  /** 备用6   */
  by6?: string;
  /** 审结时间 */
  sjsj?: Date;
}

/** 入仓分页查询参数 */
export interface gwcPageQuery extends PageQuery {
  /*申请类型 1:入仓 2：退仓 3:领用 4：借用 */
  sqlx?: string;
  /** 申请单号 */
  sqdh?: string;
  /** 登记时间 */
  djsj?: string[];
  /** 数据状态 */
  sjzt?: string;
  /** 使用人 */
  djrname?: string;
  /** 使用部门 */
  djbm?: string;
  /** 列表类型:add,view,check */
  type?: string;
  //查询代办/已办
  checkstatus?: number;
  PageNumber?: number;
  PageSize?: number;
  ucode?: string;
}

export interface GwcDetailsPageQuery extends PageQuery {
  /** 主表guid */
  syguid: string;
}

/** 明细分页对象 */
export interface GwcDetailPageVO {
  id?: number;
  guid?: string;
  syguid?: string;
  zcguid?: string;
  zcmc?: string;
  zcbh?: string;
  sl?: string;
  dj?: string;
  je?: string;
  jldw?: string;
  pp?: string;
  ggxh?: string;
  djsj?: Date;
  syxz?: string;
  syr?: string;
  syrname?: string;
  sybm?: string;
  sybmname?: string;
  bgr?: string;
  bgrname?: string;
  syfx?: string;
  cfdd?: string;
  bmzcgly?: string;
  bmzcglyname?: string;
  gwczt?: string;
  by1?: string;
  by2?: string;
  by3?: string;
  by4?: string;
  ncfdd?: string;
}

/**
 * 固定资产验收单相关API
 *
 * @property {function} getPage 获取验收单分页数据
 * @property {function} getAssetCount 获取资产类型统计数量
 * @property {function} saveGwcSyb 保存固定资产验收单数据
 */
const gwcAPI = {
  /** 获取验收单分页数据 */
  getPage(queryParams: any) {
    return request<any, PageResult<GwcHallVO[]>>({
      url: `${GWC_HALL_BASE_URL}/page`,
      method: "post",
      data: queryParams,
    });
  },

  getAssetCount() {
    return request<any, any>({
      url: `${GWC_HALL_BASE_URL}/countByType`,
      method: "get",
    });
  },

  getAssetCountByReceive() {
    return request<any, number>({
      url: `${GWC_HALL_BASE_URL}/countByReceive`,
      method: "get",
    });
  },

  //批量选择明细创建入仓单
  saveGwcSyb(ids: string, sqlx: string, iszc: string, syguid: string) {
    return request<any, any>({
      url: `${GWC_SYB_BASE_URL}/add/${ids}/${sqlx}/${iszc}/${syguid}`,
      method: "post",
    });
  },

  //获取入仓单分页列表
  getSybPage(queryParams?: gwcPageQuery) {
    return request<any, PageResult<gwcPageVO[]>>({
      url: `${GWC_SYB_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 获取表单数据
   *
   * @param id ID
   * @returns 表单数据
   */
  getFormData(id: number) {
    return request<any, gwcform>({
      url: `${GWC_SYB_BASE_URL}/form/${id}`,
      method: "get",
    });
  },
  /**
   * 提交
   *
   * @param guid guid
   * @param data 表单数据
   */
  submit(guid: string, data?: any) {
    return request({
      url: `${GWC_SYB_BASE_URL}/submit/${guid}`,
      method: "post",
      data: data,
    });
  },
  /**
   * 更新
   *
   * @param id ID
   * @param data 表单数据
   */
  update(id: number, data: gwcform): Promise<boolean> {
    return request<any, boolean>({
      url: `${GWC_SYB_BASE_URL}/update/${id}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 批量删除，多个以英文逗号(,)分割
   *
   * @param ids 主表ID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${GWC_SYB_BASE_URL}/delete/${ids}`,
      method: "get",
    });
  },

  // 明细列表查询
  getDetailsPage(queryParams?: GwcDetailsPageQuery) {
    return request<any, PageResult<GwcDetailPageVO[]>>({
      url: `${GWC_SYMXB_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 批量删除明细，多个以英文逗号(,)分割
   *
   * @param ids 明细ID字符串，多个以英文逗号(,)分割
   */
  deleteDetailsByIds(ids: string) {
    return request({
      url: `${GWC_SYMXB_BASE_URL}/${ids}/delete`,
      method: "get",
    });
  },
};

export default gwcAPI;
