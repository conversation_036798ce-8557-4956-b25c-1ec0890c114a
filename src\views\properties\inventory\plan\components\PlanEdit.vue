<!-- cursor测试:1 -->
<template>
  <div>
    <Title name="盘点计划信息">
      <div v-if="editable">
        <el-button type="primary" @click="handleSave()">保存</el-button>
        <el-button type="danger" @click="handleSubmit()">提交</el-button>
      </div>
    </Title>

    <el-form
      ref="dataFormRef"
      :model="formData"
      :rules="rules"
      label-width="auto"
      :inline="true"
      :disabled="!editable"
    >
      <el-form-item label="计划编号" prop="jhbh">
        <el-input v-model="formData.jhbh" :disabled="true" placeholder="系统自动生成" />
      </el-form-item>
      <el-form-item label="计划名称" prop="jhmc">
        <el-input v-model="formData.jhmc" placeholder="请输入计划名称" />
      </el-form-item>
      <el-form-item label="盘点资产状态" prop="dataType">
        <DDLXcode v-model="formData.dataType" xcode="020233" />
      </el-form-item>
      <el-form-item label="盘点开始时间" prop="startRq">
        <el-date-picker
          v-model="formData.startRq"
          type="date"
          placeholder="盘点开始时间"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="盘点结束时间" prop="endRq">
        <el-date-picker
          v-model="formData.endRq"
          type="date"
          placeholder="盘点结束时间"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <br />
      <el-form-item label="盘点资产分类" prop="zclxbh" style="width: 800px">
        <el-select
          v-model="formData.zclxbh"
          multiple
          placeholder="请选择盘点资产分类"
          style="width: 100%"
          :disabled="!editable"
          :class="{ 'disabled-select': !editable }"
        >
          <el-option
            v-for="item in zclxbhsOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <br />
      <el-form-item label="申请人" prop="djr">
        <DDLUserList
          :key="formData.djbm1"
          v-model="formData.djr"
          :dcode="formData.djbm1"
          :disabled="true"
        />
      </el-form-item>
      <el-form-item label="申请部门" prop="djbm">
        <DDLDeptList v-model="formData.djbm" :disabled="true" />
      </el-form-item>
      <el-form-item label="申请日期" prop="djsj">
        <el-date-picker
          v-model="formData.djsj"
          type="date"
          placeholder="系统自动生成"
          :disabled="true"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <br />
      <el-form-item label="计划类型" prop="jhlx">
        <DDLXcode v-model="formData.jhlx" xcode="020230" />
      </el-form-item>
      <br />
      <template v-if="formData.jhlx === '02023002'">
        <el-form-item label="盘点最小金额" prop="startJe">
          <el-input v-model="formData.startJe" placeholder="盘点最小金额" />
        </el-form-item>
        <el-form-item label="盘点最大金额" prop="endJe">
          <el-input v-model="formData.endJe" placeholder="盘点最大金额" />
        </el-form-item>
        <el-form-item label="入账开始时间" prop="startRksj">
          <el-date-picker
            v-model="formData.startRksj"
            type="date"
            placeholder="入账开始时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="入账结束日期" prop="endRksj">
          <el-date-picker
            v-model="formData.endRksj"
            type="date"
            placeholder="入账结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="抽查比例" prop="ccbl">
          <el-input v-model="formData.ccbl" placeholder="抽查比例" />
        </el-form-item>
        <el-form-item label="部门选择类型" prop="bmlx">
          <DDLXcode v-model="formData.bmlx" xcode="020239" />
        </el-form-item>
        <el-form-item
          label="盘点部门"
          prop="bm"
          style="width: 800px"
          v-if="formData.bmlx === '02023902'"
        >
          <el-select
            v-model="formData.bm"
            multiple
            placeholder="请选择盘点部门"
            style="width: 100%"
            :disabled="!editable"
            :class="{ 'disabled-select': !editable }"
          >
            <el-option
              v-for="item in bmsOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </template>
      <br />
      <el-form-item label="清查范围" prop="qcfw" style="width: 800px">
        <el-input
          v-model="formData.qcfw"
          type="textarea"
          maxlength="200"
          show-word-limit
          :rows="5"
        />
      </el-form-item>
      <br />
      <el-form-item label="清查要求" prop="qcyq" style="width: 800px">
        <el-input
          v-model="formData.qcyq"
          type="textarea"
          maxlength="200"
          show-word-limit
          :rows="5"
        />
      </el-form-item>
      <br />
      <el-form-item label="备注" prop="notes" style="width: 800px">
        <el-input
          v-model="formData.notes"
          type="textarea"
          maxlength="200"
          show-word-limit
          :rows="5"
        />
      </el-form-item>
    </el-form>
    <!-- 查看资产弹窗 -->
    <el-drawer v-model="itemVisible" append-to-body size="70%">
      <CardPanel :key="itemGuid" :guid="itemGuid" :rkguid="itemRkGuid" />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "planEdit",
});
import { ElLoading } from "element-plus";
import { useUserStore } from "@/store";
import planAPI, { planForm } from "@/api/properties/plan";
import DeptAPI from "@/api/system/dept";

//————————————————————————————————————————————暴露的方法,和请求参数
//组件参数
const props = defineProps({
  id: {
    type: Number,
  },
  guid: {
    type: String,
    required: true,
  },
  //保存成功后需要关闭抽屉并刷新数据
  RefreshFatherDrawer: {
    type: Function,
    required: true,
  },
  //是否可编辑
  editable: {
    type: Boolean,
  },
  dcbm: {
    type: String,
    required: true,
  },
});
//——————————————————————————————————————————————————form查询相关

const dcbmDisabled = ref(false);
const netcode = ref("");
//初始化一些用户的信息
const { nickname, dcode } = useUserStore().userInfo;
// 确保 dcode 是字符串类型
const formData = reactive<planForm>({
  guid: "",
  jhbh: "",
  djsj: new Date().toISOString().split("T")[0] + " 00:00:00",
  djbm: String(dcode), // 确保初始值为字符串
  sjzt: "0",
  djr: nickname,
  dataType: "", // 确保初始值为字符串
  zclxbh: [], // 确保初始值为数组
  zclxbhs: "",
  jhlx: "",
  bm: [], // 确保初始值为数组
  bmlx: "", // 新增: 确保初始值为字符串
});

const dataFormRef = ref<InstanceType<typeof ElForm>>();
const rules = reactive({
  jhmc: [{ required: true, message: "计划名称不能为空", trigger: "blur" }],
  dataType: [{ required: true, message: "盘点资产状态不能为空", trigger: "blur" }],
  startRq: [
    { required: true, message: "盘点开始时间不能为空", trigger: "blur" },
    {
      validator: (rule: any, value: string, callback: any) => {
        const selectedDate = new Date(value);
        const today = new Date();
        // 清除时间部分，只比较日期
        today.setHours(0, 0, 0, 0);
        if (value && selectedDate < today) {
          callback(new Error("盘点开始日期不能小于今天"));
        } else {
          callback();
        }
        if (value && formData.endRq && new Date(value) >= new Date(formData.endRq)) {
          callback(new Error("盘点开始时间必须小于盘点结束时间"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  // 其他校验规则...
  endRq: [
    { required: true, message: "盘点结束时间不能为空", trigger: "blur" },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value && formData.startRq && new Date(value) <= new Date(formData.startRq)) {
          callback(new Error("盘点结束时间必须大于盘点开始时间"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  jhlx: [{ required: true, message: "计划类型不能为空", trigger: "blur" }],
  zclxbh: [{ required: true, message: "盘点资产分类不能为空", trigger: "blur" }],
  bmlx: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        if (formData.jhlx === "02023002" && !value) {
          callback(new Error("当计划类型为抽查时，请选择部门类型"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  bm: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        if (formData.bmlx === "02023902" && !value) {
          callback(new Error("当部门类型为部分部门时，请选择盘点部门"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
});
/** 初始化计划信息 */
function handleFormQuery(guid?: string) {
  if (guid) {
    planAPI
      .getFormData(guid)
      .then((data) => {
        Object.assign(formData, data);
        formData.dataType = formData.dataType || ""; // 确保 dataType 为字符串
        formData.zclxbh = formData.zclxbhs ? formData.zclxbhs.split(",") : []; // 确保 zclxbh 为数组
        formData.bm = formData.bms ? formData.bms.split(",") : []; // 确保 bm 为数组
        formData.jhlx = formData.jhlx || "";
        netcode.value = formData.netcode || "";
        formData.djbm = String(formData.djbm || ""); // 确保 djbm 为字符串
        formData.bmlx = formData.bmlx || ""; // 新增: 确保 bmlx 为字符串
      })
      .catch((error) => {
        console.error("获取计划数据失败:", error);
        ElMessage.error("获取计划数据失败");
      });
  } else {
    // 进去前给个新的GUID
    formData.guid = props.guid;
  }
}

//——————————————————————————————————————————————————form操作相关
const handleSave = async () => {
  await dataFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      formData.zclxbhs = (formData.zclxbh || []).join(",");
      formData.bms = (formData.bm || []).join(",");
      const submitData = { ...formData };
      const id = formData.id;
      const apiCall = id ? planAPI.update(id, submitData) : planAPI.addplan(submitData);
      await apiCall
        .then(async (res) => {
          ElMessage.success("盘点计划保存成功");
          //保存成功后，重新用id去查询一次数据
          // 刷新父组件
          if (props.RefreshFatherDrawer) {
            props.RefreshFatherDrawer(true);
          }
        })
        .catch((error) => {
          ElMessage.error("操作失败，失败原因：" + error.message);
        })
        .finally(() => {});
    }
  });
};
/** 提交计划表单 */
const handleSubmit = async () => {
  ElMessageBox.confirm("确定提交审核吗?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    //第一层confirm
    .then(async () => {
      await handleSave().then(async () => {
        //第二层保存完成
        const submitData = { ...formData };
        const dialogloading = ElLoading.service({
          lock: true,
          text: "处理中",
        });
        await planAPI
          .submit(submitData.guid || "")
          .then(() => {
            //第三层提交完成
            ElMessage.success("盘点计划提交成功");
            props.RefreshFatherDrawer(true);
          })
          .finally(() => {
            dialogloading.close();
          });
      });
    })
    .catch(() => {});
};

//——————————————————————————————————————————————————list查询相关

const loading = ref(false);

const total = ref(0);

//—————————————————————————————————————————————资产选择弹窗
const keyId = ref(0);
// 弹窗显隐
const choosedialog = reactive({
  title: "资产计划明细",
  visible: false,
});
const handleAddplanPorperties = async () => {
  await dataFormRef.value?.validate();
  const api = handleSave();
  await api.then(() => {
    choosedialog.visible = true;
  });
};

//—————————————————————————————————————————————资产查看弹窗
const itemVisible = ref(false);
const itemGuid = ref("");
const itemRkGuid = ref("");
const handleRowView = (guid: string, rkguid: string) => {
  itemGuid.value = guid;
  itemRkGuid.value = rkguid;
  itemVisible.value = true;
};

// 新增: 加载部门选项数据
const loadDepartmentOptions = async () => {
  try {
    const response = await DeptAPI.getOptionsCode(); // 假设 API 提供部门选项数据
    bmsOptions.value = response.map((item: any) => ({
      value: item.value,
      label: item.label,
    }));
  } catch (error) {
    console.error("加载部门选项失败:", error);
    ElMessage.error("加载部门选项失败");
  }
};

// 修改: 初始化 bmsOptions 为数组
const bmsOptions = ref<any[]>([]);

// 在组件挂载时加载部门选项
onMounted(() => {
  if (props.id) {
    handleFormQuery(props.guid);
  } else {
    formData.guid = props.guid;
  }
  loadDepartmentOptions(); // 加载部门选项
});

// 新增: 定义盘点资产分类的选项
const zclxbhsOptions = ref([
  { value: "0101", label: "房屋及构筑物" },
  { value: "0102", label: "设备" },
  { value: "0103", label: "文物和陈列品" },
  { value: "0104", label: "图书和档案" },
  { value: "0105", label: "家具和用具" },
  { value: "0106", label: "特种动植物" },
  { value: "0107", label: "物资" },
  { value: "0208", label: "无形资产" },
  // 根据实际需求添加更多选项
]);
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}

/* 禁用状态样式（灰色） */
.disabled-select {
  :deep(.el-tag) {
    background-color: #f5f7fa !important;
    color: #909399 !important;
    border-color: #e4e7ed !important;

    .el-tag__close {
      display: none !important;
    }
  }

  :deep(.el-select__tags-text) {
    color: #909399 !important;
  }
}

/* 可编辑状态样式（蓝色） */
.el-select:not(.disabled-select) {
  :deep(.el-tag) {
    background-color: #ecf5ff !important;
    color: #409eff !important;
    border-color: #d9ecff !important;

    .el-tag__close {
      color: #409eff !important;
      &:hover {
        background-color: #409eff !important;
        color: white !important;
      }
    }
  }

  :deep(.el-select__tags-text) {
    color: #409eff !important;
  }
}

/* 自定义对话框样式 */
.custom-dialog {
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  background-color: #ffffff;
  .el-dialog__header {
    background-color: #409eff;
    color: #ffffff;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    .el-dialog__title {
      color: #ffffff;
    }
  }
  .el-dialog__body {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.9);
  }
}
</style>
