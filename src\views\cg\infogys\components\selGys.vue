<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item label="企业编码" prop="code">
            <el-input
              v-model="queryParams.code"
              placeholder="企业编码"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item label="企业名称" prop="cname">
            <el-input
              v-model="queryParams.cname"
              placeholder="企业名称"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon><Search /></template>
              搜索
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon><Refresh /></template>
              重置
            </el-button>
            <el-button @click="handleSelect">
              <template #icon><Pointer /></template>
              确定
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="列表" />
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @current-change="handleCurrentChange"
      >
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column key="code" label="企业编码" prop="code" min-width="150" align="center" />
        <el-table-column key="cname" label="企业名称" prop="cname" min-width="150" align="center" />

        <el-table-column
          key="address"
          label="单位地址"
          prop="address"
          min-width="150"
          align="center"
        />

        <el-table-column
          key="lxrname"
          label="联系人姓名"
          prop="lxrname"
          min-width="150"
          align="center"
        />

        <el-table-column
          key="lxrmobie"
          label="联系人手机"
          prop="lxrmobie"
          min-width="150"
          align="center"
        />
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store";
import InfoGysAPI, { InfoGysPageVO, InfoGysForm, InfoGysPageQuery } from "@/api/cg/info-gys";

//页面参数
const queryFormRef = ref(ElForm);
const loading = ref(false);
const selGysid = ref(0);
const total = ref(0);

const queryParams = reactive<InfoGysPageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// InfoGys表格数据
const pageData = ref<InfoGysPageVO[]>([]);

/** 查询InfoGys */
function handleQuery() {
  loading.value = true;
  InfoGysAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置InfoGys查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}

function handleCurrentChange(row: InfoGysPageVO) {
  selGysid.value = row.id as number;
}

const emits = defineEmits(["getGysid"]);

const handleSelect = () => {
  emits("getGysid", selGysid.value);
};

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped></style>
