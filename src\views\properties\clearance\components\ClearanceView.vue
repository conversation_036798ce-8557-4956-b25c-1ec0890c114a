<template>
  <el-tabs type="border-card">
    <el-tab-pane v-if="pageType === 'check' && checkstatus == 0">
      <template #label>
        <span class="custom-tabs-label">
          <el-icon><Checked /></el-icon>
          <span>审批表单</span>
        </span>
      </template>
      <Title name="审批单" />
      <!-- <CheckTest ref="compBRef" /> -->
      <CheckForm ref="CheckFormRef" :netcode="props.netcode" :guid="props.guid" sjmc="处置审核" />
      <el-form label-width="120px">
        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="getDataFromChild('Forward')">提交</el-button>
          <el-button type="danger" @click="getDataFromChild('After')">退回</el-button>
        </el-form-item>
      </el-form>
    </el-tab-pane>
    <el-tab-pane>
      <template #label>
        <span class="custom-tabs-label">
          <el-icon><calendar /></el-icon>
          <span>基本信息</span>
        </span>
      </template>
      <ClearanceEdit
        :guid="props.guid"
        :editable="props.editable"
        :djbm="props.djbm"
        :RefreshFatherDrawer="props.RefreshFatherDrawer"
      />
    </el-tab-pane>
    <el-tab-pane>
      <template #label>
        <span class="custom-tabs-label">
          <el-icon><Timer /></el-icon>
          <span>流程信息</span>
        </span>
      </template>
      <CheckList :guid="props.guid" :netcode="props.netcode" />
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
import DisposeEdit from "./DisposeEdit.vue";
import CheckForm from "@/components/GY/WorkFlowCheck/components/CheckForm.vue";
import CheckList from "@/components/GY/WorkFlowCheck/components/CheckList.vue";
import ClearanceEdit from "@/views/properties/clearance/components/ClearanceEdit.vue";
import disposeAPI from "@/api/properties/dispose";
import { ElLoading } from "element-plus";
//————————————————————————————————————————————暴露的方法,和请求参数
const route = useRoute();
const pageType = ref(route.query.type?.toString() || "view");
const props = defineProps({
  guid: {
    type: String,
    required: true,
  },
  netcode: {
    type: String,
    required: true,
  },
  editable: {
    type: Boolean,
    required: true,
  },
  djbm: {
    type: String,
  },
  checkstatus: {
    type: Number,
  },
  //保存成功后需要关闭抽屉并刷新数据
  RefreshFatherDrawer: {
    type: Function,
    required: true,
  },
});

const CheckFormRef = ref<InstanceType<typeof CheckForm> | null>(null);
const childData = ref<any | null>(null);
const submitData = reactive<any>({
  dto: ref<any>({}),
  data: ref<any>({}),
});

const getDataFromChild = async (active: string) => {
  if (CheckFormRef.value) {
    try {
      // 明确捕获错误并处理，避免返回undefined
      childData.value = await CheckFormRef.value.getFormData(active);
      console.log("子页面数据:", childData.value);
      submitData.dto = childData.value;

      const confirmMessage = active === "Forward" ? "确认要提交吗？" : "确认要退回吗？";
      const confirmTitle = active === "Forward" ? "提交确认" : "退回确认";

      try {
        await ElMessageBox.confirm(confirmMessage, confirmTitle, {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });

        const dialogloading = ElLoading.service({
          lock: true,
          text: "处理中",
        });

        await disposeAPI
          .submit(props.guid, submitData)
          .then(() => {
            //第三层提交完成
            active == "Forward" ? ElMessage.success("提交成功") : ElMessage.success("退回成功");
            props.RefreshFatherDrawer(true);
          })
          .finally(() => {
            dialogloading.close();
          });
      } catch (e) {
        console.log("操作已取消");
      }
    } catch (error: any) {
      // 统一处理表单数据获取错误
      ElMessage.error(error.message || "获取表单数据失败");
    }
  }
};

onMounted(() => {
  console.log("netcode", props.netcode);
});
</script>
<style lang="scss" scoped></style>
