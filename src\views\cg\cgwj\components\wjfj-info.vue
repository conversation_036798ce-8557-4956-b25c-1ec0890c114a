<template>
  <Title name="项目信息">
    <div>
      <el-button type="success" @click="handlePrevious()">上一步</el-button>
      <!-- <el-button type="primary" @click="handleSave()">保存</el-button> -->
      <el-button type="danger" @click="handleNext()">提交</el-button>
    </div>
  </Title>
  <FileUpload v-if="editable" ref="fileRef" :key="props.guid" :guid="props.guid" code="021530" />
</template>

<script setup lang="ts">
import { ref } from "vue";
import ZxcgCgwjAPI, { ZxcgCgwjForm } from "@/api/cg/zxcg-cgwj";
// 直接导入整个组件
import FileUpload from "@/components/Upload/FileUpload.vue"; // 导入 FileUpload 组件
const props = defineProps({
  //guid
  guid: {
    type: String,
    required: true,
  },
  //是否可编辑
  editable: {
    type: <PERSON><PERSON><PERSON>,
    required: true,
  },
});

const emits = defineEmits(["nowstep"]);
const fileRef = ref<InstanceType<typeof FileUpload> | null>(null);
const handlePrevious = async () => {
  emits("nowstep", 2);
};
const handleNext = async () => {
  if (fileRef.value) {
    if (fileRef.value.CheckIsRequired()) {
      // 访问并调用子组件的方法
      await ZxcgCgwjAPI.submit(props.guid)
        .then(async (res) => {
          ElMessage.success("提交成功");
        })
        .catch((error) => {
          ElMessage.error("操作失败，失败原因：" + error.message);
        })
        .finally(() => {});
    }
  }
};
</script>
<style scoped></style>
