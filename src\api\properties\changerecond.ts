import request from "@/utils/request";

const Accept_BASE_URL = "/api/v1/store";

const changerecondAPI = {
  /** 获取变动记录分页数据 */
  getPage(queryParams?: changerecondPageQuery) {
    return request<any, PageResult<changerecondPageVO[]>>({
      url: `${Accept_BASE_URL}/page-change`,
      method: "get",
      params: queryParams,
    });
  },
}

export default changerecondAPI;

/** 验收单分页查询参数 */
export interface changerecondPageQuery extends PageQuery {
  /** 资产名称或编号 */
  zcmc?: string;
  syr?: string;
  djr?: string;
  czlx?: string;
  czlxxl?: string;
  sybm?: string;
  djbm?: string;
}


/** 变动单分页对象 */
export interface changerecondPageVO {
  /** 资产编号 */
  zcbh?: string;
  zcmc?: string;
  guid?: string;
  zcguid?: string;
  rkguid?: string;
  syr?: string;
  syrname?: string;
  sybm?: string;
  sybmname?: string;
  djr?: string;
  djrname?: string;
  djbm?: string;
  djbmname?: string;
  lb?: string;  
  djsj?: Date;
}


