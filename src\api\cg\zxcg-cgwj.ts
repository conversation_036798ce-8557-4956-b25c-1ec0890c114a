import request from "@/utils/request";

const ZXCGCGWJ_BASE_URL = "/api/v1/zxcgCgwjs";

const ZxcgCgwjAPI = {
  /** 获取采购文件分页数据 */
  getPage(queryParams?: ZxcgCgwjPageQuery) {
    return request<any, PageResult<ZxcgCgwjPageVO[]>>({
      url: `${ZXCGCGWJ_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取采购文件表单数据
   *
   * @param id ZxcgCgwjID
   * @returns ZxcgCgwj表单数据
   */
  getFormData(guid: string) {
    return request<any, ZxcgCgwjForm>({
      url: `${ZXCGCGWJ_BASE_URL}/${guid}/form`,
      method: "get",
    });
  },

  /**
   * 提交采购文件
   */
  submit(guid: string, data?: any) {
    return request({
      url: `${ZXCGCGWJ_BASE_URL}/submit/${guid}`,
      method: "post",
      data: data,
    });
  },
  /** 添加采购文件*/
  save(data: ZxcgCgwjForm) {
    return request({
      url: `${ZXCGCGWJ_BASE_URL}/save`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新采购文件
   *
   * @param id ZxcgCgwjID
   * @param data ZxcgCgwj表单数据
   */
  update(id: number, data: ZxcgCgwjForm) {
    return request({
      url: `${ZXCGCGWJ_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 批量删除采购文件，多个以英文逗号(,)分割
   *
   * @param ids 采购文件ID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${ZXCGCGWJ_BASE_URL}/delete/${ids}`,
      method: "post",
    });
  },
};

export default ZxcgCgwjAPI;

/** 采购文件分页查询参数 */
export interface ZxcgCgwjPageQuery extends PageQuery {
  /** 文件编号 */
  wjbh?: string;
  /** 文件名称 */
  wjmc?: string;
  /** 采购形式 */
  cgxs?: string;
  /** 采购方式 */
  cgfs?: string;
  pagetype?: string;
}

/** 采购文件表单对象 */
export interface ZxcgCgwjForm {
  wjid?: number;
  guid?: string;
  /** 文件编号 */
  wjbh?: string;
  /** 文件名称 */
  wjmc?: string;
  /** 委托ID */
  wtid?: number;
  /** 委托单位 */
  wtdw?: string;
  /** 采购形式 */
  cgxs?: string;
  cgxsname?: string;
  /** 采购方式 */
  cgfs?: string;
  cgfsname?: string;
  /** 预算经费 */
  ysjf?: number;
  /** 联系人 */
  lxr?: string;
  /** 联系方式 */
  lxfs?: string;
  /** 初始0，开标1，开评定标6 ，7开评定标结束，流标e，废标f,备案b,结束9 */
  wjzt?: string;
  notes?: string;
  /** 登记人 */
  djr?: string;
  /** 登记部门 */
  djbm?: string;
  djsj?: Date;
  /** 节点编码 */
  netcode?: string;
  /** 工作流数据状态 */
  sjzt?: string;
  /** 下一审批人 */
  nexter?: string;
  /** 下一审批部门|hidden */
  nextbm?: string;
  /** 采购申请人 */
  sqr?: string;
  /** 项目负责人 */
  fzr?: string;
  /** 采购申请部门 */
  sqbm?: string;
  templateid?: string;
  /** 公告日期  （结果备案时存入） */
  ggrq?: Date;
  /** 定标日期 （结果备案时存入） */
  dbrq?: Date;
  /** 用户代表 */
  yhdb?: string;
  /** 备用zxr */
  by1?: string;
  /** 备用政采云项目编号 */
  by2?: string;
  /** 备用部门监督人 */
  by3?: string;
  /** 备用资产监督人 */
  by4?: string;
  /** 备用是否项目采购 */
  by5?: string;
  /** 备用代理机构确认人 */
  by6?: string;
  /** 备用是否政采云 */
  by7?: string;
  /** 备用 */
  by8?: string;
  /** 备用 */
  by9?: string;
  nid?: number;
  /** 开标时间 */
  kbsj?: Date;
  /** 每一轮降价幅度元/次 */
  jjfd?: string;
  /** 质保期限 */
  zbqx?: string;
  /** 供货周期 */
  ghzq?: string;
  /** 商务要求 */
  swyq?: string;
  /** 部门负责人 */
  bmfzr?: string;
  sfts?: string;
  fbsj?: Date;
  jgsj?: Date;
  /** 主管部门 */
  zgbm?: string;
  /** 任务id，这个字段是蒋加的，现在废弃，因为有可能多任务生成一个文件 */
  rwid?: string;
  /** 执行状态用于判断是进行一般流程，简易流程，网超流程，询价，竞价 */
  zxbj?: string;
  /** 文件类别 */
  wjlb?: string;
}

/** 采购文件分页对象 */
export interface ZxcgCgwjPageVO {
  qrsbh?: string;
  wjid?: number;
  guid?: string;
  /** 文件编号 */
  wjbh?: string;
  /** 文件名称 */
  wjmc?: string;
  /** 委托ID */
  wtid?: number;
  /** 委托单位 */
  wtdw?: string;
  /** 采购形式 */
  cgxs?: string;
  /** 采购方式 */
  cgfs?: string;
  /** 预算经费 */
  ysjf?: number;
  /** 联系人 */
  lxr?: string;
  /** 联系方式 */
  lxfs?: string;
  /** 初始0，开标1，开评定标6 ，7开评定标结束，流标e，废标f,备案b,结束9 */
  wjzt?: string;
  notes?: string;
  /** 登记人 */
  djr?: string;
  /** 登记部门 */
  djbm?: string;
  djsj?: Date;
  /** 节点编码 */
  netcode?: string;
  netcodename?: string;
  /** 工作流数据状态 */
  sjzt?: string;
  /** 下一审批人 */
  nexter?: string;
  /** 下一审批部门|hidden */
  nextbm?: string;
  /** 采购申请人 */
  sqr?: string;
  /** 项目负责人 */
  fzr?: string;
  /** 采购申请部门 */
  sqbm?: string;
  templateid?: string;
  /** 公告日期  （结果备案时存入） */
  ggrq?: Date;
  /** 定标日期 （结果备案时存入） */
  dbrq?: Date;
  /** 用户代表 */
  yhdb?: string;
}
