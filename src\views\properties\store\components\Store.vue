<template>
  <div>
    <el-card>
      <template #header>
        <Title name="入库单信息">
          <div>
            <el-button v-if="props.editable" type="primary" @click="handleSave">保存</el-button>
            <el-button v-if="props.editable" type="danger" @click="handleSubmit">提交</el-button>
          </div>
        </Title>
      </template>
      <el-form
        ref="dataFormRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        :inline="true"
        :disabled="!props.editable"
      >
        <el-form-item label="入库单号" prop="zcrkdh">
          <el-input v-model="formData.zcrkdh" :disabled="true" placeholder="系统自动生成" />
        </el-form-item>
        <el-form-item label="财政分类" prop="czcode">
          <!-- 弹出选择 -->
          <div class="flex">
            <el-input v-model="formData.czcodeName" :disabled="true" placeholder="请选择财政分类" />
            <el-button type="primary" @click="visibleFin = true">选择</el-button>
          </div>
        </el-form-item>
        <el-form-item label="教育分类" prop="jycode">
          <!-- 弹出选择 -->
          <div class="flex">
            <el-input v-model="formData.jycodeName" :disabled="true" placeholder="请选择教育分类" />
            <el-button type="primary" @click="visibleEdu = true">选择</el-button>
          </div>
        </el-form-item>
        <el-form-item label="资产名称" prop="zcmc">
          <el-input v-model="formData.zcmc" />
        </el-form-item>
        <el-form-item label="数量" prop="sl">
          <el-input v-model="formData.sl" />
        </el-form-item>
        <el-form-item label="计量单位" prop="jldw">
          <el-input v-model="formData.jldw" />
        </el-form-item>
        <el-form-item label="货币单位" prop="hbdw">
          <!-- 下拉框组件 -->
          <DDLXcode v-model="formData.hbdw" xcode="020216" />
        </el-form-item>
        <el-form-item label="单价（元）" prop="dj">
          <el-input v-model="formData.dj" type="number" />
        </el-form-item>
        <el-form-item label="资产原值（元）" prop="je">
          <el-input v-model="formData.je" />
        </el-form-item>
        <el-form-item label="价值类型" prop="jzlx">
          <!-- 下拉框组件 -->
          <DDLXcode v-model="formData.jzlx" xcode="020208" />
        </el-form-item>
        <el-form-item label="购置日期" prop="gzrq">
          <el-date-picker
            v-model="formData.gzrq"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="date"
            placeholder="请选择购置日期"
          />
        </el-form-item>
        <el-form-item label="取得日期" prop="qdrq">
          <el-date-picker
            v-model="formData.qdrq"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="date"
            placeholder="请选择取得日期"
          />
        </el-form-item>
        <el-form-item label="验收日期" prop="ysrq">
          <el-date-picker
            v-model="formData.ysrq"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="date"
            placeholder="请选择验收日期"
          />
        </el-form-item>
        <el-form-item label="验收单号" prop="ysdh">
          <el-input v-model="formData.ysdh" />
        </el-form-item>
        <el-form-item label="保修截止日期" prop="bxdqrq">
          <el-date-picker
            v-model="formData.bxdqrq"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="date"
            placeholder="请选择保修截止日期"
          />
        </el-form-item>
        <el-form-item label="经费编号" prop="jfbh">
          <el-input v-model="formData.jfbh" />
        </el-form-item>
        <el-form-item label="经费名称" prop="jfmc">
          <el-input v-model="formData.jfmc" />
        </el-form-item>
        <el-form-item label="经费年度" prop="jfnd">
          <el-input v-model="formData.jfnd" />
        </el-form-item>
        <el-form-item label="经费来源" prop="zjly">
          <el-input v-model="formData.zjly" />
        </el-form-item>
        <el-form-item label="取得方式" prop="qdfs">
          <!-- 下拉框组件 -->
          <DDLXcode v-model="formData.qdfs" xcode="020218" />
        </el-form-item>
        <el-form-item label="资产管理员" prop="bmzcgly">
          <!-- 下拉框组件 -->
          <DDLDataList v-model="formData.bmzcgly" type="PropertyManager" :rcode="'0203'" />
        </el-form-item>
        <el-form-item label="是否进口" prop="sfjk">
          <el-radio-group v-model="formData.sfjk">
            <el-radio value="1">是</el-radio>
            <el-radio value="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="formData.sfjk == '1'" label="国别">
          <!-- 下拉框组件 -->
          <DDLXcode v-model="formData.gb" xcode="020249" />
        </el-form-item>
        <el-form-item label="确认书号">
          <el-input v-model="formData.querensh" />
        </el-form-item>
      </el-form>

      <!-- 车辆相关信息 -->
      <VehicleInfoForm
        ref="ChirdRef"
        v-if="type == 'cl'"
        :guid="props.guid"
        :zclxbh="formData.zclxbh"
        :editable="props.editable"
      />
      <!-- 土地相关信息 -->
      <LandPropertyForm
        ref="ChirdRef"
        v-else-if="type == 'td'"
        :guid="props.guid"
        :zclxbh="formData.zclxbh"
        :editable="props.editable"
      />
      <!-- 房屋相关信息 -->
      <RealEstateAssetForm
        ref="ChirdRef"
        v-else-if="type == 'fw'"
        :guid="props.guid"
        :zclxbh="formData.zclxbh"
        :editable="props.editable"
      />
      <!-- 股权投资相关信息 -->
      <EnterpriseAssetForm
        ref="ChirdRef"
        v-else-if="type == 'gqtz'"
        :guid="props.guid"
        :zclxbh="formData.zclxbh"
        :editable="props.editable"
      />
      <!-- 知识产权相关信息 -->
      <IntellectualPropertyForm
        ref="ChirdRef"
        v-else-if="type == 'zscq'"
        :guid="props.guid"
        :zclxbh="formData.zclxbh"
        :editable="props.editable"
      />
      <!-- 通用相关信息 -->
      <UniversalAssetForm
        ref="ChirdRef"
        v-else
        :guid="props.guid"
        :zclxbh="formData.zclxbh"
        :editable="props.editable"
        @ClearBmgly="ClearBmgly"
      />

      <el-form
        ref="dataNotesRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        :inline="true"
        :disabled="!props.editable"
      >
        <el-form-item label="备注" prop="notes" class="full-width">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="3"
            placeholder="最多200个文字"
            :maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </el-card>
    <!-- 财政弹窗 -->
    <el-dialog v-model="visibleFin" title="财政分类选择框" width="60%">
      <TreeCzJyCode type="CZ" @node-click="handleCzcodeNodeClick" />
    </el-dialog>
    <!-- 教育弹窗 -->
    <el-dialog v-model="visibleEdu" title="教育分类选择框" width="60%">
      <TreeCzJyCode type="JY" @node-click="handleJycodeNodeClick" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import storeAPI from "@/api/properties/store";
import { czcodes, fetchCzCodes } from "@/assets/CZCode";
import TreeCzJyCode from "@/components/Properties/TreeCzJyCode/index.vue";
import { ElLoading } from "element-plus";
import VehicleInfoForm from "@/views/properties/store/components/VehicleInfoForm.vue";
import LandPropertyForm from "@/views/properties/store/components/LandPropertyForm.vue";
import RealEstateAssetForm from "@/views/properties/store/components/RealEstateAssetForm.vue";
import UniversalAssetForm from "@/views/properties/store/components/UniversalAssetForm.vue";
import EnterpriseAssetForm from "@/views/properties/store/components/EnterpriseAssetForm.vue";
import IntellectualPropertyForm from "@/views/properties/store/components/IntellectualPropertyForm.vue";

import { jycodes } from "@/assets/JYCode";
const visibleFin = ref(false);
const visibleEdu = ref(false);
const dataFormRef = ref<InstanceType<typeof ElForm>>();
const dataNotesRef = ref<InstanceType<typeof ElForm>>();
const ChirdRef = ref<any | null>(null);
const type = ref("");
const props = defineProps({
  editable: {
    type: Boolean,
  },
  //查询详情的入库单ID和guid
  id: {
    type: Number,
  },
  guid: {
    type: String,
  },
  zclxbh: {
    type: String,
  },
  czcode: {
    type: String,
  },
  //保存成功后需要关闭抽屉并刷新数据
  RefreshFatherDrawer: {
    type: Function,
  },
});

const emit = defineEmits(["UpdateCard", "UpdateZclxbh", "CheckFileRequired"]);

/** 表单验证规则 */
const rules = reactive({
  zcmc: [{ required: true, message: "资产名称不能为空", trigger: "blur" }],
  sl: [
    { required: true, message: "数量不能为空", trigger: "blur" },
    { pattern: /^[1-9]\d*$/, message: "数量必须为正整数", trigger: "blur" },
  ],
  jldw: [{ required: true, message: "计量单位不能为空", trigger: "blur" }],
  dj: [
    { required: true, message: "单价不能为空", trigger: "blur" },
    { pattern: /^\d+(\.\d{1,2})?$/, message: "单价必须为数字(最多两位小数)", trigger: "blur" },
  ],
  je: [
    { required: true, message: "资产原值不能为空", trigger: "blur" },
    { pattern: /^\d+(\.\d{1,2})?$/, message: "资产原值必须为数字(最多两位小数)", trigger: "blur" },
  ],
  gzrq: [{ required: true, message: "购置日期不能为空", trigger: "blur" }],
  qdrq: [{ required: true, message: "取得日期不能为空", trigger: "blur" }],
  ysrq: [{ required: true, message: "验收日期不能为空", trigger: "blur" }],
  czcode: [{ required: true, message: "财政分类不能为空", trigger: "blur" }],
  jycode: [{ required: true, message: "教育分类不能为空", trigger: "blur" }],
  qdfs: [{ required: true, message: "取得方式不能为空", trigger: "blur" }],
  bmzcgly: [{ required: true, message: "资产管理员不能为空", trigger: "blur" }],
  zclxbh: [{ required: true, message: "资产类别编号不能为空", trigger: "blur" }],
  hbdw: [{ required: true, message: "货币单位不能为空", trigger: "blur" }],
  jzlx: [{ required: true, message: "价值类型不能为空", trigger: "blur" }],
  gb: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        if (formData.sfjk === "1" && !value) {
          callback(new Error("当选择进口时，国别不能为空"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
});
const formData = reactive<any>({
  jzlx: "02020801", //价值类型
  gb: "", //国别
  sfjk: "0", //是否进口
  gzrq: new Date().toISOString().slice(0, 19).replace("T", " "), //购置日期
  qdrq: new Date().toISOString().slice(0, 19).replace("T", " "),
  ysrq: new Date().toISOString().slice(0, 19).replace("T", " "), //验收日期
  //设置默认值
  hbdw: "02021601",
  qdfs: "02021801",
});

/** 验证日期顺序 */
const validateDateOrder = (): boolean => {
  const gzrqDate = formData.gzrq ? new Date(formData.gzrq) : null;
  const qdrqDate = formData.qdrq ? new Date(formData.qdrq) : null;
  const ysrqDate = formData.ysrq ? new Date(formData.ysrq) : null;

  if (gzrqDate && qdrqDate && gzrqDate > qdrqDate) {
    ElMessage.error("购置日期不能晚于取得日期");
    return false;
  }

  if (qdrqDate && ysrqDate && qdrqDate > ysrqDate) {
    ElMessage.error("取得日期不能晚于验收日期");
    return false;
  }

  return true;
};

/** 保存表单 */
const handleSave = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: "正在保存...",
    background: "rgba(0, 0, 0, 0.7)",
  });
  try {
    // 验证日期顺序
    if (!validateDateOrder()) {
      loading.close();
      return;
    }

    // 验证主表单
    const mainFormValid = await new Promise<boolean>((resolve) => {
      dataFormRef.value?.validate((valid: boolean) => resolve(valid));
    });

    // 验证子表单
    let childFormValid = false;
    try {
      await ChirdRef.value?.validate();
      childFormValid = true;
    } catch (error) {
      childFormValid = false;
    }

    // 如果任一表单验证失败，显示提示并返回
    if (!mainFormValid || !childFormValid) {
      ElMessage.warning("请完善必填项");
      return;
    }

    // 保存数据
    const id = formData.id;
    console.log("formData", formData);
    const apiCall = id ? storeAPI.updateSame(formData) : storeAPI.addsame(formData);

    // 先保存主表单
    await apiCall.then(async (res: any) => {
      console.log("入库单保存res", res);
      if (!res) {
        ElMessage.error("入库单保存失败");
      } else {
        // 保存成功后，重新用id去查询一次数据
        if (!id) {
          //给当前formData.id赋值
          formData.id = res;
        }
      }
      await ChirdRef.value?.submitForm();
    });
    // 刷新父组件
    if (props.RefreshFatherDrawer) {
      props.RefreshFatherDrawer();
    }
    //保存完成之后更新Card
    emit("UpdateCard");
  } catch (error) {
    console.error("保存入库数据失败:", error);
    ElMessage.error(error instanceof Error ? error.message : "保存入库数据失败");
  } finally {
    loading.close();
  }
};

const handleSubmit = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: "正在保存...",
    background: "rgba(0, 0, 0, 0.7)",
  });
  try {
    // 验证日期顺序
    if (!validateDateOrder()) {
      loading.close();
      return;
    }

    // 验证主表单
    const mainFormValid = await new Promise<boolean>((resolve) => {
      dataFormRef.value?.validate((valid: boolean) => resolve(valid));
    });

    // 验证子表单
    let childFormValid = false;
    try {
      await ChirdRef.value?.validate();
      childFormValid = true;
    } catch (error) {
      childFormValid = false;
    }

    // 触发父组件的文件校验并获取结果
    const fileCheckResult = await new Promise<boolean>((resolve) => {
      // 使用nextTick确保在DOM更新后执行
      nextTick(() => {
        // 触发事件并传递回调函数
        emit("CheckFileRequired", (isValid: boolean) => {
          resolve(isValid);
        });
      });
    });

    // 如果任一表单验证失败，显示提示并返回
    if (!mainFormValid || !childFormValid || !fileCheckResult) {
      ElMessage.warning("请完善必填项");
      return;
    }

    // 保存数据
    const id = formData.id;
    console.log("formData", formData);
    const apiCall = id ? storeAPI.updateSame(formData) : storeAPI.addsame(formData);

    // 先保存主表单
    await apiCall.then(async (res: any) => {
      if (!id) {
        //给当前formData.id赋值
        formData.id = res;
      }
      await ChirdRef.value?.submitForm().then(async () => {
        await storeAPI.submit(formData.guid);
        // 全部保存成功
        ElMessage.success("提交成功");
      });
    });

    // 刷新父组件
    if (props.RefreshFatherDrawer) {
      props.RefreshFatherDrawer(true);
    }
  } catch (error) {
    console.error("保存入库数据失败:", error);
    ElMessage.error(error instanceof Error ? error.message : "提交入库数据失败");
  } finally {
    loading.close();
  }
};

const queryParams = reactive<any>({
  guid: "",
  zclxbh: "",
});

function handleQuery() {
  queryParams.guid = props.guid;
  queryParams.zclxbh = props.zclxbh;
  storeAPI
    .getFormDataSame(queryParams)
    .then((data: any) => {
      Object.assign(formData, data);
    })
    .catch((error) => {
      console.error("获取入库数据失败:", error);
      ElMessage.error("获取入库数据失败");
    })
    .finally(() => {});
}

//设置原值自动填写
watch(
  () => [formData.sl, formData.dj],
  ([newSl, newDj]) => {
    // 解构数组
    if (newSl && newDj) {
      // 根据 sl 和 dj 计算 je
      formData.je = newSl * newDj;
    }
  }
);

onMounted(() => {
  formData.guid = props.guid;
  formData.zclxbh = props.zclxbh;
  if (props.id == null) {
    //如果没有id，则必选财政分类
    if (props.czcode) {
      formData.czcode = props.czcode;
      //根据czcode进行显隐
    } else {
      ElMessage.warning("请选择财政分类");
      props.RefreshFatherDrawer;
      return;
    }
  } else {
    handleQuery();
  }
});

//子组件向父组件传值
const handleCzcodeNodeClick = (treeNodeValue: string) => {
  visibleFin.value = false;
  formData.czcode = treeNodeValue;
};
const handleJycodeNodeClick = (treeNodeValue: string) => {
  visibleEdu.value = false;
  formData.jycode = treeNodeValue;
};

//监听formData.czcode的值，并从codes中找到对应的name
watch(
  () => formData.czcode,
  (newVal) => {
    if (newVal) {
      const codeItem = findInTree(czcodes.value, newVal);
      console.log("codeItem", codeItem);
      if (codeItem) {
        formData.czcodeName = codeItem.label;
        formData.zclxbh = codeItem.text;
        type.value = getTypeByCzcode(codeItem);
        console.log("type", type.value);
      }
    } else {
      formData.czcodeName = "";
    }
  }
);

//监听formData.czcode的值，并从codes中找到对应的name
watch(
  () => formData.jycode,
  (newVal) => {
    if (newVal) {
      const codeItem = findInTree(jycodes.value, newVal);
      console.log("codeItem--jycodeName", codeItem);
      if (codeItem) {
        formData.jycodeName = codeItem.label;
      }
    } else {
      formData.jycodeName = "";
    }
  }
);

// 递归查找树形结构中的节点
function findInTree(tree: any[], value: string): any {
  for (const node of tree) {
    if (node.value === value) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findInTree(node.children, value);
      if (found) {
        return found;
      }
    }
  }
  return null;
}

function getTypeByCzcode(item: OptionType) {
  // 查找是否存在特殊对应分类
  let clt_name = ""; // 使用 let 声明变量
  let str2 = item.text || "";
  let str3 = item.text?.substring(0, 4); // 确保 str3 是从 item.text 获取的
  switch (str3) {
    case "0101": {
      // 房屋及构筑物
      if (str2.startsWith("010103")) {
        clt_name = "td";
      } else {
        clt_name = "fw";
      }
      break;
    }
    case "0102": {
      // 设备
      emit("UpdateZclxbh");
      if (str2.startsWith("010203")) {
        clt_name = "cl";
      } else {
        clt_name = "ty";
      }
      break;
    }

    case "0103": // 文物和陈列品
    case "0104": // 图书和档案
    case "0105": // 家具和用具
    case "0106": // 特种动植物
    case "0107": {
      // 物资
      clt_name = "ty";
      break;
    }
    case "0208": {
      // 无形资产
      if (str2.startsWith("020807")) {
        clt_name = "gqtz";
      } else {
        clt_name = "zscq";
      }
      break;
    }
    default:
      clt_name = "ty";
      break;
  }
  return clt_name;
}

function ClearBmgly(code: string) {
  formData.bmzcgly = "";
}
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}

.full-width {
  width: 80%;
}

.flex {
  display: flex;
  flex-direction: row;
}
</style>
