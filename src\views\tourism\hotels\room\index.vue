<template>
  <div class="app-container room-admin">
    <!-- 操作栏 -->
    <div class="search-bar">
      <el-form :inline="true">
        <el-form-item label="关键字">
          <el-input
            v-model="searchQuery"
            placeholder="搜索房间类型"
            style="width: 200px"
            class="filter-item"
          />
        </el-form-item>
        <el-form-item label="排序">
          <el-select v-model="sortKey" placeholder="排序" style="width: 120px" class="filter-item">
            <el-option label="价格升序" value="priceAsc" />
            <el-option label="价格降序" value="priceDesc" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleAdd">添加房间</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 房间列表 -->
    <el-row :gutter="20">
      <el-col
        v-for="(room, index) in sortedFilteredRoomList"
        :key="room.id"
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
        class="room-card-col"
      >
        <el-card
          shadow="hover"
          :class="['room-card', room.status === '维护中' ? 'disabled-card' : '']"
        >
          <div class="card-header">
            <el-carousel
              v-if="room.images && room.images.length > 0"
              :interval="5000"
              indicator-position="outside"
              height="200px"
            >
              <el-carousel-item v-for="(img, idx) in room.images" :key="idx">
                <el-image
                  :src="img"
                  fit="cover"
                  class="room-image"
                  :preview-src-list="room.images"
                  :initial-index="idx"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                      <span>图片加载失败</span>
                    </div>
                  </template>
                </el-image>
              </el-carousel-item>
            </el-carousel>
            <div v-else class="image-placeholder">
              <el-icon :size="50"><Picture /></el-icon>
              <span>暂无图片</span>
            </div>

            <el-tag v-if="room.isFeatured" effect="dark" type="danger" class="feature-tag">
              推荐房型
            </el-tag>
          </div>

          <div class="card-body">
            <h3 class="room-title">{{ room.type }}</h3>
            <div class="room-meta">
              <div class="meta-item">
                <el-icon><User /></el-icon>
                <span>最多入住：{{ room.capacity }}人</span>
              </div>
              <div class="meta-item">
                <el-icon><OfficeBuilding /></el-icon>
                <span>面积：{{ room.area }}㎡</span>
              </div>
            </div>

            <div class="price-section">
              <span class="current-price">
                ¥{{ room.price }}
                <small>/晚</small>
              </span>
              <span v-if="room.originalPrice" class="original-price">
                ¥{{ room.originalPrice }}
              </span>
              <el-tag v-if="room.discount" type="success" effect="dark" class="discount-tag">
                {{ room.discount }}折
              </el-tag>
            </div>

            <div class="facilities">
              <el-tooltip
                v-for="facility in room.facilities"
                :key="facility.name"
                :content="facility.desc"
                placement="top"
              >
                <div class="facility-item">
                  <el-icon :size="20"><component :is="facility.icon" /></el-icon>
                  <span>{{ facility.name }}</span>
                </div>
              </el-tooltip>
            </div>
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="showBookingInfo(room)">
                预订信息
              </el-button>
              <el-button type="info" size="small" @click="handleDetail(room)">房间详情</el-button>
            </div>
          </div>

          <div class="card-footer">
            <el-tag
              :type="room.status === '可用' ? 'success' : 'warning'"
              effect="plain"
              class="status-tag"
            >
              {{ room.status }}
            </el-tag>
            <div class="operation-buttons">
              <el-button type="text" :icon="Edit" @click="handleEdit(index, room)" />
              <el-button
                type="text"
                :icon="Star"
                :class="{ featured: room.isFeatured }"
                @click="handleFeatureToggle(room)"
              />
              <el-button type="text" :icon="Setting" @click="handleRoomStatus(room)" />
              <el-button
                type="text"
                :icon="Delete"
                class="delete-btn"
                @click="handleDelete(index, room)"
              />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 30, 50]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 添加/编辑房间对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="65%"
      class="room-form-dialog"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <el-form ref="formRef" :model="form" label-width="120px" class="room-form">
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <div class="form-grid">
            <el-form-item label="房型" prop="type">
              <el-input v-model="form.type" placeholder="请输入房间类型" />
            </el-form-item>
            <el-form-item label="价格" prop="price">
              <el-input-number
                v-model="form.price"
                :min="0"
                :precision="2"
                :step="100"
                class="full-width"
                placeholder="请输入价格"
              />
            </el-form-item>
            <el-form-item label="面积" prop="area">
              <el-input-number
                v-model="form.area"
                :min="0"
                :precision="1"
                :step="5"
                class="full-width"
                placeholder="请输入面积"
              />
            </el-form-item>
            <el-form-item label="最大入住人数" prop="capacity">
              <el-input-number
                v-model="form.capacity"
                :min="1"
                :max="10"
                class="full-width"
                placeholder="请输入最大入住人数"
              />
            </el-form-item>
          </div>
        </div>

        <div class="form-section">
          <div class="section-title">房间配置</div>
          <div class="form-grid">
            <el-form-item label="设施" prop="facilities" class="full-width">
              <el-select
                v-model="form.facilities"
                multiple
                placeholder="请选择房间设施"
                class="full-width"
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option
                  v-for="item in facilityOptions"
                  :key="item.name"
                  :label="item.name"
                  :value="item"
                >
                  <div class="facility-option">
                    <el-icon><component :is="item.icon" /></el-icon>
                    <span>{{ item.name }}</span>
                    <small>{{ item.desc }}</small>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" class="full-width">
                <el-option label="可用" value="可用">
                  <div class="status-option">
                    <el-icon class="success"><CircleCheck /></el-icon>
                    <span>可用</span>
                  </div>
                </el-option>
                <el-option label="维护中" value="维护中">
                  <div class="status-option">
                    <el-icon class="warning"><Warning /></el-icon>
                    <span>维护中</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="推荐房型" prop="isFeatured">
              <div class="featured-switch">
                <el-switch
                  v-model="form.isFeatured"
                  active-text="设为推荐"
                  inactive-text="普通房型"
                />
              </div>
            </el-form-item>
          </div>
        </div>

        <div class="form-section">
          <div class="section-title">房间描述</div>
          <el-form-item label="描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="4"
              placeholder="请输入房间描述信息"
              resize="none"
            />
          </el-form-item>
        </div>

        <div class="form-section">
          <div class="section-title">房间图片</div>
          <el-form-item label="图片" class="upload-item">
            <el-upload
              class="room-uploader"
              action="/api/upload"
              :auto-upload="false"
              :limit="5"
              :on-exceed="handleExceed"
              :file-list="fileList"
              list-type="picture-card"
              accept="image/*"
            >
              <template #default>
                <div class="upload-trigger">
                  <el-icon :size="24"><Plus /></el-icon>
                  <div class="upload-text">点击上传</div>
                </div>
              </template>
              <template #file="{ file }">
                <div class="upload-preview">
                  <img :src="file.url" class="upload-image" />
                  <div class="upload-actions">
                    <el-icon class="delete" @click.stop="handleRemove(file)"><Delete /></el-icon>
                  </div>
                </div>
              </template>
            </el-upload>
            <div class="upload-tip">支持jpg、png格式，最多上传5张图片</div>
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 房间详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="房间详情"
      width="60%"
      class="detail-dialog"
      :close-on-click-modal="false"
    >
      <el-form label-width="100px" class="detail-form">
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <div class="form-grid">
            <el-form-item label="房型">
              <div class="info-value">
                {{ selectedRoom?.type }}
                <el-tag
                  v-if="selectedRoom?.isFeatured"
                  type="danger"
                  effect="dark"
                  size="small"
                  class="featured-tag"
                >
                  推荐房型
                </el-tag>
              </div>
            </el-form-item>
            <el-form-item label="房间面积">
              <div class="info-value">{{ selectedRoom?.area }}㎡</div>
            </el-form-item>
            <el-form-item label="最大入住">
              <div class="info-value">{{ selectedRoom?.capacity }}人</div>
            </el-form-item>
            <el-form-item label="床型">
              <div class="info-value">{{ selectedRoom?.bedType }}</div>
            </el-form-item>
            <el-form-item label="楼层">
              <div class="info-value">{{ selectedRoom?.floor }}</div>
            </el-form-item>
            <el-form-item label="景观">
              <div class="info-value">{{ selectedRoom?.view }}</div>
            </el-form-item>
          </div>
        </div>

        <div class="form-section">
          <div class="section-title">配套设施</div>
          <el-form-item>
            <div class="amenities-list">
              <el-tag
                v-for="amenity in selectedRoom?.amenities"
                :key="amenity"
                size="small"
                effect="plain"
                class="amenity-tag"
              >
                <el-icon><Check /></el-icon>
                {{ amenity }}
              </el-tag>
            </div>
          </el-form-item>
        </div>

        <div class="form-section">
          <div class="section-title">房间描述</div>
          <el-form-item>
            <div class="description-box">
              {{ selectedRoom?.description }}
            </div>
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 预订信息对话框 -->
    <el-dialog
      v-model="bookingDialogVisible"
      title="预订详情"
      width="65%"
      class="booking-dialog"
      :close-on-click-modal="false"
    >
      <div class="booking-container">
        <!-- 日历部分 -->
        <div class="calendar-section">
          <el-calendar v-model="currentDate">
            <template #dateCell="{ data }">
              <div
                class="calendar-cell"
                :class="{
                  'has-booking': hasBooking(data.day),
                  'past-date': isPastDate(data.day),
                }"
              >
                <span>{{ data.day.split("-")[2] }}</span>
                <div v-if="hasBooking(data.day)" class="booking-indicator" />
              </div>
            </template>
          </el-calendar>
        </div>

        <!-- 预订信息展示部分 -->
        <el-card class="booking-card" shadow="hover">
          <div class="info-header">
            <h3 class="section-title">{{ formatDate(selectedDate) }} 预订信息</h3>
          </div>
          <el-form label-width="100px" class="booking-form">
            <div class="form-section">
              <div class="section-title">预订详情</div>
              <el-form-item label="预订人">
                <div class="info-value">{{ selectedDateBooking?.guestName }}</div>
              </el-form-item>
              <el-form-item label="联系电话">
                <div class="info-value">{{ selectedDateBooking?.phone }}</div>
              </el-form-item>
              <el-form-item label="入住时间">
                <div class="info-value">{{ selectedDateBooking?.checkIn }}</div>
              </el-form-item>
              <el-form-item label="退房时间">
                <div class="info-value">{{ selectedDateBooking?.checkOut }}</div>
              </el-form-item>
              <el-form-item label="预订状态">
                <div class="info-value">
                  <el-tag type="success">已确认</el-tag>
                </div>
              </el-form-item>
            </div>
          </el-form>
        </el-card>
        <!-- <div v-else-if="selectedDate" class="no-booking-info">
          <el-empty description="该日期暂无预订信息" />
        </div> -->
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="bookingDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  User,
  OfficeBuilding,
  Picture,
  Edit,
  Delete,
  Plus,
  Setting,
  Star,
  CircleCheck,
  Warning,
  Check,
} from "@element-plus/icons-vue";
import type { UploadProps, UploadUserFile } from "element-plus";

interface Facility {
  name: string;
  desc: string;
  icon: string;
}

interface Room {
  id: number;
  type: string;
  roomNumber: string;
  images: string[];
  price: number;
  originalPrice?: number;
  discount?: number;
  facilities: Facility[];
  description: string;
  status: string;
  area: number;
  capacity: number;
  isFeatured: boolean;
  amenities: string[];
  bedType: string;
  view: string;
  floor: string;
  isBooked: boolean;
  bookingInfo?: BookingInfo;
}

interface BookingInfo {
  orderId: number;
  orderNo: string;
  guestName: string;
  checkIn: string;
  checkOut: string;
  phone: string;
  status: number;
}

// 响应式状态
const searchQuery = ref("");
const sortKey = ref("");
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const dialogVisible = ref(false);
const dialogTitle = ref("添加房间");
const fileList = ref<UploadUserFile[]>([]);
const detailDialogVisible = ref(false);
const bookingDialogVisible = ref(false);
const selectedRoom = ref<Room | null>(null);
const currentDate = ref(new Date());
const selectedDate = ref("");
const selectedDateBooking = ref<BookingInfo | null>(null);

// 表单数据
const form = reactive({
  id: 0,
  type: "",
  price: 0,
  area: 0,
  capacity: 1,
  facilities: [] as Facility[],
  description: "",
  status: "可用",
  images: [] as string[],
  isFeatured: false,
  floor: "1",
});

// 设施选项
const facilityOptions = [
  { name: "WiFi", desc: "高速无线网络", icon: "Platform" },
  { name: "空调", desc: "独立温控系统", icon: "ColdDrink" },
  { name: "卫浴", desc: "干湿分离浴室", icon: "WaterCup" },
  { name: "景观", desc: "180度海景阳台", icon: "View" },
];

// 静态房间数据
const roomList = ref<Room[]>([
  {
    id: 1001,
    type: "豪华海景大床房",
    roomNumber: "0601",
    images: ["/src/assets/rooms/1001-1.png", "/src/assets/rooms/1001-2.png"],
    price: 1280,
    originalPrice: 1580,
    discount: 8,
    facilities: [
      { name: "WiFi", desc: "高速无线网络", icon: "Platform" },
      { name: "空调", desc: "中央空调", icon: "ColdDrink" },
      { name: "卫浴", desc: "独立卫浴", icon: "WaterCup" },
    ],
    description: "45平米豪华海景房，配备2米大床，全景落地窗",
    status: "可用",
    area: 45,
    capacity: 2,
    isFeatured: true,
    amenities: ["雨淋花洒", "智能马桶", "定制洗护"],
    bedType: "2米大床",
    view: "一线海景",
    floor: "6",
    isBooked: true,
    bookingInfo: {
      orderId: 1,
      orderNo: "H202403150001",
      guestName: "张三",
      checkIn: "2024-03-20",
      checkOut: "2024-03-22",
      phone: "13800138000",
      status: 2,
    },
  },
  {
    id: 1002,
    type: "行政套房",
    roomNumber: "1101",
    images: ["/src/assets/rooms/1001-1.png", "/src/assets/rooms/1001-2.png"],
    price: 2180,
    originalPrice: 2580,
    discount: 8.5,
    facilities: [
      { name: "WiFi", desc: "千兆网络", icon: "Platform" },
      { name: "办公", desc: "办公区域", icon: "OfficeBuilding" },
      { name: "会客", desc: "独立会客厅", icon: "User" },
    ],
    description: "68平米豪华套房，独立会客区和办公区",
    status: "可用",
    area: 68,
    capacity: 2,
    isFeatured: true,
    amenities: ["行政酒廊", "欢迎水果", "免费早餐"],
    bedType: "1.8米大床",
    view: "城市景观",
    floor: "11",
    isBooked: false,
  },
  {
    id: 1003,
    type: "家庭亲子套房",
    roomNumber: "0801",
    images: ["/src/assets/rooms/1001-3.png", "/src/assets/rooms/1001-4.png"],
    price: 1680,
    originalPrice: 1980,
    discount: 8.5,
    facilities: [
      { name: "游乐", desc: "儿童游乐区", icon: "Platform" },
      { name: "安全", desc: "儿童防护", icon: "Lock" },
      { name: "卫浴", desc: "儿童卫浴", icon: "WaterCup" },
    ],
    description: "55平米亲子套房，配备儿童床和游乐设施",
    status: "可用",
    area: 55,
    capacity: 3,
    isFeatured: false,
    amenities: ["儿童洗护", "消毒柜", "儿童拖鞋"],
    bedType: "1.8米大床+儿童床",
    view: "花园景观",
    floor: "8",
    isBooked: false,
  },
  {
    id: 1004,
    type: "豪华双床房",
    roomNumber: "0501",
    images: ["/src/assets/rooms/1001-5.png", "/src/assets/rooms/1001-6.png"],
    price: 980,
    originalPrice: 1280,
    discount: 7.5,
    facilities: [
      { name: "WiFi", desc: "高速无线", icon: "Platform" },
      { name: "空调", desc: "中央空调", icon: "ColdDrink" },
      { name: "卫浴", desc: "独立卫浴", icon: "WaterCup" },
    ],
    description: "40平米标准房，配备两张1.2米单人床",
    status: "维护中",
    area: 40,
    capacity: 2,
    isFeatured: false,
    amenities: ["免费洗护", "矿泉水", "拖鞋"],
    bedType: "双床",
    view: "城市景观",
    floor: "5",
    isBooked: false,
  },
  {
    id: 1005,
    type: "商务大床房",
    roomNumber: "0301",
    images: ["/src/assets/rooms/1001-1.png", "/src/assets/rooms/1001-2.png"],
    price: 880,
    facilities: [
      { name: "WiFi", desc: "高速无线", icon: "Platform" },
      { name: "办公", desc: "办公桌", icon: "OfficeBuilding" },
      { name: "卫浴", desc: "独立卫浴", icon: "WaterCup" },
    ],
    description: "35平米商务房，配备办公桌椅",
    status: "可用",
    area: 35,
    capacity: 2,
    isFeatured: false,
    amenities: ["免费洗护", "欢迎水果", "咖啡机"],
    bedType: "1.8米大床",
    view: "城市景观",
    floor: "3",
    isBooked: false,
  },
  {
    id: 1006,
    type: "豪华套房",
    roomNumber: "1001",
    images: ["/src/assets/rooms/1001-1.png", "/src/assets/rooms/1001-2.png"],
    price: 2580,
    originalPrice: 3080,
    discount: 8.5,
    facilities: [
      { name: "WiFi", desc: "千兆网络", icon: "Platform" },
      { name: "空调", desc: "中央空调", icon: "ColdDrink" },
      { name: "卫浴", desc: "豪华浴室", icon: "WaterCup" },
      { name: "会客", desc: "独立会客厅", icon: "User" },
    ],
    description: "80平米豪华套房，独立会客厅和餐厅，尊贵享受",
    status: "可用",
    area: 80,
    capacity: 4,
    isFeatured: true,
    amenities: ["私人管家", "欢迎香槟", "高级洗护", "24小时管家服务"],
    bedType: "2米大床+1.5米沙发床",
    view: "270度全海景",
    floor: "10",
    isBooked: false,
  },
  {
    id: 1007,
    type: "总统套房",
    roomNumber: "3601",
    images: ["/src/assets/rooms/1001-1.png", "/src/assets/rooms/1001-2.png"],
    price: 5880,
    originalPrice: 6880,
    discount: 8.5,
    facilities: [
      { name: "WiFi", desc: "专属网络", icon: "Platform" },
      { name: "空调", desc: "独立温控", icon: "ColdDrink" },
      { name: "卫浴", desc: "豪华浴室", icon: "WaterCup" },
      { name: "会客", desc: "会客厅", icon: "User" },
      { name: "厨房", desc: "私人厨房", icon: "Food" },
    ],
    description: "180平米总统套房，配备私人厨房、会客厅、餐厅，尊享顶级服务",
    status: "可用",
    area: 180,
    capacity: 6,
    isFeatured: true,
    amenities: ["私人管家", "专属电梯", "定制餐饮", "机场接送", "高级红酒"],
    bedType: "2.2米特大床+2个1.8米大床",
    view: "360度全景",
    floor: "36",
    isBooked: false,
  },
  {
    id: 1008,
    type: "亲子主题套房",
    roomNumber: "0802",
    images: ["/src/assets/rooms/1001-1.png", "/src/assets/rooms/1001-2.png"],
    price: 1980,
    originalPrice: 2380,
    discount: 8.3,
    facilities: [
      { name: "游乐", desc: "儿童游乐区", icon: "Platform" },
      { name: "安全", desc: "儿童防护", icon: "Lock" },
      { name: "卫浴", desc: "儿童卫浴", icon: "WaterCup" },
      { name: "影音", desc: "儿童影音室", icon: "Video" },
    ],
    description: "65平米亲子主题套房，配备儿童游乐区、儿童影音室，全方位儿童设施",
    status: "可用",
    area: 65,
    capacity: 4,
    isFeatured: false,
    amenities: ["儿童洗护", "儿童餐具", "儿童图书", "儿童影片", "儿童玩具"],
    bedType: "1.8米大床+儿童床+儿童帐篷",
    view: "花园景观",
    floor: "8",
    isBooked: false,
  },
]);

// 计算属性：过滤和排序后的房间列表
const sortedFilteredRoomList = computed(() => {
  let list = roomList.value.filter((room) =>
    room.type.toLowerCase().includes(searchQuery.value.toLowerCase())
  );

  if (sortKey.value === "priceAsc") {
    list = list.sort((a, b) => a.price - b.price);
  } else if (sortKey.value === "priceDesc") {
    list = list.sort((a, b) => b.price - a.price);
  }

  total.value = list.length;
  return list.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value);
});

// 方法定义
const handleSearch = () => {
  currentPage.value = 1;
};

const handleAdd = () => {
  dialogTitle.value = "添加房间";
  dialogVisible.value = true;
  resetForm();
};

const handleEdit = (index: number, row: Room) => {
  dialogTitle.value = "编辑房间";
  Object.assign(form, row);
  dialogVisible.value = true;
};

const handleDelete = (index: number, row: Room) => {
  ElMessageBox.confirm("确定要删除该房间吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    roomList.value.splice(index, 1);
    ElMessage.success("删除成功");
  });
};

const handleDetail = (room: Room) => {
  selectedRoom.value = room;
  detailDialogVisible.value = true;
};

const handleRoomStatus = (room: Room) => {
  ElMessageBox.confirm(
    `是否要${room.status === "可用" ? "将房间设置为维护中" : "将房间恢复为可用"}？`,
    "修改房间状态",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  ).then(() => {
    room.status = room.status === "可用" ? "维护中" : "可用";
    ElMessage.success("房间状态已更新");
  });
};

const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
};

const handleExceed: UploadProps["onExceed"] = (files) => {
  ElMessage.warning(`最多只能上传 5 张图片`);
};

const handleRemove = (file: UploadUserFile) => {
  const index = fileList.value.indexOf(file);
  if (index !== -1) {
    fileList.value.splice(index, 1);
  }
};

const resetForm = () => {
  Object.assign(form, {
    id: 0,
    type: "",
    price: 0,
    area: 0,
    capacity: 1,
    facilities: [],
    description: "",
    status: "可用",
    images: [],
    isFeatured: false,
    floor: "1",
  });
  fileList.value = [];
};

const handleSubmit = () => {
  if (form.id === 0) {
    // 添加新房间
    const newRoom: Room = {
      ...form,
      id: roomList.value.length + 1001,
      roomNumber: generateRoomNumber(form.floor || "1"),
      isFeatured: false,
      isBooked: false,
      amenities: [],
      bedType: "",
      view: "",
      floor: form.floor || "1",
      images: fileList.value.map((file) => URL.createObjectURL(file.raw!)),
    };
    roomList.value.push(newRoom);
    ElMessage.success("添加成功");
  } else {
    // 更新现有房间
    const index = roomList.value.findIndex((room) => room.id === form.id);
    if (index !== -1) {
      roomList.value[index] = { ...roomList.value[index], ...form };
      ElMessage.success("更新成功");
    }
  }
  dialogVisible.value = false;
};

const generateRoomNumber = (floor: string) => {
  const floorNum = floor.padStart(2, "0");
  const roomCount = roomList.value.filter((room) => room.floor === floor).length + 1;
  return `${floorNum}${roomCount.toString().padStart(2, "0")}`;
};

const showBookingInfo = (room: Room) => {
  selectedRoom.value = room;
  currentDate.value = new Date(); // 设置为当天
  const today = new Date().toISOString().split("T")[0];
  selectedDate.value = today;

  // 检查当天是否有预订
  if (hasBooking(today)) {
    selectedDateBooking.value = room.bookingInfo || null;
  } else {
    selectedDateBooking.value = null;
  }

  bookingDialogVisible.value = true;
};

const handleFeatureToggle = (room: Room) => {
  room.isFeatured = !room.isFeatured;
  ElMessage.success(`${room.type}${room.isFeatured ? "已设为推荐房型" : "已取消推荐"}`);
};

const hasBooking = (day: string) => {
  const booking = selectedRoom.value?.bookingInfo;
  if (!booking) return false;

  const date = new Date(day);
  const checkIn = new Date(booking.checkIn);
  const checkOut = new Date(booking.checkOut);

  // 只显示当天及之后的预订
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  return date >= today && date >= checkIn && date <= checkOut;
};

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "long",
    day: "numeric",
    weekday: "long",
  });
};

const isPastDate = (day: string) => {
  const date = new Date(day);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return date < today;
};

watch(
  () => currentDate.value,
  (newDate) => {
    const dateStr = newDate.toISOString().split("T")[0];
    selectedDate.value = dateStr;

    if (hasBooking(dateStr)) {
      selectedDateBooking.value = selectedRoom.value?.bookingInfo || null;
    } else {
      selectedDateBooking.value = null;
    }
  }
);
</script>

<style lang="scss" scoped>
.room-admin {
  padding: 20px;
  background-color: #f5f7fa;

  .search-bar {
    margin-bottom: 20px;
    padding: 15px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .room-card-col {
    margin-bottom: 20px;
  }

  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: all 0.3s;
    border: none;
    border-radius: 8px;
    overflow: hidden;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    &.disabled-card {
      opacity: 0.7;
      background-color: #fafafa;
    }

    .card-header {
      position: relative;

      .room-image {
        width: 100%;
        height: 180px;
        object-fit: cover;
      }

      .image-placeholder {
        height: 180px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #f5f7fa;
        color: #909399;
      }

      .feature-tag {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1;
        border-radius: 4px;
      }
    }

    .card-body {
      flex: 1;
      padding: 15px;
      background: #fff;

      .room-title {
        margin: 0 0 12px;
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }

      .room-meta {
        margin-bottom: 12px;

        .meta-item {
          display: flex;
          align-items: center;
          margin-bottom: 4px;
          color: #606266;
          font-size: 13px;

          .el-icon {
            margin-right: 6px;
            font-size: 16px;
          }
        }
      }

      .price-section {
        margin-bottom: 12px;
        display: flex;
        align-items: center;

        .current-price {
          font-size: 20px;
          color: #f56c6c;
          font-weight: bold;

          small {
            font-size: 12px;
            font-weight: normal;
            margin-left: 2px;
          }
        }

        .original-price {
          margin-left: 8px;
          color: #909399;
          text-decoration: line-through;
          font-size: 13px;
        }

        .discount-tag {
          margin-left: 8px;
          transform: scale(0.8);
        }
      }

      .facilities {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        margin-bottom: 12px;

        .facility-item {
          display: flex;
          align-items: center;
          padding: 2px 6px;
          background-color: #f5f7fa;
          border-radius: 4px;
          font-size: 12px;
          color: #606266;

          .el-icon {
            margin-right: 4px;
            font-size: 14px;
          }
        }
      }

      .action-buttons {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 8px;
        margin-top: auto;

        .booking-status {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }

    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 15px;
      background: #fff;
      border-top: 1px solid #ebeef5;

      .status-tag {
        transform: scale(0.9);
        transform-origin: left;
      }

      .operation-buttons {
        .el-button {
          padding: 4px;

          &.delete-btn {
            color: #f56c6c;
          }

          &.featured {
            color: #e6a23c;
          }
        }
      }
    }
  }
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}

:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

.room-form-dialog {
  :deep(.el-dialog__body) {
    padding: 0 20px;
  }

  .room-form {
    .form-section {
      margin-bottom: 24px;
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: bold;
        color: #1890ff;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e8e8e8;
      }

      .form-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;

        .full-width {
          grid-column: span 2;
        }
      }
    }

    :deep(.el-form-item) {
      margin-bottom: 0;

      .el-form-item__label {
        font-weight: 500;
      }

      &.upload-item {
        margin-bottom: 0;
      }
    }

    .facility-option {
      display: flex;
      align-items: center;
      gap: 8px;

      .el-icon {
        font-size: 16px;
        color: #1890ff;
      }

      small {
        color: #909399;
        margin-left: auto;
      }
    }

    .status-option {
      display: flex;
      align-items: center;
      gap: 8px;

      .el-icon {
        font-size: 16px;

        &.success {
          color: #67c23a;
        }

        &.warning {
          color: #e6a23c;
        }
      }
    }

    .featured-switch {
      display: flex;
      align-items: center;
      height: 32px;
    }

    .room-uploader {
      :deep(.el-upload--picture-card) {
        width: 148px;
        height: 148px;
        margin: 0 8px 8px 0;
      }

      .upload-trigger {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #909399;

        .upload-text {
          margin-top: 8px;
          font-size: 12px;
        }
      }

      .upload-preview {
        position: relative;
        width: 100%;
        height: 100%;

        .upload-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .upload-actions {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          background: rgba(0, 0, 0, 0.6);
          opacity: 0;
          transition: opacity 0.2s;

          .delete {
            color: #fff;
            font-size: 20px;
            cursor: pointer;
          }
        }

        &:hover .upload-actions {
          opacity: 1;
        }
      }
    }

    .upload-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 8px;
    }
  }
}

.full-width {
  width: 100%;
}

.detail-dialog {
  :deep(.el-dialog__body) {
    padding: 0 20px;
  }

  .detail-form {
    .form-section {
      margin-bottom: 24px;
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: bold;
        color: #1890ff;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e8e8e8;
      }

      .form-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
      }
    }

    .info-value {
      color: #303133;
      font-size: 14px;

      .featured-tag {
        margin-left: 8px;
        transform: scale(0.9);
      }
    }

    .amenities-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .amenity-tag {
        display: flex;
        align-items: center;
        gap: 4px;

        i {
          color: #67c23a;
        }
      }
    }

    .description-box {
      padding: 12px;
      background: white;
      border-radius: 4px;
      color: #606266;
      line-height: 1.6;
    }
  }
}

.booking-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }

  .booking-container {
    display: flex;
    gap: 20px;

    .calendar-section {
      flex: 1;
      min-width: 300px;

      :deep(.el-calendar) {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

        .el-calendar__header {
          padding: 12px 20px;
          border-bottom: 1px solid #ebeef5;
        }

        .calendar-cell {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          cursor: pointer;

          &.has-booking {
            color: #67c23a;
            font-weight: bold;
          }

          &.past-date {
            color: #c0c4cc;
            cursor: not-allowed;
          }

          .booking-indicator {
            position: absolute;
            bottom: 4px;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #67c23a;
          }
        }
      }
    }

    .booking-info-section {
      flex: 1;
      min-width: 300px;

      .info-header {
        margin-bottom: 20px;

        h3 {
          margin: 0;
          color: #303133;
          font-size: 18px;
        }
      }
    }

    .no-booking-info {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #fff;
      border-radius: 8px;
      padding: 40px;
    }
  }
}
.booking-card {
  margin-top: 20px;
  border-radius: 8px;
  transition: all 0.3s ease;

  .info-header {
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;

    .section-title {
      font-size: 16px;
      color: #303133;
      font-weight: 600;
    }
  }

  .el-form-item {
    margin-bottom: 12px;

    :deep(.el-form-item__label) {
      color: #606266;
      font-weight: 500;
    }

    .info-value {
      color: #303133;
      font-size: 14px;
    }
  }

  .el-tag {
    padding: 0 8px;
    height: 24px;
    line-height: 24px;
    border-radius: 4px;
  }
}
</style>
