import request from "@/utils/request";

const SYSACODE_BASE_URL = "/api/v1/sysAcodes";

const SysAcodeAPI = {
  /** 获取zz分页数据 */
  getPage(queryParams?: SysAcodePageQuery) {
    return request<any, PageResult<SysAcodePageVO[]>>({
      url: `${SYSACODE_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },

  /** 获取zz分页数据 */
  getList(queryParams?: SysAcodePageQuery) {
    return request<any, SysAcodePageVO[]>({
      url: `${SYSACODE_BASE_URL}/list`,
      method: "get",
      params: queryParams,
    });
  },

  /** 获取下拉列表 */
  getOptions() {
    return request<any, OptionType[]>({
      url: `${SYSACODE_BASE_URL}/options`,
      method: "get",
    });
  },

  /**
   * 获取zz表单数据
   *
   * @param id SysAcodeID
   * @returns SysAcode表单数据
   */
  getFormData(id: number) {
    return request<any, SysAcodeForm>({
      url: `${SYSACODE_BASE_URL}/${id}/form`,
      method: "get",
    });
  },

  /** 添加zz*/
  add(data: SysAcodeForm) {
    return request({
      url: `${SYSACODE_BASE_URL}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新zz
   *
   * @param id SysAcodeID
   * @param data SysAcode表单数据
   */
  update(id: number, data: SysAcodeForm) {
    return request({
      url: `${SYSACODE_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 批量删除zz，多个以英文逗号(,)分割
   *
   * @param ids zzID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${SYSACODE_BASE_URL}/${ids}`,
      method: "delete",
    });
  },

  /** 获取分页数据 */
  getAcodelist(queryParams?: SysAcodePageQuery) {
    return request<any, PageResult<SysAcodePageVO[]>>({
      url: `${SYSACODE_BASE_URL}/get-acodelist`,
      method: "get",
      params: queryParams,
    });
  },
};

export default SysAcodeAPI;

/** zz分页查询参数 */
export interface SysAcodePageQuery extends PageQuery {
  preCode?: string;
  name?: string;
}

/** zz表单对象 */
export interface SysAcodeForm {
  /** 自增ID */
  id?: number;
  /** 自定义结构化编码 */
  xcode?: string;
  /** 首码 */
  ycode?: string;
  /** 规定编码 */
  coderule?: string;
  /** 编码名称 */
  name?: string;
  /** 1为数据边界栏位，0为非数据边界栏位 */
  dataflag?: string;
  /** 排序 */
  sort?: number;
  /** 备注 */
  remark?: string;
  /** 启用状态，（1启用，0禁用） */
  flag?: number;
  notes?: string;
  /** 所属部门,点集合 */
  by1?: string;
  by2?: string;
  /** 所属分类编码 */
  by3?: string;
  mburl?: string;

  parentid?: number;
}

/** zz分页对象 */
export interface SysAcodePageVO {
  /** 自增ID */
  id?: number;
  /** 自定义结构化编码 */
  xcode?: string;
  /** 首码 */
  ycode?: string;
  /** 规定编码 */
  coderule?: string;
  /** 编码名称 */
  name?: string;
  /** 1为数据边界栏位，0为非数据边界栏位 */
  dataflag?: string;
  /** 排序 */
  sort?: number;
  /** 备注 */
  remark?: string;
  /** 启用状态，（1启用，0禁用） */
  flag?: string;
  notes?: string;
  /** 所属部门,点集合 */
  by1?: string;
  by2?: string;
  /** 所属分类编码 */
  by3?: string;
  mburl?: string;
  parentid?: number;
  children?: SysAcodePageVO[];
}
