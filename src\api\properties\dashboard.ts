import request from "@/utils/request";
/** 流程日志组件模块API */
const WORKFLOW_BASE_URL = "/api/v1/workflow/todolist/";
const PROPERTY_BASE_URL = "/api/v1/property/";

const dashboardAPI = {
  //结束待办
  delToDoById(params: { id: string }) {
    return request({
      url: `${WORKFLOW_BASE_URL}` + "end",
      method: "get",
      params: params,
    });
  },
  //个人待办分页列表
  getToDoList(params: { pageNum: number; pageSize: number; sxmc: string; ucode: string }) {
    return request({
      url: `${WORKFLOW_BASE_URL}` + "get",
      method: "get",
      params: params,
    });
  },
  //个人待办分页列表
  getToDoListAll(params: { pageNum: number; pageSize: number; sxmc: string; ucode: string }) {
    return request({
      url: `${WORKFLOW_BASE_URL}` + "get-all",
      method: "get",
      params: params,
    });
  },
  //个人分类待办
  getGroupToDoList() {
    return request({
      url: `${WORKFLOW_BASE_URL}` + "get-group",
      method: "get",
    });
  },
  //资产统计
  getAssetSStatistics() {
    return request({
      url: `${PROPERTY_BASE_URL}` + "get-propertystatistics",
      method: "get",
    });
  },
  /**
   * 获取资产预警
   */
  getPropertyWarningList() {
    return request({
      url: `${PROPERTY_BASE_URL}` + "get-propertywarninglist",
      method: "get",
    });
  },
  /**
   * 获取资产预警详情
   */
  getPropertyWarningInfo(params: {
    zcbh: string;
    zcmc: string;
    syr: string;
    syrbm: string;
    zcbm: string;
  }) {
    return request({
      url: `${PROPERTY_BASE_URL}` + "get-propertywarninginfo",
      method: "get",
      params: params,
    });
  },
  /**
   * 获取资产预警详情分页列表
   */
  getPropertyWarningInfoPage(params: {
    zcbh: string;
    zcmc: string;
    syr: string;
    syrbm: string;
    zcbm: string;
    pageNum: number;
    pageSize: number;
  }) {
    console.log("发送1");
    console.log(params);
    return request({
      url: `${PROPERTY_BASE_URL}` + "get-propertywarninginfopage",
      method: "get",
      params: params,
    });
  },
  /**
   * 获取资产概况
   */
  getPropertyMyCard() {
    return request<any, tjType[]>({
      url: `${PROPERTY_BASE_URL}` + "get-propertymycard",
      method: "get",
    });
  },
};

export default dashboardAPI;

export interface tjType {
  JE: number;
  SL: number;
  ZCTYPE: string;
}
