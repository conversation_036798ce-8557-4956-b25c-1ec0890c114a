<!-- cursor测试:1 -->
<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item v-if="add || view || check" label="验收单号" prop="ysdh">
            <el-input
              v-model="queryParams.ysdh"
              placeholder="验收单号"
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon>
                <Search />
              </template>
              搜索
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon>
                <Refresh />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="验收单列表"></Title>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        stripe
        border
      >
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column key="ysdh" label="验收单号" prop="ysdh" min-width="120" align="center" />
        <el-table-column key="jfmc" label="经费名称" prop="jfmc" min-width="200" align="center" />

        <el-table-column key="je" label="总金额" prop="je" min-width="120" align="center" />
        <el-table-column key="sl" label="数量" prop="sl" min-width="100" align="center" />

        <el-table-column
          key="nickname"
          label="登记人"
          prop="nickname"
          min-width="120"
          align="center"
        />

        <el-table-column fixed="right" label="操作" width="120">
          <template #default="scope">
            <el-button
              v-if="view"
              type="success"
              size="small"
              icon="Document"
              @click="handleOpenDialog(false, scope.row, scope.row.czcode)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 10"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 入库单的弹窗 -->
    <el-drawer v-model="dialog.storeVisible" :title="dialog.title" append-to-body size="85%">
      <!-- 设置V-if是为了关闭弹窗的时候销毁组件 -->
      <StoreInfo
        :id="itemId"
        :key="itemId"
        :guid="itemGuid"
        :editable="itemEditable"
        :zclxbh="itemZclxbh"
        :netcode="itemNetcode"
        :dcbm="itemDcbm"
        :czcode="itemCzcode"
        :RefreshFatherDrawer="handleCloseDialog"
      />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import acceptAPI, { acceptPageVO, acceptPageQuery } from "@/api/properties/accept";
import StoreInfo from "@/views/properties/store/components/index.vue";
import { getGuid } from "@/utils/guid";
//————————————————————————————————————————————暴露的方法,和请求参数
function RefreshFatherDrawer(close?: boolean) {
  //是否关闭弹窗由参数决定，默认不关闭
  if (close) {
    dialog.storeVisible = false;
  }
  handleQuery();
}
// 获取路由参数,view/add/check
const route = useRoute();
const type = ref(route.query.type?.toString() || "view");
//显示隐藏权限,用于v-if
const add = type.value == "add";
const view = type.value == "view";
const check = type.value == "check";

//————————————————————————————————————————————查询相关
const queryFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);
const queryParams = reactive<acceptPageQuery>({
  pageNum: 1,
  pageSize: 10,
});
// 验收表格数据
const pageData = ref<acceptPageVO[]>([]);

/** 查询验收 */
function handleQuery() {
  loading.value = true;
  console.log("刷新列表数据");
  acceptAPI
    .getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
      console.log("data");
      console.log(pageData.value);
    })
    .catch((error) => {
      console.error("获取验收数据失败:", error);
      ElMessage.error("获取验收数据失败");
      pageData.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置验收查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}
const handleChangeRadio = () => {
  handleResetQuery();
};

//————————————————————————————————————————————弹窗相关
// 弹窗
const dialog = reactive({
  title: "",
  storeVisible: false,
  accountVisible: false,
});
/** 打开调拨弹窗 */
const itemGuid = ref("");
const itemId = ref();
const itemZclxbh = ref();
const itemEditable = ref(false);
const itemCzcode = ref("");
const itemDcbm = ref<string>("");
const itemNetcode = ref<string>("");
function handleOpenDialog(editable: boolean, row?: any, czcode?: string) {
  itemDcbm.value = row?.dcbm || "";
  itemNetcode.value = row?.netcode || "";
  itemEditable.value = editable;
  //打开弹窗，如果没有guid则生成一个GUID
  itemGuid.value = row?.guid || getGuid();
  itemId.value = row?.id;
  itemZclxbh.value = row?.zclxbh;
  itemCzcode.value = czcode || "";
  dialog.storeVisible = true;
}

/** 关闭调拨弹窗 */
function handleCloseDialog() {
  dialog.storeVisible = false;
  handleQuery();
}

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}

.flex {
  display: flex;
  flex-direction: row;
}
</style>
