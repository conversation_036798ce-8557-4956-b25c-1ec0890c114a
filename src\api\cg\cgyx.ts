import request from "@/utils/request";

const ZXCGYXB_BASE_URL = "/api/v1/zxcgYxbs";

const ZxcgYxbAPI = {
  /** 获取意向分页数据 */
  getPage(queryParams?: ZxcgYxbPageQuery) {
    return request<any, PageResult<ZxcgYxbPageVO[]>>({
      url: `${ZXCGYXB_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },
  /** 获取意向分页数据,给采购申请选意向使用 */
  SqSelgetPage(queryParams?: ZxcgYxbPageQuery) {
    return request<any, PageResult<ZxcgYxbPageVO[]>>({
      url: `${ZXCGYXB_BASE_URL}/sqsel/page`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取意向表单数据
   *
   * @param id ZxcgYxbID
   * @returns ZxcgYxb表单数据
   */
  getFormData(id: number) {
    return request<any, ZxcgYxbForm>({
      url: `${ZXCGYXB_BASE_URL}/${id}/form`,
      method: "get",
    });
  },

  /** 添加意向*/
  add(data: ZxcgYxbForm) {
    return request({
      url: `${ZXCGYXB_BASE_URL}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新意向
   *
   * @param id ZxcgYxbID
   * @param data ZxcgYxb表单数据
   */
  update(id: number, data: ZxcgYxbForm) {
    return request({
      url: `${ZXCGYXB_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 批量删除意向，多个以英文逗号(,)分割
   *
   * @param ids 意向ID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${ZXCGYXB_BASE_URL}/${ids}`,
      method: "delete",
    });
  },
};

export default ZxcgYxbAPI;

/** 意向分页查询参数 */
export interface ZxcgYxbPageQuery extends PageQuery {
  /** 采购意向单号  年月+顺序号4位：10位 */
  cgyxdh?: string;
  /** 预算项目编码 */
  ysxmbh?: string;
  /** 预算项目名称 */
  ysxmmc?: string;
  /**经费负责人姓名 */
  xmfzrname?: string;
  /**登记部门 */
  djbm?: string;
}

/** 意向表单对象 */
export interface ZxcgYxbForm {
  /** 采购意向单号  年月+顺序号4位：10位 */
  cgyxdh?: string;
  /** 预算项目编码 */
  ysxmbh?: string;
  /** 预算项目名称 */
  ysxmmc?: string;
  /** 预算项目编码2 */
  ysxmbh2?: string;
  /** 预算项目名称2 */
  ysxmmc2?: string;
  /** 预算批复时间 */
  yspfsj?: Date;
  /** 采购项目名称 默认=预算项目名称，可改 */
  cgxmmc?: string;
  /** 采购需求概况 */
  cgxqgk?: string;
  /** 预算金额（元） */
  ysje?: number;
  /** 预计采购时间 到年月 */
  yjcgsj?: Date;
  /** 所在部门 */
  szbm?: string;
  /** 项目负责人 */
  xmfzr?: string;
  /** 登记人 */
  djr?: string;
  /** 登记部门 */
  djbm?: string;
  /** 登记时间 */
  djsj?: Date;
  /** 联系人 */
  lxr?: string;
  /** 办公电话 */
  bgdh?: string;
  /** 电子邮箱 */
  email?: string;
  /** 采购类别 */
  cgml?: string;
}

/** 意向分页对象 */
export interface ZxcgYxbPageVO {
  /** 采购意向单号  年月+顺序号4位：10位 */
  cgyxdh?: string;
  /** 预算项目编码 */
  ysxmbh?: string;
  /** 预算项目名称 */
  ysxmmc?: string;
  /** 预算项目编码2 */
  ysxmbh2?: string;
  /** 预算项目名称2 */
  ysxmmc2?: string;
  /** 预算批复时间 */
  yspfsj?: Date;
  /** 采购项目名称 默认=预算项目名称，可改 */
  cgxmmc?: string;
  /** 采购需求概况 */
  cgxqgk?: string;
  /** 预算金额（元） */
  ysje?: number;
  /** 预计采购时间 到年月 */
  yjcgsj?: Date;
  /** 所在部门 */
  szbm?: string;
  /** 项目负责人 */
  xmfzr?: string;
  /** 登记人 */
  djr?: string;
  /** 登记部门 */
  djbm?: string;
  /** 登记时间 */
  djsj?: Date;
  /** 联系人 */
  lxr?: string;
  /** 办公电话 */
  bgdh?: string;
  /** 电子邮箱 */
  email?: string;
  /** 采购类别 */
  cgml?: string;
  /** guid */
  guid?: string;
}
