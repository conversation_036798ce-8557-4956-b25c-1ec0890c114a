import request from "@/utils/request";
import { get } from "sortablejs";

const USER_BASE_URL = "/api/v1/propertyCards";

const PropertyCardsAPI = {
  /**
   * 获取资产分页列表
   *
   * @param queryParams 查询参数
   */
  getPropertyCardList(queryParams: PropertyListQuery) {
    return request<any, PageResult<any[]>>({
      url: `${USER_BASE_URL}/get-mypropertypage`,
      method: "get",
      params: queryParams,
    });
  },
};

export default PropertyCardsAPI;

/**
 * 资产列表查询对象
 */
export interface PropertyListQuery extends PageQuery {
  zcbh?: string;
  syr?: string;
  sybm?: string;
}
