<template>
  <div>
    <el-table stripe :data="pageData" :border="true" style="width: 100%">
      <!-- <el-table-column prop="id" label="序号" width="55" /> -->
      <el-table-column label="序号" type="index" width="55" align="center" fixed>
        <template #default="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="fjmc" label="附件名称" />
      <el-table-column prop="fjcodeName" label="附件类型" width="150" />
      <el-table-column prop="filekide" label="文件类型" width="100" />
      <el-table-column prop="filesize" label="文件大小" width="100" />
      <el-table-column prop="djrName" label="登记人" width="100" />
      <el-table-column prop="djsj" label="登记时间" width="150" />
      <el-table-column label="操作" fixed="right" width="100">
        <template #default="scope">
          <el-button type="success" icon="view" size="small" @click="hanclePreView(scope.row.url)">
            预览
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import UniFjsAPI, { fjVo } from "@/api/system/uniFjs";

const props = defineProps({
  guid: {
    type: String,
    required: true,
  },
});
const hanclePreView = (url: string) => {};

const pageData = ref<fjVo[]>([]);

onMounted(() => {
  if (props.guid) {
    UniFjsAPI.getFjList({
      guid: props.guid,
      fjcode: "",
    }).then((res) => {
      pageData.value = res.list;
    });
  }
});
</script>
<style lang="scss" scoped></style>
