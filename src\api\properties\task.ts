import request from "@/utils/request";

const task_BASE_URL = "/api/v1/task";

const taskAPI = {
  /** 获取领用分页数据 */
  getPage(queryParams?: taskPageQuery) {
    return request<any, PageResult<taskPageVO[]>>({
      url: `${task_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },

  /** 获取领用分页数据 */
  getAllPage(queryParams?: any) {
    return request<any, PageResult<any[]>>({
      url: `${task_BASE_URL}/page-byjhguid`,
      method: "get",
      params: queryParams,
    });
  },
  /** 获取领用分页数据 */
  getBmAllPage(queryParams?: any) {
    return request<any, PageResult<any[]>>({
      url: `${task_BASE_URL}/page-bysybm`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取领用表单数据
   *
   * @param id taskID
   * @returns task表单数据
   */
  getFormData(guid: string) {
    return request<any, taskForm>({
      url: `${task_BASE_URL}/form/${guid}`,
      method: "get",
    });
  },

  /** 添加领用*/ // 添加/api/v1/lend/add/{guid}
  add(guid: string) {
    return request({
      url: `${task_BASE_URL}/add/${guid}`,
      method: "post",
    });
  },

  /** 添加领用*/
  addtask(data: taskForm): Promise<boolean> {
    return request<any, boolean>({
      url: `${task_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新领用
   *
   * @param id taskID
   * @param data task表单数据
   */
  update(id: number, data: taskForm): Promise<boolean> {
    return request<any, boolean>({
      url: `${task_BASE_URL}/update/${id}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 提交领用
   *
   * @param id taskID
   * @param data task表单数据
   */
  submit(guid: string, data?: any) {
    return request({
      url: `${task_BASE_URL}/submit/${guid}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 批量删除领用，多个以英文逗号(,)分割
   *
   * @param ids 领用ID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${task_BASE_URL}/delete/${ids}`,
      method: "get",
    });
  },

  //———————————————————————————————— 明细相关接口————————————————————————————
  /** 添加领用明细*/
  addDetail(data: { syGuid: string; zcGuid: string }) {
    return request({
      url: `${task_BASE_URL}/details/add`,
      method: "post",
      data: data,
    });
  },

  //回收/api/v1/tasks/callback/{guid}
  callBack(guid: string) {
    return request({
      url: `${task_BASE_URL}/callback/${guid}`,
      method: "get",
    });
  },

  /**
   * 批量更新卡片
   *
   */
  batchUpdate(ids: string, data: any) {
    return request({
      url: `${task_BASE_URL}/batch/${ids}`,
      method: "post",
      data: data,
    });
  },

  //回收/api/v1/tasks/node/list
  getNodeList() {
    return request({
      url: `${task_BASE_URL}/node/list`,
      method: "get",
    });
  },
};

export default taskAPI;

/** 领用分页查询参数 */
export interface taskPageQuery extends PageQuery {
  /** 资产编号 */
  zcbh?: string;
  /** 资产名称 */
  zcmc?: string;
  /** 存放地点 */
  cfdd?: string;
  /** 年份 */
  nf?: string;
  /** 数据编码 */
  qczt?: string;
  /** 登记时间 */
  djsj?: string[];
  //查询类型，是新增页审核页还是查询页
  type?: string;
  jhguid?: string;
  syr?: string;
  sybm?: string;
  qcjg?: string;
}

/** 领用表单对象 */
export interface taskForm {
  id?: number;
  guid?: string;
  /** 任务编号,年4位+顺序号7，与处置程序关联 */
  rwbh?: string;
  /** 计划guid */
  jhguid?: string;
  /** 计划编号 */
  jhbh?: string;
  /** 资产guid */
  zcguid?: string;
  /** 资产编号 */
  zcbh?: string;
  /** 资产名称 || 默认：财政资产分类名称    必填项 */
  zcmc?: string;
  /** 规格型号 */
  ggxh?: string;
  /** 数量 || 必填项 */
  sl?: number;
  /** 使用人 */
  syr?: string;
  /** 存放地点 ||  可以考虑格式化地址Xcode */
  cfdd?: string;
  cfddname?: string;
  /** 资产使用人所在部门 ||（作废，因为就是保管人所在部门，又重新启用了） */
  sybm?: string;
  /** 资产管理人 || 分类集格式 */
  bmzcgly?: string;
  /** 使用现状 */
  syxz?: string;
  /** 清查情况 */
  qcqk?: string;
  /** 清查结果 */
  qcjg?: string;
  /** 清查状态,集合编码：待清查、提交(已完成清查) */
  qczt?: string;
  /** 登记时间,产生任务时间 */
  djsj?: Date;
  /** 清查时间,清查提交时间 */
  qcsj?: Date;
  /** 是否处置,0-非处置（无需处置）、1-未处置、2-已处置 */
  iscz?: string;
  /** 备注 */
  notes?: string;
}

/** 领用分页对象 */
export interface taskPageVO {
  id?: number;
  guid?: string;
  /** 任务编号,年4位+顺序号7，与处置程序关联 */
  rwbh?: string;
  /** 计划guid */
  jhguid?: string;
  /** 计划编号 */
  jhbh?: string;
  /** 资产guid */
  zcguid?: string;
  /** 资产编号 */
  zcbh?: string;
  /** 资产名称 || 默认：财政资产分类名称    必填项 */
  zcmc?: string;
  /** 规格型号 */
  ggxh?: string;
  /** 数量 || 必填项 */
  sl?: number;
  /** 使用人 */
  syr?: string;
  /** 存放地点 ||  可以考虑格式化地址Xcode */
  cfdd?: string;
  /** 资产使用人所在部门 ||（作废，因为就是保管人所在部门，又重新启用了） */
  sybm?: string;
  /** 资产管理人 || 分类集格式 */
  bmzcgly?: string;
  /** 使用现状 */
  syxz?: string;
  /** 清查情况 */
  qcqk?: string;
  /** 清查结果 */
  qcjg?: string;
  /** 清查状态,集合编码：待清查、提交(已完成清查) */
  qczt?: string;
  /** 登记时间,产生任务时间 */
  djsj?: Date;
  /** 清查时间,清查提交时间 */
  qcsj?: Date;
  /** 是否处置,0-非处置（无需处置）、1-未处置、2-已处置 */
  iscz?: string;
  /** 备注 */
  notes?: string;
}
