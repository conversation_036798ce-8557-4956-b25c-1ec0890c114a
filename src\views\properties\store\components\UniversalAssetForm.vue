<template>
  <div>
    <!-- 通用信息 -->
    <el-form
      :model="formData"
      :rules="rules"
      ref="formRef"
      label-width="120px"
      :inline="true"
      :disabled="!props.editable"
    >
      <el-form-item label="采购申请人" prop="cgsqr" style="width: 100%">
        <table-select
          v-model="formData.cgsqr"
          :text="text"
          :select-config="selectConfig"
          @confirm-click="handleConfirm"
        ></table-select>
      </el-form-item>

      <el-form-item label="申购人手机" prop="cgsqrsj">
        <el-input v-model="formData.cgsqrsj" placeholder="请输入申购人手机" />
      </el-form-item>

      <el-form-item label="申购部门" prop="sgdwbh">
        <el-tree-select
          v-model="formData.sgdwbh"
          placeholder="请选择申购部门"
          :data="deptOptions"
          filterable
          check-strictly
          :render-after-expand="false"
        />
      </el-form-item>

      <el-form-item label="品牌" prop="pp">
        <el-input v-model="formData.pp" placeholder="请输入品牌" />
      </el-form-item>

      <el-form-item label="规格型号" prop="ggxh">
        <el-input v-model="formData.ggxh" placeholder="请输入规格型号" />
      </el-form-item>

      <el-form-item label="合同编号" prop="htbh">
        <el-input v-model="formData.htbh" placeholder="请输入合同编号" />
      </el-form-item>
      <el-form-item label="供应商名称" prop="gysmc">
        <el-input v-model="formData.gysmc" placeholder="请输入供应商名称" />
      </el-form-item>

      <el-form-item label="生产厂家" prop="sccj">
        <el-input v-model="formData.sccj" placeholder="请输入生产厂家" />
      </el-form-item>

      <el-form-item label="生产日期" prop="ccrq">
        <el-date-picker
          v-model="formData.ccrq"
          type="date"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择生产日期"
        />
      </el-form-item>

      <el-form-item label="合同名称" prop="htmc">
        <el-input v-model="formData.htmc" placeholder="请输入合同名称" />
      </el-form-item>

      <el-form-item label="合同金额(元)" prop="htje">
        <el-input v-model="formData.htje" placeholder="请输入合同金额" />
      </el-form-item>

      <el-form-item label="合同日期" prop="htrq">
        <el-date-picker
          v-model="formData.htrq"
          type="date"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择合同日期"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import storeAPI, { ZcinfoDifferentForm } from "@/api/properties/store";
import { ElLoading, ElMessage } from "element-plus";
import selectConfig from "./config/select";
import DeptAPI from "@/api/system/dept";
import UserAPI from "@/api/system/user";
import { useUserStore } from "@/store";
const userStore = useUserStore();
const visibleFin = ref(false);
const deptOptions = ref<OptionType[]>();

// 表单验证规则
const rules = reactive({
  cgsqr: [{ required: true, message: "请选择采购申请人", trigger: "blur" }],
  sgdwbh: [{ required: true, message: "请选择申购部门", trigger: "blur" }],
  pp: [{ required: true, message: "请输入品牌", trigger: "blur" }],
  ggxh: [{ required: true, message: "请输入规格型号", trigger: "blur" }],
  htbh: [{ required: true, message: "请输入合同编号", trigger: "blur" }],
});
const formRef = ref(ElForm);
// 表单引用

// 组件属性定义
const props = defineProps({
  guid: {
    type: String,
  },
  zclxbh: {
    type: String,
  },
  editable: {
    type: Boolean,
    required: true,
  },
});
// 表单数据
const formData = reactive<ZcinfoDifferentForm>({
  zclxbh: props.zclxbh || "",
});
// 组件事件
const emit = defineEmits(["update:modelValue", "ClearBmgly"]);

const selectedUser = ref<any>({});

function handleConfirm(data: any[]) {
  selectedUser.value = data[0];
  formData.cgsqr = selectedUser.value.username;
}
const text = computed(() => {
  console.log("text:", selectedUser.value);
  return selectedUser.value && selectedUser.value.nickname ? `${selectedUser.value.nickname}` : "";
});

// 下拉框选项
const cgzzxsOptions = ref([
  { value: "0", label: "政府集中采购" },
  { value: "1", label: "分散采购" },
  { value: "2", label: "其他" },
]);

// 监听值变化
watch(
  () => [props.guid, props.zclxbh],
  (val) => {
    console.log("props-----------val:", val);
    if (val && val[0] != "" && val[1] != undefined) {
      console.log("props:", val, val[0], val[1]);
      //获取表单数据
      storeAPI.getFormDataDiff({ guid: val[0], zclxbh: val[1] }).then((res) => {
        Object.assign(formData, res);
        console.log("formData.cgsqr", formData.cgsqr);
      });
      formData.zclxbh = val[1];
      formData.guid = val[0];
    }
  },
  { deep: true, immediate: true }
);

watch(
  () => formData.cgsqr,
  (val) => {
    console.log("formData.cgsqr", val);
    if (val && val != "") {
      UserAPI.getFormDatabycode(val).then((res) => {
        console.log("User", res);
        selectedUser.value.nickname = res.nickname || "";
        selectedUser.value.username = res.username || "";
      });
    }
  }
);

watch(
  () => formData.sgdwbh,
  (newval, oldVal) => {
    if (newval && newval != "" && oldVal && oldVal != "") {
      formData.cgsqr = "";
      selectedUser.value = {};
      emit("ClearBmgly", newval);
    }
  }
);

// 选择用户方法
const selectUser = (field: any) => {
  // 这里可以打开用户选择对话框
  if (field === "xmfzr") {
    // TODO: 实现项目负责人选择逻辑
    console.log("选择项目负责人");
  }
};

// 选择部门方法
const selectDept = () => {
  // TODO: 实现部门选择逻辑
  console.log("选择部门");
};

// 提交表单
const submitForm = () => {
  return new Promise((resolve, reject) => {
    formRef.value?.validate((valid) => {
      if (valid) {
        // 显示加载状态
        const loading = ElLoading.service({
          lock: true,
          text: "正在保存...",
          background: "rgba(0, 0, 0, 0.7)",
        });
        console.log("表单提交数据:", formData);
        storeAPI
          .updateDiff(formData)
          .then((res) => {
            // 关闭加载状态
            loading.close();
            // 显示成功消息
            ElMessage({
              type: "success",
              message: "保存成功",
            });
            console.log("表单提交成功:", res);
            resolve(res);
          })
          .catch((error) => {
            // 关闭加载状态
            loading.close();
            // 显示错误消息
            ElMessage({
              type: "error",
              message: "保存失败: " + (error.message || "未知错误"),
            });
            console.error("表单提交失败:", error);
            reject(error);
          });
      } else {
        ElMessage({
          type: "warning",
          message: "请填写必填项",
        });
        reject(new Error("表单验证失败"));
        return false;
      }
    });
  });
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
};

// 表单验证方法
const validateForm = () => {
  return new Promise((resolve, reject) => {
    formRef.value?.validate((valid) => {
      if (valid) {
        resolve(true);
      } else {
        reject(new Error("表单验证失败"));
      }
    });
  });
};

onMounted(() => {
  // 加载部门选项
  DeptAPI.getOptionsCode().then((res) => {
    deptOptions.value = res;
    console.log("部门选项加载完成:", deptOptions.value.length);
  });

  formData.cgsqr = userStore.userInfo.username;
});

// 导出方法供父组件使用
defineExpose({
  submitForm,
  resetForm,
  validate: validateForm,
});
</script>

<style scoped>
.el-form-item {
  width: 330px;
}

.flex {
  display: flex;
  flex-direction: row;
}
</style>
