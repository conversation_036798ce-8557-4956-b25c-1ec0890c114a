import request from "@/utils/request";

const Accept_BASE_URL = "/api/v1/store";

const acceptAPI = {
  /** 获取验收单分页数据 */
  getPage(queryParams?: acceptPageQuery) {
    return request<any, PageResult<acceptPageVO[]>>({
      url: `${Accept_BASE_URL}/page-checkaccept`,
      method: "get",
      params: queryParams,
    });
  },
}

export default acceptAPI;

/** 验收单分页查询参数 */
export interface acceptPageQuery extends PageQuery {
  /** 验收单号 */
  ysdh?: string;
}


/** 验收单分页对象 */
export interface acceptPageVO {
  /** 验收单号 */
  ysdh?: string;
  /** 登记人 */
  nickname?: string;
  /** 经费名称 */
  jfmc?: string;
  /** 数量 */
  sl?: string;
  /** 金额 */
  je?: string;
  /** 金额 */
  djr?: string;
  guid?: string;
  zclxbh?: string;
}


