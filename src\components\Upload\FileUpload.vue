<!-- 文件上传组件 -->
<template>
  <div class="file-upload-container">
    <!-- 文件列表表格 -->
    <div class="table-wrapper">
      <el-card shadow="never">
        <el-table :data="fileList" style="width: 100%" size="large" border>
          <el-table-column type="index" label="序号" width="80" align="center" />
          <el-table-column label="文件类型" min-width="200" align="center">
            <template #default="{ row }">
              <div class="file-name-cell">
                <el-icon>
                  <Notebook />
                </el-icon>
                <span color="red" v-if="row.isRequired">*</span>
                <span class="file-name">{{ row.fjname }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="文件名" min-width="400" align="center">
            <template #default="{ row }">
              <div class="file-name-cell">
                <el-icon>
                  <Document />
                </el-icon>
                <span class="file-name">{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag v-if="row.status === 'success'" type="success">
                {{ row.status === "success" ? "已上传" : "未上传" }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="250" align="center">
            <template #default="{ row }">
              <div class="operation-cell">
                <el-button v-if="row.uid" type="primary" link @click="downloadFile(row)">
                  <el-icon>
                    <Download />
                  </el-icon>
                  下载
                </el-button>
                <el-upload
                  v-if="!row.uid && props.show"
                  :headers="props.headers"
                  :name="props.name"
                  :before-upload="handleBeforeUpload"
                  :on-remove="handleRemove"
                  :on-progress="handleProgress"
                  :on-success="handleSuccessFile"
                  :on-error="handleError"
                  :action="`${props.action}?guid=${props.guid}&code=${row.fjcode}`"
                  :accept="props.accept"
                  :show-file-list="false"
                  class="header-upload"
                >
                  <el-button type="primary" link>
                    <el-icon class="upload-icon">
                      <Upload />
                    </el-icon>
                    上传
                  </el-button>
                </el-upload>
                <el-button
                  v-if="row.uid && props.show"
                  type="danger"
                  link
                  @click="handleRemove(row)"
                >
                  <el-icon>
                    <Delete />
                  </el-icon>
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 上传进度遮罩 -->
        <div v-if="showUploadPercent" class="upload-overlay">
          <div class="upload-progress-content">
            <el-icon class="upload-icon">
              <Upload />
            </el-icon>
            <div class="progress-info">
              <div class="progress-text">正在上传...{{ uploadPercent }}%</div>
              <el-progress
                :percentage="uploadPercent"
                :color="customColorMethod"
                :stroke-width="8"
                :show-text="false"
              />
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  UploadRawFile,
  UploadUserFile,
  UploadFile,
  UploadProgressEvent,
  UploadFiles,
} from "element-plus";

import FileAPI, { FileInfo } from "@/api/file";
import { getToken } from "@/utils/auth";
import { ResultEnum } from "@/enums/ResultEnum";

const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  /**
   * 项目guid
   */
  guid: {
    type: String,
    default: "",
    required: true, //必填
  },
  /*
   * 必填
   */
  code: {
    type: String,
    default: "",
  },
  /**
   * 是否预览
   */
  show: {
    type: Boolean,
    default: true,
  },
  /**
   * 请求头
   */
  headers: {
    type: Object,
    default: () => {
      return {
        Authorization: getToken(),
      };
    },
  },
  /**
   * 上传文件参数名称
   */
  name: {
    type: String,
    default: "file",
  },
  /**
   * 上传地址
   */
  action: {
    type: String,
    default: FileAPI.uploadUrl,
  },
  /**
   * 上传文件类型
   */
  accept: {
    type: String,
    default: "*",
  },
});

const fileList = ref([] as FileInfo[]);
const showUploadPercent = ref(false);
const uploadPercent = ref(0);

watch(
  () => props.guid,
  (newVal) => {
    bindFileList(newVal, props.code);
  },
  { immediate: true }
);

//给附件列表绑定数据
function bindFileList(guid: string, code: string) {
  FileAPI.getFileList(guid, code).then((data) => {
    console.log(data);
    if (data.length > 0) {
      fileList.value = data;
    } else {
      ElMessage.error("获取附件列表失败");
    }
  });
}

/**
 * 限制用户上传文件的大小
 */
function handleBeforeUpload(file: UploadRawFile) {
  // if (file.size > props.maxSize) {
  //   ElMessage.warning("上传文件不能大于" + props.maxSize + "M");
  //   return false;
  // }
  uploadPercent.value = 0;
  showUploadPercent.value = true;
  return true;
}

const handleSuccessFile = (response: any, file: UploadFile) => {
  showUploadPercent.value = false;
  uploadPercent.value = 0;
  if (response.code === ResultEnum.SUCCESS) {
    ElMessage.success("上传成功");
    //重新绑定列表
    bindFileList(props.guid, props.code);
  } else {
    ElMessage.error(response.msg || "上传失败");
  }
};

const handleError = (error: any) => {
  showUploadPercent.value = false;
  uploadPercent.value = 0;
  ElMessage.error("上传失败");
};

const customColorMethod = (percentage: number) => {
  if (percentage < 30) {
    return "#909399";
  }
  if (percentage < 70) {
    return "#375ee8";
  }
  return "#67c23a";
};

const handleProgress = (event: UploadProgressEvent) => {
  uploadPercent.value = event.percent;
};

const CheckIsRequired = () => {
  let isValid = true;
  for (const file of fileList.value) {
    if (file.isRequired && !file.uid) {
      ElMessage.error("请上传" + file.fjname + "附件");
      isValid = false;
      break;
    }
  }
  return isValid;
};
/**
 * 删除文件
 */
function handleRemove(removeFile: UploadUserFile) {
  const filePath = removeFile.url;
  if (filePath) {
    // 确认是否删除
    ElMessageBox.confirm("确定要删除该文件吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        FileAPI.deleteByPath(removeFile.uid, filePath).then(() => {
          //重新绑定列表
          bindFileList(props.guid, props.code);
          ElMessage.success("删除成功");
        });
        // 确认删除
      })
      .catch(() => {
        // 取消删除
      });
  }
}

/**
 * 下载文件
 */
function downloadFile(file: UploadUserFile) {
  const filePath = file.url;
  if (filePath) {
    FileAPI.downloadFile(filePath, file.name);
  }
}

// 添加新的工具函数
const formatFileSize = (size?: number) => {
  if (!size) return "未知";
  const units = ["B", "KB", "MB", "GB"];
  let index = 0;
  let fileSize = size;

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }

  return `${fileSize.toFixed(2)} ${units[index]}`;
};

const formatDate = (timestamp?: number) => {
  if (!timestamp) return "未知";
  const date = new Date(timestamp);
  return date.toLocaleString();
};

defineExpose({
  CheckIsRequired,
});
</script>

<style lang="scss" scoped>
.file-upload-container {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 10px;
  }

  .header-upload {
    :deep(.el-upload) {
      display: inline-block;
    }
  }

  .file-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .file-name {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .operation-cell {
    display: flex;
    justify-content: center;
    gap: 12px;
  }

  .table-wrapper {
    position: relative;
    width: 100%;
  }

  .upload-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(2px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .upload-progress-content {
    text-align: center;
    padding: 20px;
    border-radius: 8px;
    background: white;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .upload-icon {
      font-size: 48px;
      color: var(--el-color-primary);
      margin-bottom: 16px;
      animation: bounce 1s infinite;
    }

    .progress-info {
      width: 300px;

      .progress-text {
        margin-bottom: 12px;
        color: var(--el-text-color-primary);
        font-size: 14px;
      }

      :deep(.el-progress-bar) {
        .el-progress-bar__outer {
          border-radius: 4px;
        }
      }
    }
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}
</style>
