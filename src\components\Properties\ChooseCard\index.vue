<!-- 调拨单 -->
<template>
  <el-card>
    <!-- ********************** 用插槽显示筛选框 ********************** -->
    <template #header>
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
        <el-form-item label="资产编号" prop="zcbh">
          <el-input v-model="queryParams.zcbh" placeholder="请输入资产编号" />
        </el-form-item>
        <el-form-item label="资产名称" prop="zcmc">
          <el-input v-model="queryParams.zcmc" placeholder="请输入资产名称" />
        </el-form-item>
        <el-form-item label="使用人" prop="syr">
          <el-input
            v-model="queryParams.syr"
            placeholder="请输入使用人"
          />
        </el-form-item>
        <el-form-item label="使用部门" prop="sybm">
          <DDLDeptList v-model="queryParams.sybm" disabled />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon><Search /></template>
            搜索
          </el-button>
        </el-form-item>
      </el-form>
    </template>

    <Title name="资产清单">
      <el-button
        plain
        type="primary"
        icon="Pointer"
        :disabled="seletctIds.length === 0"
        @click="handleConfirm"
      >
        批量选择
      </el-button>
    </Title>
    <!-- ********************** 列表内容 ********************** -->
    <el-table
      v-loading="loading"
      stripe
      :data="pageData"
      highlight-current-row
      :border="true"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" align="center" fixed />
      <el-table-column
        type="selection"
        width="50"
        align="center"
        fixed
        :selectable="(row: any) => row.locName == ''"
      />
      <el-table-column
        type="index"
        label="序号"
        width="55"
        align="center"
        :index="handleIndex"
        fixed
      />
      <el-table-column label="资产编号" prop="zcbh" width="120" />
      <el-table-column label="资产名称" prop="zcmc" width="200" />
      <el-table-column label="金额(元)" prop="je" width="100" />
      <el-table-column label="数量" prop="sl" min-width="80" />
      <el-table-column label="资产状态" prop="locName" width="150">
        <template #default="scope">
          <span>{{ scope.row.locName === "" ? "正常" : scope.row.locName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="使用人" prop="syrname" width="150" />
      <el-table-column label="使用部门" prop="sybmname" width="200" />
      <!-- <el-table-column label="存放地点" prop="cfdd" width="150" /> -->
      <!-- ********************** 操作列 **********************
         按钮：查询View；编辑Edit；删除Delete -->
      <el-table-column label="操作" fixed="right" width="150">
        <template #default="scope">
          <el-button
            v-hasPerm="'sys:user:password:reset'"
            type="primary"
            icon="View"
            size="small"
            link
            @click="hancleRowView(scope.row.guid)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- ********************** 翻页 ********************** -->
    <pagination
      v-if="total > 0"
      v-model:total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="handleQuery"
    />
  </el-card>

  <!-- 查看资产弹窗 -->
  <el-drawer v-model="itemVisible" append-to-body size="70%">
    <CardPanel :key="itemGuid" :guid="itemGuid" />
  </el-drawer>
</template>

<script setup lang="ts">
import BookApi, { bookChoosePageVO, bookPageQuery, bookPageVO } from "@/api/properties/book";
function handleIndex(index: number) {
  return (queryParams.pageNum - 1) * queryParams.pageSize + (index + 1);
}
//查询类型，新增【add】，编辑【edit】，查询【view】
const props = defineProps({
  handleReturnConfirm: {
    type: Function,
    required: true,
  },
  dcode: {
    type: String,
  },
  syr: {
    type: String,
  },
});

const loading = ref(false);
const seletctIds = ref<number[]>([]);

const total = ref(0);
// 调拨表格数据
const pageData = ref<bookChoosePageVO[]>([]);
// 选中项发生变化
function handleSelectionChange(selection: any[]) {
  seletctIds.value = selection.map((item) => item.guid);
  console.log(seletctIds.value);
}

//弹窗相关参数
const itemVisible = ref(false);
const itemGuid = ref("");
const hancleRowView = (guid: string) => {
  itemGuid.value = guid;
  itemVisible.value = true;
};

const queryParams = reactive<bookPageQuery>({
  pageNum: 1,
  pageSize: 10,
  sybm: "",
  syr: "",
  zcbh: "",
  zcmc: "",
});
const handleQuery = () => {
  console.log("props.dcode", props.dcode);
  loading.value = true;
  console.log(queryParams);
  BookApi.getCommonBookPage(queryParams)
    .then((res) => {
      pageData.value = res.list;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
};

const handleConfirm = () => {
  props.handleReturnConfirm(seletctIds.value);
};

onMounted(() => {
  queryParams.syr = props.syr;
  queryParams.sybm = props.dcode;
  handleQuery();
});

watch(
  () => props.dcode,
  (newValue) => {
    queryParams.sybm = newValue;
    handleQuery();
  }
);
</script>
<style lang="scss" scoped></style>
