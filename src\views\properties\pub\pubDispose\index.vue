<template>
  <div class="app-container">
    <el-card>
      <!-- ********************** 用插槽显示筛选框 ********************** -->
      <template #header>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
          <el-form-item label="处置方式" prop="czlx">
            <DDLYcode v-model="queryParams.czlx" ycode="0102" />
          </el-form-item>
          <el-form-item label="处置单号" prop="czbh">
            <el-input
              v-model="queryParams.czbh"
              placeholder="处置单号"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="申请日期" prop="djsj">
            <el-date-picker
              v-model="queryParams.djsj"
              :editable="false"
              class="filter-item"
              type="daterange"
              range-separator="~"
              start-placeholder="开始时间"
              end-placeholder="截止时间"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>
          <el-form-item label="申请部门" prop="djbm">
            <DDLDeptList v-model="queryParams.djbm" clearable />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon><Search /></template>
              搜索
            </el-button>
            <!-- <el-button @click="handleResetQuery">
              <template #icon><Refresh /></template>
              重置
            </el-button> -->
          </el-form-item>
        </el-form>
      </template>
      <!-- ********************** 页面批量操作按钮 ********************** -->
      <Title name="处置公示列表" />
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        stripe
        border
      >
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="处置单号" prop="czbh" width="150" align="center" fixed />
        <el-table-column label="处置方式" prop="czlxname" width="150" align="center" fixed />
        <el-table-column label="处置金额(元)" prop="by2" width="150" align="center" />
        <el-table-column label="申请部门" prop="djbmname" width="200" align="center" />
        <el-table-column label="申请人" prop="djrname" width="150" align="center" />
        <el-table-column label="申请时间" prop="djsj" width="200" align="center" />
        <el-table-column label="公示剩余时间" prop="by5" width="150" align="center" />

        <el-table-column label="操作" fixed="right" width="100">
          <template #default="scope">
            <el-button
              type="success"
              icon="Document"
              size="small"
              @click="handleOpenDialog(false, scope.row)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :page-size="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 处置表单弹窗 -->
    <el-drawer v-model="dialog.visible" :title="dialog.title" append-to-body size="75%">
      <el-card>
        <DisposeView
          :key="itemGuid + '1'"
          :guid="itemGuid"
          :editable="itemEditable"
          :netcode="itemNetcode"
          :djbm="itemDjbm"
          :RefreshFatherDrawer="handleCloseDialog"
        />
      </el-card>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "dispose",
  inheritAttrs: false,
});

import { useRouter } from "vue-router";
import pubAPI, { pubPubDisposeQuery } from "@/api/properties/pub";

/** 关闭调拨弹窗 */
function handleCloseDialog(close?: boolean) {
  if (close) {
    dialog.visible = false;
  }
  handleQuery();
}

//页面参数
const queryFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);

//请求列表数据
const pageData = ref<any[]>([]);
const queryParams = reactive<pubPubDisposeQuery>({
  pageNum: 1,
  pageSize: 10,
});
const handleQuery = () => {
  loading.value = true;
  console.log(queryParams);
  pubAPI
    .getPubDispose(queryParams)
    .then((res) => {
      pageData.value = res.list;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
};

const dialog = reactive({
  title: "",
  visible: false,
});
const itemGuid = ref<string>("");
const itemId = ref<number | undefined>();
const itemEditable = ref<boolean>(false);
const itemDjbm = ref<string>("");
const itemNetcode = ref<string>("");
function handleOpenDialog(editable: boolean, row?: any) {
  //打开弹窗，如果没有guid则生成一个GUID
  itemGuid.value = row?.guid;
  itemId.value = row?.id;
  itemEditable.value = editable;
  itemDjbm.value = row?.djbm;
  itemNetcode.value = row?.netcode || "";
  dialog.visible = true;
}

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.el-form {
  width: auto;
}
</style>
