import request from "@/utils/request";

const ZXCGCGJYS_BASE_URL = "/api/v1/zxcgCgjyss";

const ZxcgCgjysAPI = {
    /** 获取采购建议书书分页数据 */
    getPage(queryParams?: ZxcgCgjysPageQuery) {
        return request<any, PageResult<ZxcgCgjysPageVO[]>>({
            url: `${ZXCGCGJYS_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    },
    /**
     * 获取采购建议书书表单数据
     *
     * @param id ZxcgCgjysID
     * @returns ZxcgCgjys表单数据
     */
    getFormData(id: number) {
        return request<any, ZxcgCgjysForm>({
            url: `${ZXCGCGJYS_BASE_URL}/${id}/form`,
            method: "get",
        });
    },

    /** 添加采购建议书书*/
    add(data: ZxcgCgjysForm) {
        return request({
            url: `${ZXCGCGJYS_BASE_URL}`,
            method: "post",
            data: data,
        });
    },

    /**
     * 更新采购建议书书
     *
     * @param id ZxcgCgjysID
     * @param data ZxcgCgjys表单数据
     */
     update(id: number, data: ZxcgCgjysForm) {
        return request({
            url: `${ZXCGCGJYS_BASE_URL}/${id}`,
            method: "put",
            data: data,
        });
    },

    /**
     * 批量删除采购建议书书，多个以英文逗号(,)分割
     *
     * @param ids 采购建议书书ID字符串，多个以英文逗号(,)分割
     */
     deleteByIds(ids: string) {
        return request({
            url: `${ZXCGCGJYS_BASE_URL}/${ids}`,
            method: "delete",
        });
    }
}

export default ZxcgCgjysAPI;

/** 采购建议书书分页查询参数 */
export interface ZxcgCgjysPageQuery extends PageQuery {
    /** 任务ID */
    rwid?: number;
    /** 任务名称 */
    rwmc?: string;
    /** 确认书编号 */
    qrsbh?: string;
}

/** 采购建议书书表单对象 */
export interface ZxcgCgjysForm {
    /** 建议书表ID */
    jysid?:  number;
    /** 任务ID */
    rwid?:  number;
    /** 申请ID */
    sqid?:  number;
    /** 任务名称 */
    rwmc?:  string;
    /** 任务描述 */
    rwms?:  string;
    /** 采购形式 */
    cgxs?:  string;
    /** 采购方式 */
    cgfs?:  string;
    /** 采购审批人 */
    spr?:  string;
    /** 审批人姓名 */
    sprname?:  string;
    /** 登记人 */
    djr?:  string;
    /** 登记时间 */
    djsj?:  Date;
    /** 备注 */
    notes?:  string;
    /** Guid */
    guid?:  string;
    /** Netcode */
    netcode?:  string;
    /** Sjzt */
    sjzt?:  string;
    /** 下一审批人|hidden */
    nexter?:  string;
    /** 下一审批部门|hidden */
    nextbm?:  string;
    /** 执行人|hidden */
    zxr?:  string;
    /** 委托ID */
    wtid?:  number;
    /** 委托类型 */
    wtlx?:  string;
    /** 委托单位 */
    wtdw?:  string;
    /** 确认书编号 */
    qrsbh?:  string;
    /** 确认时间 */
    qrsj?:  Date;
    /** 是否选择供应商比价 0:未选择供应商比价 1:选择供应商比价 */
    isselgys?:  number;
    /** 申请ID集 */
    sqids?:  string;
    /** Zxbm */
    zxbm?:  string;
    /** 委托人是否解密 */
    iswtrdecrypt?:  number;
    /** 委托人工号 */
    wtr?:  string;
    /** 登记人是否解密 */
    isdjrdecrypt?:  number;
    /** 比价结束时间 */
    cppriceendtime?:  Date;
    /** 比价是否成交 1:成交 0:未成交 */
    isclinch?:  number;
    /** 需求确认状态  0待受理   1待确认  2已确认 3已完成 4完成后提交委托 */
    xqqrzt?:  number;
    /** 采购内容 */
    cgnr?:  string;
    /** 技术参数 */
    jscs?:  string;
    /** 计量单位 */
    jldw?:  string;
    /** 资金类型 */
    zjlx?:  string;
    /** 付款方式 */
    fkfs?:  string;
    /** 浙江省财建议书上报结果 0失败 1成功 */
    sbjg?:  string;
    /** 上报结果说明 */
    sbjgsm?:  string;
    /** 备用1（判断新获取的确认书号申请受理人 是否已阅，0未阅，1已阅） */
    by1?:  number;
    /** 备用2（判断任务审核人是否已阅，0未阅，1已阅） */
    by2?:  number;
    /** 备用3（判断新获取的确认书号是否已阅，0未阅，1已阅） */
    by3?:  number;
    /** 任务的数量（受理人可以修改） */
    by4?:  string;
    /** 备用5 （判断自行验收步骤是否结束 0未结束，1已结束） */
    by5?:  string;
    /** 执行标记  A、(11)分散国内/单一10w+    B、(12)分散自行进口  C、(21) 零星/单一10w-   (22)询价  (23)网上竞价   D 、定点(31)电子卖场(32)  E 、(4)政府/集中 */
    zxbj?:  number;
    /** 判断是否为校内确认书 1是 0否 */
    xnqrs?:  number;
    /** 确认书接收人 */
    qrsjsr?:  string;
    /** 执行接收人 */
    zxjsr?:  string;
    /** 委托代理机构 */
    wtdljg?:  string;
    /** 委托代理时间 */
    wtdlsj?:  Date;
    /** 开标时间 */
    kbsj?:  Date;
    /** 开标详细信息 */
    kbxx?:  string;
    /** 中标单位 */
    zbdw?:  string;
    /** 中标金额 */
    zbje?:  number;
    /** 电子卖场-下单时间 */
    dzmcXdsj?:  Date;
    /** 电子卖场-详细信息 */
    dzmcXx?:  string;
    /** 电子卖场-下单金额 */
    dzmcJe?:  number;
    /** 电子卖场-供应商 */
    dzmcGys?:  string;
    /** 备案方式 */
    bafs?:  string;
    /** 建议书号（数据标识，15位字符串，4位年+6位单位代码+5位顺序码） */
    bid?:  string;
    /** 1待办、2提交成功未确认、4提交失败、50、冻结中，无法操作、5、已确认待审核、6已退回已撤销、7已退回未撤销、8已确认、9支付完成 */
    rwzt?:  number;
    /** 预算金额 */
    ysje?:  number;
    /** 采购目录 */
    cgml?:  string;
    /** 是否委托机构受理，1：受理，其他：未受理 */
    iswtsl?:  string;
    /** 是否50万及以下校内定点货物、省级定点服务，1是，0否 */
    sfdd?:  string;
    /** 工程派工单位 */
    gcpgdw?:  string;
    /** 预计采购时间 */
    yjcgsj?:  Date;
    /** 工程类申请确认人 */
    sqrsh?:  string;
    /** 工程类结算金额由申请人填写 */
    jsje?:  number;
    /** 0校采通，1政采云 */
    zxfs?:  string;
    /** 工程任务派单员 */
    gcrwpd?:  string;
    /** 1未推送校采云，2已推送校采云 */
    autopushxcy?:  string;
    /** 外贸委托表id */
    wmwtid?:  number;
    /** 外贸委托单位 */
    wmwtdw?:  string;
    /** 是否从金财系统中下载确认书附件(1已经获取，0未获取) */
    by6?:  string;
    /** 原rwguid */
    by7?:  string;
    /** 经费负责人审核 */
    by8?:  string;
    /** 部门负责人审核 */
    by9?:  string;
    zxqytype?:  string;
    zxqyratio?:  number;
    detailid?:  string;
    budgetid?:  string;
    /** 预算金额 */
    rwsl?:  number;
    fltk?:  string;
    yxcd?:  string;
    babz?:  string;
    sfbm?:  string;
    zxqyflag?:  string;
    zxqyly?:  string;
    zbgcxm?:  string;
}

/** 采购建议书书分页对象 */
export interface ZxcgCgjysPageVO {
    /** 建议书表ID */
    jysid?: number;
    /** 任务ID */
    rwid?: number;
    /** 申请ID */
    sqid?: number;
    /** 任务名称 */
    rwmc?: string;
    /** 任务描述 */
    rwms?: string;
    /** 采购形式 */
    cgxs?: string;
    /** 采购方式 */
    cgfs?: string;
    /** 采购审批人 */
    spr?: string;
    /** 审批人姓名 */
    sprname?: string;
    /** 登记人 */
    djr?: string;
    /** 登记时间 */
    djsj?: Date;
    /** 备注 */
    notes?: string;
    /** Guid */
    guid?: string;
    /** Netcode */
    netcode?: string;
    /** Sjzt */
    sjzt?: string;
    /** 下一审批人|hidden */
    nexter?: string;
    /** 下一审批部门|hidden */
    nextbm?: string;
    /** 执行人|hidden */
    zxr?: string;
    /** 委托ID */
    wtid?: number;
    /** 委托类型 */
    wtlx?: string;
    /** 委托单位 */
    wtdw?: string;
    /** 确认书编号 */
    qrsbh?: string;
    /** 确认时间 */
    qrsj?: Date;
    /** 是否选择供应商比价 0:未选择供应商比价 1:选择供应商比价 */
    isselgys?: number;
    /** 申请ID集 */
    sqids?: string;
    /** Zxbm */
    zxbm?: string;
    /** 委托人是否解密 */
    iswtrdecrypt?: number;
    /** 委托人工号 */
    wtr?: string;
    /** 登记人是否解密 */
    isdjrdecrypt?: number;
    /** 比价结束时间 */
    cppriceendtime?: Date;
    /** 比价是否成交 1:成交 0:未成交 */
    isclinch?: number;
    /** 需求确认状态  0待受理   1待确认  2已确认 3已完成 4完成后提交委托 */
    xqqrzt?: number;
    /** 采购内容 */
    cgnr?: string;
    /** 技术参数 */
    jscs?: string;
    /** 计量单位 */
    jldw?: string;
    /** 资金类型 */
    zjlx?: string;
    /** 付款方式 */
    fkfs?: string;
    /** 浙江省财建议书上报结果 0失败 1成功 */
    sbjg?: string;
    /** 上报结果说明 */
    sbjgsm?: string;
    /** 备用1（判断新获取的确认书号申请受理人 是否已阅，0未阅，1已阅） */
    by1?: number;
    /** 备用2（判断任务审核人是否已阅，0未阅，1已阅） */
    by2?: number;
    /** 备用3（判断新获取的确认书号是否已阅，0未阅，1已阅） */
    by3?: number;
    /** 任务的数量（受理人可以修改） */
    by4?: string;
    /** 备用5 （判断自行验收步骤是否结束 0未结束，1已结束） */
    by5?: string;
    /** 执行标记  A、(11)分散国内/单一10w+    B、(12)分散自行进口  C、(21) 零星/单一10w-   (22)询价  (23)网上竞价   D 、定点(31)电子卖场(32)  E 、(4)政府/集中 */
    zxbj?: number;
    /** 判断是否为校内确认书 1是 0否 */
    xnqrs?: number;
    /** 确认书接收人 */
    qrsjsr?: string;
    /** 执行接收人 */
    zxjsr?: string;
    /** 委托代理机构 */
    wtdljg?: string;
    /** 委托代理时间 */
    wtdlsj?: Date;
    /** 开标时间 */
    kbsj?: Date;
    /** 开标详细信息 */
    kbxx?: string;
    /** 中标单位 */
    zbdw?: string;
    /** 中标金额 */
    zbje?: number;
    /** 电子卖场-下单时间 */
    dzmcXdsj?: Date;
    /** 电子卖场-详细信息 */
    dzmcXx?: string;
    /** 电子卖场-下单金额 */
    dzmcJe?: number;
    /** 电子卖场-供应商 */
    dzmcGys?: string;
    /** 备案方式 */
    bafs?: string;
    /** 建议书号（数据标识，15位字符串，4位年+6位单位代码+5位顺序码） */
    bid?: string;
    /** 1待办、2提交成功未确认、4提交失败、50、冻结中，无法操作、5、已确认待审核、6已退回已撤销、7已退回未撤销、8已确认、9支付完成 */
    rwzt?: number;
    /** 预算金额 */
    ysje?: number;
    /** 采购目录 */
    cgml?: string;
    /** 是否委托机构受理，1：受理，其他：未受理 */
    iswtsl?: string;
    /** 是否50万及以下校内定点货物、省级定点服务，1是，0否 */
    sfdd?: string;
    /** 工程派工单位 */
    gcpgdw?: string;
    /** 预计采购时间 */
    yjcgsj?: Date;
    /** 工程类申请确认人 */
    sqrsh?: string;
    /** 工程类结算金额由申请人填写 */
    jsje?: number;
    /** 0校采通，1政采云 */
    zxfs?: string;
    /** 工程任务派单员 */
    gcrwpd?: string;
    /** 1未推送校采云，2已推送校采云 */
    autopushxcy?: string;
    /** 外贸委托表id */
    wmwtid?: number;
    /** 外贸委托单位 */
    wmwtdw?: string;
    /** 是否从金财系统中下载确认书附件(1已经获取，0未获取) */
    by6?: string;
    /** 原rwguid */
    by7?: string;
    /** 经费负责人审核 */
    by8?: string;
    /** 部门负责人审核 */
    by9?: string;
    zxqytype?: string;
    zxqyratio?: number;
    detailid?: string;
    budgetid?: string;
    /** 预算金额 */
    rwsl?: number;
    fltk?: string;
    yxcd?: string;
    babz?: string;
    sfbm?: string;
    zxqyflag?: string;
    zxqyly?: string;
    zbgcxm?: string;
}
