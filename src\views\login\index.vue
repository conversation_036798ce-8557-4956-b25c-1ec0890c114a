<template>
  <div class="login">
    <!-- 登录页头部 
    <div class="login-header">
      <div class="flex-y-center">
        <el-switch
          v-model="isDark"
          inline-prompt
          active-icon="Moon"
          inactive-icon="Sunny"
          @change="toggleTheme"
        />
        <lang-select class="ml-2 cursor-pointer" />
      </div>
    </div>-->

    <!-- 登录页内容 -->
    <div class="login-form">
      <el-form ref="loginFormRef" :model="loginData" :rules="loginRules">
        <div class="form-title" v-if="isShowUserLoginTest">
          <h2>{{ defaultSettings.title }}</h2>
          <el-button type="text" class="test-account-btn" @click="testAccountDialogVisible = true">
            <el-tooltip effect="dark" content="测试账号" placement="top">
              <el-icon class="text-[var(--el-text-color-primary)]">
                <arrow-down />
              </el-icon>
            </el-tooltip>
          </el-button>
        </div>

        <!-- 测试账号对话框 -->
        <el-dialog
          v-model="testAccountDialogVisible"
          title="选择账号"
          width="800px"
          destroy-on-close
          append-to-body
          :close-on-click-modal="false"
        >
          <div class="test-account-dialog">
            <div class="search-box">
              <el-input
                v-model="userQueryParams.keywords"
                placeholder="搜索用户"
                clearable
                @clear="handleQuery"
                @input="handleQuery"
                size="default"
                :prefix-icon="Search"
              />
              <el-tree-select
                v-model="userQueryParams.deptId"
                placeholder="选择部门"
                :data="deptOptions"
                :loading="queryLoading"
                filterable
                check-strictly
                clearable
                :render-after-expand="false"
                @change="handleQuery"
                style="width: 200px"
              />
              <el-select
                v-model="userQueryParams.roleIds"
                placeholder="选择角色"
                clearable
                style="width: 200px"
                multiple
                @change="handleQuery"
              >
                <el-option
                  v-for="item in roleOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-button type="primary" @click="handleQuery" size="default">搜索</el-button>
            </div>
            <el-scrollbar height="300px">
              <div v-loading="queryLoading" class="account-list">
                <el-empty v-if="!pageData?.length" :image-size="60" description="暂无数据" />
                <div
                  v-for="user in pageData"
                  :key="user.username"
                  class="account-item"
                  @click="selectTestAccount(user)"
                >
                  <div class="account-info">
                    <div class="nickname">{{ user.nickname }}</div>
                    <div class="username">{{ user.username }}</div>
                  </div>
                  <el-tag size="small" type="info" class="dept-tag">
                    {{ user.deptName }}
                  </el-tag>
                  <el-tag size="small" type="success" class="role-tag">
                    {{ user.roleNames }}
                  </el-tag>
                </div>
              </div>
            </el-scrollbar>
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="userQueryParams.pageNum"
                v-model:page-size="userQueryParams.pageSize"
                :page-sizes="[10, 20, 30, 50]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </el-dialog>

        <!-- 用户名 -->
        <el-form-item prop="username">
          <div class="input-wrapper">
            <el-icon class="mx-2">
              <User />
            </el-icon>
            <el-input
              ref="username"
              v-model="loginData.username"
              :placeholder="$t('login.username')"
              name="username"
              size="large"
              class="h-[48px]"
            />
          </div>
        </el-form-item>

        <!-- 密码 -->
        <el-tooltip :visible="isCapslock" :content="$t('login.capsLock')" placement="right">
          <el-form-item prop="password">
            <div class="input-wrapper">
              <el-icon class="mx-2">
                <Lock />
              </el-icon>
              <el-input
                v-model="loginData.password"
                :placeholder="$t('login.password')"
                type="password"
                name="password"
                size="large"
                class="h-[48px] pr-2"
                show-password
                @keyup="checkCapslock"
                @keyup.enter="handleLoginSubmit"
              />
            </div>
          </el-form-item>
        </el-tooltip>

        <!-- 验证码 -->
        <el-form-item prop="captchaCode">
          <div class="input-wrapper">
            <svg-icon icon-class="captcha" class="mx-2" />
            <el-input
              v-model="loginData.captchaCode"
              auto-complete="off"
              size="large"
              class="flex-1"
              :placeholder="$t('login.captchaCode')"
              @keyup.enter="handleLoginSubmit"
            />

            <el-image :src="captchaBase64" class="captcha-img" @click="getCaptcha" />
          </div>
        </el-form-item>

        <div class="flex-x-between w-full py-1">
          <el-checkbox>
            {{ $t("login.rememberMe") }}
          </el-checkbox>

          <el-link type="primary" href="/forget-password">
            {{ $t("login.forgetPassword") }}
          </el-link>
        </div>

        <!-- 登录按钮 -->
        <el-button
          :loading="loading"
          type="primary"
          size="large"
          class="w-full"
          @click.prevent="handleLoginSubmit"
        >
          {{ $t("login.login") }}
        </el-button>

        <!-- 第三方登录 
        <el-divider>
          <el-text size="small">{{ $t("login.otherLoginMethods") }}</el-text>
        </el-divider>
        <div class="third-party-login">
          <svg-icon icon-class="wechat" class="icon" />
          <svg-icon icon-class="qq" class="icon" />
          <svg-icon icon-class="github" class="icon" />
          <svg-icon icon-class="gitee" class="icon" />
        </div>-->
      </el-form>
    </div>

    <!-- 登录页底部 -->
    <div class="login-footer">
      <el-text size="small">
        版权所有：XXXXX | 地址：XXXXXX | 技术支持：浙江工越信息科技有限公司
      </el-text>
    </div>
  </div>
</template>

<script setup lang="ts">
import { LocationQuery, useRoute } from "vue-router";
import { useI18n } from "vue-i18n";

import AuthAPI, { type LoginData } from "@/api/auth";
import router from "@/router";

import type { FormInstance } from "element-plus";
import { Search, UserFilled } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

import defaultSettings from "@/settings";
import { ThemeEnum } from "@/enums/ThemeEnum";

import { useSettingsStore, useUserStore, useDictStore } from "@/store";
import UserAPI, { UserPageQuery, UserPageVO } from "@/api/system/user";
import DeptAPI from "@/api/system/dept";
import RoleAPI from "@/api/system/role";

const userStore = useUserStore();
const settingsStore = useSettingsStore();
const dictStore = useDictStore();

const route = useRoute();
const { t } = useI18n();
const loginFormRef = ref<FormInstance>();

const isDark = ref(settingsStore.theme === ThemeEnum.DARK); // 是否暗黑模式
const loading = ref(false); // 按钮 loading 状态
const queryLoading = ref(false);
const isCapslock = ref(false); // 是否大写锁定
const captchaBase64 = ref(); // 验证码图片Base64字符串

const logo = ref(new URL("../../assets/logo.png", import.meta.url).href);
const loginImage = ref(new URL("../../assets/images/login-image.svg", import.meta.url).href);

const isShowUserLoginTest = ref(false);
const testAccountDialogVisible = ref(false);

const loginData = ref<LoginData>({
  username: "**********",
  password: "123456",
  captchaKey: "",
  captchaCode: "",
});

const userQueryParams = reactive<UserPageQuery>({
  pageNum: 1,
  pageSize: 10,
  deptId: undefined,
  keywords: "",
});

// 部门数据
const deptOptions = ref<OptionType[]>([]);
// 角色数据
const roleOptions = ref<OptionType[]>([]);

// 获取部门列表
const getDeptOptions = async () => {
  try {
    const data = await DeptAPI.getOptions();
    deptOptions.value = data;
  } catch (error) {
    console.error("获取部门列表失败:", error);
  }
};

// 获取角色列表
const getRoleOptions = async () => {
  try {
    const data = await RoleAPI.getOptions();
    roleOptions.value = data;
  } catch (error) {
    console.error("获取角色列表失败:", error);
  }
};

const pageData = ref<UserPageVO[]>();
const total = ref(0);

const loginRules = computed(() => {
  return {
    username: [
      {
        required: true,
        trigger: "blur",
        message: t("login.message.username.required"),
      },
    ],
    password: [
      {
        required: true,
        trigger: "blur",
        message: t("login.message.password.required"),
      },
      {
        min: 6,
        message: t("login.message.password.min"),
        trigger: "blur",
      },
    ],
    captchaCode: [
      {
        required: true,
        trigger: "blur",
        message: t("login.message.captchaCode.required"),
      },
    ],
  };
});

// 获取验证码
function getCaptcha() {
  AuthAPI.getCaptcha().then((data) => {
    loginData.value.captchaKey = data.captchaKey;
    captchaBase64.value = data.captchaBase64;
  });
}

// 登录
async function handleLoginSubmit() {
  loginFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      userStore
        .login(loginData.value)
        .then(async () => {
          await userStore.getUserInfo();
          // 需要在路由跳转前加载字典数据，否则会出现字典数据未加载完成导致页面渲染异常
          await dictStore.loadDictionaries();
          // 跳转到登录前的页面
          const { path, queryParams } = parseRedirect();
          router.push({ path: path, query: queryParams });
        })
        .catch(() => {
          getCaptcha();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

/**
 * 解析 redirect 字符串 为 path 和  queryParams
 *
 * @returns { path: string, queryParams: Record<string, string> } 解析后的 path 和 queryParams
 */
function parseRedirect(): {
  path: string;
  queryParams: Record<string, string>;
} {
  const query: LocationQuery = route.query;
  const redirect = (query.redirect as string) ?? "/";

  const url = new URL(redirect, window.location.origin);
  const path = url.pathname;
  const queryParams: Record<string, string> = {};

  url.searchParams.forEach((value, key) => {
    queryParams[key] = value;
  });

  return { path, queryParams };
}

// 主题切换
const toggleTheme = () => {
  const newTheme = settingsStore.theme === ThemeEnum.DARK ? ThemeEnum.LIGHT : ThemeEnum.DARK;
  settingsStore.changeTheme(newTheme);
};

// 检查输入大小写
function checkCapslock(event: KeyboardEvent) {
  // 防止浏览器密码自动填充时报错
  if (event instanceof KeyboardEvent) {
    isCapslock.value = event.getModifierState("CapsLock");
  }
}

// 设置登录凭证
const setLoginCredentials = (username: string, password: string) => {
  loginData.value.username = username;
  loginData.value.password = password;
};

// 选择测试账号
const selectTestAccount = (user: UserPageVO) => {
  setLoginCredentials(user.username || "", "123456");
  testAccountDialogVisible.value = false;
  ElMessage.success(`已选择账号: ${user.nickname}`);
};

onMounted(() => {
  if (import.meta.env.VITE_APP_QUICK_LOGIN) {
    isShowUserLoginTest.value = true;
    getDeptOptions();
    getRoleOptions();
    handleQuery();
  }
  getCaptcha();
});

// 处理每页显示数量变化
const handleSizeChange = (val: number) => {
  userQueryParams.pageSize = val;
  handleQuery();
};

// 处理页码变化
const handleCurrentChange = (val: number) => {
  userQueryParams.pageNum = val;
  handleQuery();
};

const handleQuery = () => {
  queryLoading.value = true;
  UserAPI.getPageByUserLoginTest(userQueryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      queryLoading.value = false;
    });
};
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background: url("@/assets/images/back.png") no-repeat;
  background-size: cover;

  .test-account-btn {
    position: absolute;
    right: 10px;
    top: 10px;
  }
  .login-header {
    position: absolute;
    top: 0;
    display: flex;
    justify-content: right;
    width: 100%;
    padding: 15px;

    .logo {
      width: 26px;
      height: 26px;
    }

    .title {
      margin: auto 5px;
      font-size: 24px;
      font-weight: bold;
      color: #3b82f6;
    }
  }

  .login-form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 460px;
    padding: 40px;
    margin-left: 50%;
    overflow: hidden;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: var(--el-box-shadow-light);

    @media (width <= 460px) {
      width: 100%;
      padding: 0 20px;
    }

    .form-title {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 0 20px;
      text-align: center;
    }

    .input-wrapper {
      display: flex;
      align-items: center;
      width: 100%;
    }

    .captcha-img {
      height: 48px;
      cursor: pointer;
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
    }

    .third-party-login {
      display: flex;
      justify-content: center;
      width: 100%;
      color: var(--el-text-color-secondary);

      *:not(:first-child) {
        margin-left: 20px;
      }

      .icon {
        cursor: pointer;
      }
    }
  }

  .login-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    text-align: center;
  }
}

:deep(.el-form-item) {
  background: var(--el-input-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 5px;
}

:deep(.el-input) {
  .el-input__wrapper {
    padding: 0;
    background-color: transparent;
    box-shadow: none;

    &.is-focus,
    &:hover {
      box-shadow: none !important;
    }

    input:-webkit-autofill {
      /* 通过延时渲染背景色变相去除背景颜色 */
      transition: background-color 1000s ease-in-out 0s;
    }
  }
}

html.dark {
  .login {
    background: url("@/assets/images/login-bg-dark.jpg") no-repeat center right;

    .login-content {
      background: transparent;
      box-shadow: var(--el-box-shadow);
    }
  }
}

// 测试账号对话框样式
.test-account-dialog {
  .search-box {
    padding: 0 0 16px;
    display: flex;
    gap: 10px;
    align-items: center;
    border-bottom: 1px solid var(--el-border-color-lighter);

    .el-input {
      flex: 1;
    }
  }

  .account-list {
    min-height: 100px;
    padding: 10px 0;
  }

  .account-item {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px;
    width: 100%;
    transition: background-color 0.3s;
    border-radius: 4px;

    &:hover {
      background-color: var(--el-fill-color-light);
    }

    .account-info {
      width: 100%;
      overflow: hidden;

      .nickname {
        font-weight: 500;
        font-size: 14px;
        line-height: 1.2;
        color: var(--el-text-color-primary);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .username {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        margin-top: 4px;
      }
    }
  }

  .pagination-container {
    padding: 10px;
    text-align: right;
    background: white;
    border-top: 1px solid var(--el-border-color-lighter);
  }
}

:deep(.el-dialog) {
  border-radius: 8px;

  .el-dialog__header {
    margin: 0;
    padding: 16px 20px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .el-dialog__body {
    padding: 20px;
  }
}

// 辅助类
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
