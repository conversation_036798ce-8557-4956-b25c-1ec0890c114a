<script setup lang="ts">
import { ref, computed } from 'vue'
import { scenicList, type ScenicItem } from '../data' // 导入类型定义

const queryParams = ref({
  name: ''
})

const handleQuery = () => {
  // 搜索逻辑将在过滤后的列表实现
}

const filteredItems = computed(() => {
  return scenicList.filter((item: {
    id: number
    name: string
    image: string
    description: string
    price: number
    openingHours: string
  }) => 
    item.name.toLowerCase().includes(queryParams.value.name.toLowerCase())
  )
})
</script>

<template>
  <!-- 搜索表单 -->
  <el-form :model="queryParams" inline class="search-form">
    <el-form-item label="景区名称">
      <el-input
        v-model="queryParams.name"
        placeholder="请输入景区名称"
        clearable
        @keyup.enter="handleQuery"
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="handleQuery">搜索</el-button>
    </el-form-item>
  </el-form>

  <el-row :gutter="20">
    <el-col v-for="item in props.items" :key="item.id" :xs="24" :sm="12" :md="8" :lg="6">
      <el-card class="scenic-card" shadow="hover">
        <div class="image-container">
          <img 
            :src="item.image" 
            class="scenic-image"
            alt="景区图片"
          />
          <div class="rating-tag">4.8分</div>
        </div>
        <h3 class="title">{{ item.name }}</h3>
        <p class="description">{{ item.description }}</p>
        <div class="info-box">
          <div class="price">¥{{ item.price }}起</div>
          <div class="time">{{ item.openingHours }}</div>
        </div>
        <el-button type="primary" class="book-btn" round>立即预订</el-button>
      </el-card>
    </el-col>
  </el-row>
</template>

<style scoped>
.scenic-card {
  margin-bottom: 20px;
  transition: transform 0.3s;
  &:hover {
    transform: translateY(-5px);
  }
}
.image-container {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
}
.scenic-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s;
  &:hover {
    transform: scale(1.05);
  }
}
.rating-tag {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255,255,255,0.9);
  padding: 4px 8px;
  border-radius: 14px;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.title {
  margin: 12px 0 8px;
  font-size: 16px;
  color: #333;
}
.description {
  font-size: 13px;
  color: #666;
  line-height: 1.6;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.info-box {
  margin: 12px 0;
  .price {
    color: #ff4d4f;
    font-size: 16px;
    font-weight: 500;
  }
  .time {
    color: #999;
    font-size: 12px;
  }
}
.book-btn {
  width: 100%;
  margin-top: 8px;
}
</style>
