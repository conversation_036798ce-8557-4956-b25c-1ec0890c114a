<template>
  <div>
    <Title name="审核信息" />
    <approval-Form :netcode="props.netcode" :guid="props.guid" :sjmc="props.sjmc" />
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent } from "vue";

defineOptions({
  name: "WorkflowApproval",
});
const props = defineProps({
  //查询详情的调拨单ID和guid
  netcode: {
    type: String,
    required: true,
  },
  guid: {
    type: String,
    required: true,
  },
  sjmc: {
    type: String,
    default: "",
  },
});
// 异步加载组件
const ApprovalForm = defineAsyncComponent(() => import("./components/CheckForm.vue"));
</script>

<style lang="scss" scoped>
.workflow-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      font-size: 16px;
      font-weight: bold;
      color: var(--el-text-color-primary);
    }
  }
}
</style>
