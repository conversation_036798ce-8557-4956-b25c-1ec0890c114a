import request from "@/utils/request";

const Finance_BASE_URL = "/api/v1/finance";

const uploadfinAPI = {
  /** 获取变动记录分页数据 */
  getPage(queryParams?: uploadfinPageQuery) {
    return request<any, PageResult<uploadfinPageVO[]>>({
      url: `${Finance_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },
}

export default uploadfinAPI;

/** 验收单分页查询参数 */
export interface uploadfinPageQuery extends PageQuery {
  /** 资产名称或编号 */
  zcmc?: string;
  zcbh?: string;
  czcode?: string;
  rzzt?: string;
  /** 登记时间 */
  djsj?: string[];
}


/** 财政上传分页对象 */
export interface uploadfinPageVO {
  /** 资产编号 */
  zcbh?: string;
  zcmc?: string;
  guid?: string;
  zcgjztname?: string;
  czcodename?: string;
  pp?: string;
  ggxh?: string;
  djbm?: string;
  djbmname?: string;
  sl?: string;
  /** 金额 */
  je?: string;
  /** 单价 */
  dj?: string;
  rkguid?:string;
}


