<!-- 采购申请明细 -->
<template>
  <!--采购申请明细添加弹窗-->
  <el-form
    :model="mxForm"
    style="margin: 30px"
    ref="dataFormRef"
    :rules="mxrules"
    :disabled="!editable"
  >
    <el-form-item label="采购目录" prop="cgml">
      <!-- 弹出选择 -->
      <div class="flex">
        <el-input v-model="mxForm.cgmlname" :disabled="true" placeholder="请选择采购目录" />
        <el-button type="primary" @click="handleOpenCgmlDialog" v-if="editable">选择</el-button>
      </div>
    </el-form-item>
    <el-form-item
      :label="props.cglb === 'A' ? '物品名称' : props.cglb === 'B' ? '工程名称' : '服务名称'"
      prop="wpmc"
    >
      <el-input v-model="mxForm.wpmc" placeholder="请输入物品名称" />
    </el-form-item>
    <el-form-item v-if="props.cglb === 'A'" label="规格型号" prop="gg">
      <el-input v-model="mxForm.gg" placeholder="请输入规格型号" />
    </el-form-item>
    <el-form-item v-if="props.cglb === 'A'" label="数量" prop="sl">
      <el-input v-model="mxForm.sl" placeholder="请输入数量" />
    </el-form-item>
    <el-form-item v-if="props.cglb === 'A'" label="计量单位" prop="dw">
      <DDLXcode v-model="mxForm.dw" xcode="020210" />
    </el-form-item>
    <el-form-item v-if="props.cglb === 'A'" label="单价(元)" prop="price">
      <el-input v-model="mxForm.price" placeholder="请输入单价" />
    </el-form-item>
    <el-form-item label="金额(元)" prop="ysje">
      <el-input
        v-model="mxForm.ysje"
        placeholder="请输入预算金额"
        :disabled="props.cglb === 'A' ? true : false"
      />
    </el-form-item>
    <el-form-item label="备注" prop="notes">
      <el-input v-model="mxForm.notes" placeholder="请输入备注" />
    </el-form-item>
    <el-form-item v-if="props.cglb === 'A'" label="网上参考链接地址" prop="zcydz">
      <el-input v-model="mxForm.zcydz" placeholder="请输入网上参考链接地址" />
    </el-form-item>
  </el-form>
  <div style="text-align: right" v-if="editable">
    <el-button @click="props.RefreshFatherDrawer(true)">取消</el-button>
    <el-button type="primary" @click="handleAddMx">确定</el-button>
  </div>
  <!--采购申请明细添加弹窗-->
  <!--采购目录-->
  <el-dialog v-model="cgml_dialog.cgmlVisible" :title="cgml_dialog.title" width="60%">
    <TreeCgmlCode @node-click="handleCgmlcodeNodeClick" :Cglb="props.cglb" :key="props.cglb" />
  </el-dialog>
  <!--采购目录-->
</template>

<script setup lang="ts">
import ZxcgMxAPI, { ZxcgMxForm, ZxcgMxPageQuery, ZxcgMxPageVO } from "@/api/cg/zxcg-mx";
import { clearFormData } from "@/utils/page";
import { Cgmlcodes } from "@/api/cg/sys-cgml";
//————————————————————————————————————————————暴露的方法,和请求参数
//查询类型，新增【add】，编辑【edit】，查询【view】
const formType = reactive({
  add: false,
  edit: false,
  view: false,
});

//const emit = defineEmits(["handlecheck"]);
// 清理表单
const handleClearForm = () => {
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  mxForm.mxid = undefined;

  //formData.id = undefined;
  // formData.status = 1;
};

const mxForm = reactive<ZxcgMxForm>({});
const dataFormRef = ref(ElForm);
/** 初始化调拨信息 */
function handleFormQuery(id?: number) {
  if (id) {
    ZxcgMxAPI.getFormData(id)
      .then((data) => {
        Object.assign(mxForm, data);
      })
      .catch((error) => {
        console.error("获取采购申请明细数据失败:", error);
        ElMessage.error("获取采购申请明细数据失败");
      });
  } else {
    // 进去前给个新的GUID
    //新增
  }
}

const mxrules = reactive({
  cgml: [{ required: true, message: "采购目录不能为空", trigger: "blur" }],
  wpmc: [{ required: true, message: "物品名称不能为空", trigger: "blur" }],
  gg: [{ required: true, message: "规格型号不能为空", trigger: "blur" }],
  sl: [
    { required: true, message: "数量不能为空", trigger: "blur" },
    {
      pattern: /^[1-9]\d*$/,
      message: "必须为大于0的数字",
      trigger: "blur",
    },
  ], //数量
  dw: [{ required: true, message: "计量单位不能为空", trigger: "blur" }],
  price: [
    { required: true, message: "单价不能为空", trigger: "blur" },
    {
      pattern: /^(?=.*[1-9])\d*(?:\.\d{1,2})?$/,
      message: "必须为大于0的数字",
      trigger: "blur",
    },
  ],
  ysje: [
    { required: true, message: "预算金额不能为空", trigger: "blur" },
    {
      pattern: /^(?=.*[1-9])\d*(?:\.\d{1,2})?$/,
      message: "必须为大于0的数字",
      trigger: "blur",
    },
  ], //金额要大于0
});
//新增明细
const handleAddMx = async () => {
  dataFormRef.value.validate(async (valid: any) => {
    if (valid) {
      mxForm.sqid = props.sqid || 0;
      mxForm.parentguid = props.parentguid || "";
      const id = mxForm.mxid;
      mxForm.guid = props.guid || "";
      if (props.cglb === "B" || props.cglb === "C") {
        mxForm.sl = 1;
        mxForm.price = mxForm.ysje;
        mxForm.dw = "02021014";
      }
      const apiCall = id ? ZxcgMxAPI.update(id, mxForm) : ZxcgMxAPI.add(mxForm);

      await apiCall
        .then(() => {
          ElMessage.success("添加采购申请明细成功");
          handleClearForm();
          /* if (!id) {
            handleFormQuery(Number(props.id));
          }*/
          // 重新加载经费列表
          props.RefreshFatherDrawer(true);
        })
        .catch((error) => {
          console.error("添加采购申请明细失败:", error);
          ElMessage.error("添加采购申请明细失败");
        })
        .finally(() => {
          //.close();
        });
    }
  });
};

const props = defineProps({
  RefreshFatherDrawer: {
    type: Function,
    required: true,
  },
  id: {
    type: Number,
  },
  sqid: {
    type: Number,
  },
  parentguid: {
    type: String,
  },
  guid: {
    type: String,
  },
  //是否可编辑
  editable: {
    type: Boolean,
  },
  cglb: {
    type: String,
  },
});
//--------------------采购目录---------------------

// 弹窗
const cgml_dialog = reactive({
  title: "",
  storeVisible: false,
  accountVisible: false,
  cgmlVisible: false, // 采购目录选择弹窗
});
// 打开采购目录选择对话框
const handleOpenCgmlDialog = () => {
  cgml_dialog.cgmlVisible = true;
  cgml_dialog.title = "选择采购目录";
};

// 处理采购目录选择
const handleCgmlcodeNodeClick = (treeNodeValue: string) => {
  console.log("treeNodeValue", treeNodeValue);
  if (!treeNodeValue) {
    ElMessage.warning("请选择采购目录");
    return;
  }
  cgml_dialog.cgmlVisible = false;
  mxForm.cgml = treeNodeValue;

  // handleOpenDialog(true, undefined, undefined, treeNodeValue);
};

//监听formData.czcode的值，并从codes中找到对应的name
watch(
  () => mxForm.cgml,
  (newVal) => {
    if (newVal) {
      const codeItem = findInTree(Cgmlcodes.value, newVal);
      console.log("codeItem", codeItem);
      if (codeItem) {
        mxForm.cgmlname = codeItem.label;
      }
    } else {
      mxForm.cgmlname = "";
    }
  }
);

// 递归查找树形结构中的节点
function findInTree(tree: any[], value: string): any {
  for (const node of tree) {
    if (node.value === value) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findInTree(node.children, value);
      if (found) {
        return found;
      }
    }
  }
  return null;
}
//---------------------采购目录------------------------
//——————————————————————————————————————————————————查询相关
const loading = ref(false);

//——————————————————————————————————————————————————操作相关

//——————————————————————————————————————————————————弹窗相关参数
onMounted(() => {
  //console.log("jfid:", props.id);
  handleFormQuery(props.id);
});

/*
watch(
   () => props.id,
   (newValue) => {
     handleFormQuery(newValue);
   }
 );*/

watch(
  () => props.sqid,
  (newValue) => {
    //jfqueryParams.sqid = newValue;
  }
);
watch(
  [() => mxForm.sl, () => mxForm.price],
  ([newSl, newPrice]) => {
    const quantity = Math.max(0, Number(newSl) || 0);
    const price = Math.max(0, Number(newPrice) || 0);
    mxForm.ysje = quantity * price;
  },
  { immediate: true }
);
// 明确暴露给父组件的方法
defineExpose({
  // handleAddMx,
  handleClearForm,
});
</script>
<style lang="scss" scoped></style>
