<template>
  <el-tabs v-model="activeName" type="card" class="tabs">
    <el-tab-pane
      v-if="(pageType === 'check' || pageType === 'balance') && checkstatus == 0"
      name="check"
    >
      <template #label>
        <span class="custom-tabs-label">
          <el-icon>
            <Checked />
          </el-icon>
          <span>审批表单</span>
        </span>
      </template>
      <Title name="审批单" />
      <CheckTest ref="compBRef" />
      <CheckForm ref="CheckFormRef" :netcode="props.netcode" :guid="props.guid" sjmc="入库审核" />
      <el-form label-width="120px">
        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="getDataFromChild('Forward')">提交</el-button>
          <el-button type="danger" @click="getDataFromChild('After')">退回</el-button>
        </el-form-item>
      </el-form>
    </el-tab-pane>
    <el-tab-pane label="基本信息" name="store">
      <Store
        :id="props.id"
        :guid="props.guid"
        :editable="props.editable"
        :zclxbh="props.zclxbh"
        :czcode="props.czcode"
        :RefreshFatherDrawer="RefreshFatherDrawer"
        @UpdateCard="UpdateCard"
        @UpdateZclxbh="UpdateZclxbh"
        @CheckFileRequired="CheckFileRequired"
      />
    </el-tab-pane>
    <el-tab-pane label="卡片信息" name="card">
      <Card
        :key="cardKey"
        :editable="props.editable"
        :guid="props.guid"
        :isEquipment="isEquipment"
      />
    </el-tab-pane>
    <el-tab-pane label="资产照片" name="img">
      <MultiImageUpload
        :key="props.guid"
        :guid="props.guid"
        code="02030501"
        :showUploadBtn="props.editable"
      />
    </el-tab-pane>
    <el-tab-pane label="附属件信息" name="extra">
      <Extra :id="props.id" :editable="props.editable" :guid="props.guid" />
    </el-tab-pane>
    <el-tab-pane label="附件文档" name="files">
      <Title name="附件上传" />
      <FileUpload
        ref="fileRef"
        :key="props.guid"
        :guid="props.guid"
        code="020302"
        :show="props.editable"
      />
    </el-tab-pane>
    <el-tab-pane>
      <template #label>
        <span class="custom-tabs-label">
          <el-icon>
            <Timer />
          </el-icon>
          <span>流程信息</span>
        </span>
      </template>
      <CheckList :guid="props.guid" :netcode="props.netcode" />
    </el-tab-pane>
  </el-tabs>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import storeAPI from "@/api/properties/store";
import Store from "@/views/properties/store/components/Store.vue";
import Card from "@/views/properties/store/components/Card.vue";
import Extra from "@/views/properties/store/extra/index.vue";
import CheckForm from "@/components/GY/WorkFlowCheck/components/CheckForm.vue";
import CheckList from "@/components/GY/WorkFlowCheck/components/CheckList.vue";
import CheckTest from "@/components/GY/WorkFlowCheck/components/CheckTest.vue";
import { ElLoading } from "element-plus";
// 直接导入整个组件
import FileUpload from "@/components/Upload/FileUpload.vue"; // 导入 FileUpload 组件
const route = useRoute();
const pageType = ref(route.query.type?.toString() || "view");
const activeName = ref(
  pageType.value == "check" || pageType.value == "balance" ? "check" : "store"
);
const fileRef = ref<InstanceType<typeof FileUpload> | null>(null);
const cardKey = ref(0);
const isEquipment = ref(false);

const props = defineProps({
  editable: {
    type: Boolean,
    required: true,
  },
  id: {
    type: Number,
  },
  guid: {
    type: String,
    required: true,
  },
  zclxbh: {
    type: String,
  },
  czcode: {
    type: String,
  },
  RefreshFatherDrawer: {
    type: Function,
    required: true,
  },
  netcode: {
    type: String,
    required: true,
  },
  dcbm: {
    type: String,
    required: true,
  },
  checkstatus: {
    type: Number,
  },
});
const CheckFormRef = ref<InstanceType<typeof CheckForm> | null>(null);
const childData = ref<any | null>(null);
const submitData = reactive<any>({
  dto: ref<any>({}),
  data: ref<any>({}),
});

const getDataFromChild = async (active: string) => {
  if (CheckFormRef.value) {
    try {
      // 明确捕获错误并处理，避免返回undefined
      childData.value = await CheckFormRef.value.getFormData(active);
      console.log("子页面数据:", childData.value);
      submitData.dto = childData.value;

      const confirmMessage = active === "Forward" ? "确认要提交吗？" : "确认要退回吗？";
      const confirmTitle = active === "Forward" ? "提交确认" : "退回确认";

      try {
        await ElMessageBox.confirm(confirmMessage, confirmTitle, {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });

        const dialogloading = ElLoading.service({
          lock: true,
          text: "处理中",
        });

        await storeAPI
          .submit(props.guid, submitData)
          .then(() => {
            //第三层提交完成
            active == "Forward" ? ElMessage.success("提交成功") : ElMessage.success("退回成功");
            props.RefreshFatherDrawer(true);
          })
          .finally(() => {
            dialogloading.close();
          });
      } catch (e) {
        console.log("操作已取消");
      }
    } catch (error: any) {
      // 统一处理表单数据获取错误
      ElMessage.error(error.message || "获取表单数据失败");
    }
  }
};

function UpdateCard() {
  cardKey.value++;
}

function UpdateZclxbh() {
  isEquipment.value = !isEquipment.value;
}

function CheckFileRequired(callback: (isValid: boolean) => void): void {
  let isValid = true;
  if (fileRef.value) {
    isValid = fileRef.value.CheckIsRequired();
  }
  callback(isValid);
}
</script>

<style scoped>
.tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
</style>
