<template>
  <Title name="标段信息">
    <div>
      <el-button type="success" @click="handlePrevious()">上一步</el-button>
      <el-button v-if="bdcount <= 0" type="primary" @click="handleCreateBd()">创建标段</el-button>
      <el-button type="danger" @click="handleNext()">下一步</el-button>
    </div>
  </Title>
  <ucMxList v-if="bdcount <= 0" ref="mxListRef" :wjid="props.wjid" :editable="false" />
  <!-- ********************** 列表内容 ********************** -->
  <el-table
    v-if="bdcount > 0"
    ref="dataTableRef"
    v-loading="loading"
    :data="pageData"
    highlight-current-row
    border
  >
    <el-table-column label="序号" type="index" width="55" align="center" fixed>
      <template #default="scope">
        <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
      </template>
    </el-table-column>
    <el-table-column key="bdbh" label="标段编号" prop="bdbh" min-width="150" align="center" />
    <el-table-column key="bdmc" label="标段名称" prop="bdmc" min-width="150" align="center" />
    <el-table-column key="cgfs" label="采购方式" prop="cgfsname" min-width="150" align="center" />
    <el-table-column key="ysjf" label="预算金额" prop="ysjf" min-width="150" align="center" />
    <el-table-column key="bzj" label="保证金" prop="bzj" min-width="150" align="center" />
    <el-table-column key="mxcount" label="明细数量" prop="mxcount" min-width="150" align="center" />
    <el-table-column fixed="right" label="操作" width="220">
      <template #default="scope">
        <el-button
          type="primary"
          size="small"
          icon="Edit"
          link
          @click="handleOpenDialog(true, scope.row.guid)"
        >
          编辑
        </el-button>
        <el-button
          type="danger"
          size="small"
          icon="Delete"
          link
          @click="handleDelete(scope.row.guid)"
        >
          删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>
  <!-- ********************** 翻页 ********************** -->
  <pagination
    v-if="total > 0"
    v-model:total="total"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    @pagination="handleQuery()"
  />
  <!-- 采购标段表单弹窗 -->
  <el-drawer
    v-model="dialog.visible"
    :title="dialog.title"
    append-to-body
    size="55%"
    :before-close="handleCloseDialog"
  >
    <el-card>
      <template #header>
        <Title name="标段信息">
          <div>
            <el-button type="primary" @click="handleSave()">保存</el-button>
          </div>
        </Title>
      </template>

      <el-form
        ref="dataFormRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        :inline="true"
        :disabled="!editable"
      >
        <el-row :gutter="20">
          <el-form-item v-show="false" label="采购标段ID|hidden" prop="bdid">
            <el-input v-model="formData.bdid" placeholder="采购标段ID|hidden" />
          </el-form-item>
          <el-form-item v-show="false" label="guid|hidden" prop="guid">
            <el-input v-model="formData.guid" placeholder="guid|hidden" />
          </el-form-item>
          <el-col :span="12">
            <el-form-item label="标段编号" prop="bdbh">
              <el-input v-model="formData.bdbh" placeholder="标段编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标段名称" prop="bdmc">
              <el-input v-model="formData.bdmc" placeholder="标段名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预算金额" prop="ysjf">
              <el-text>{{ formData.ysjf }}</el-text>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保证金" prop="bzj">
              <el-input v-model="formData.bzj" placeholder="保证金" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="标段内容" prop="bdnr">
              <el-input
                v-model="formData.bdnr"
                placeholder="标段内容"
                type="textarea"
                :rows="2"
                style="width: 400px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="notes">
              <el-input
                v-model="formData.notes"
                placeholder="备注"
                type="textarea"
                :rows="2"
                style="width: 400px"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer />
    </el-card>
  </el-drawer>
</template>
<script setup lang="ts">
import { defineProps, defineEmits, ref, reactive } from "vue";
import ZxcgCgbdAPI, { ZxcgCgbdPageVO, ZxcgCgbdForm, ZxcgCgbdPageQuery } from "@/api/cg/zxcg-cgbd";
import ucMxList from "@/views/cg/mx/components/ucMxList.vue";
import ZxcgMxAPI from "@/api/cg/zxcg-mx";

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);
const mxListRef = ref(ucMxList);
const bdcount = ref(0);
const props = defineProps({
  //guid
  wjid: {
    type: Number,
    required: true,
  },
  //是否可编辑
  editable: {
    type: Boolean,
    required: true,
  },
});

const queryParams = reactive<ZxcgCgbdPageQuery>({
  pageNum: 1,
  pageSize: 10,
});

queryParams.wjid = props.wjid;
const emits = defineEmits(["nowstep"]);

// 采购标段表格数据
const pageData = ref<ZxcgCgbdPageVO[]>([]);
const formData = reactive<ZxcgCgbdForm>({});
// 弹窗
const dialog = reactive({
  title: "",
  guid: "",
  visible: false,
  editEnable: false,
});
/** 查询标段 */
function handleQuery() {
  loading.value = true;
  ZxcgCgbdAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
      bdcount.value = data.list.length;
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleCreateBd() {
  if (mxListRef.value) {
    if (mxListRef.value.removeIds.length == 0) {
      ElMessage.warning("请选择明细");
      return;
    }
    ZxcgMxAPI.getYsje(mxListRef.value.removeIds.join(","))
      .then((res) => {
        formData.ysjf = res as unknown as number;
        formData.wjid = props.wjid;
        formData.mxids = mxListRef.value.removeIds.join(",");
      })
      .then(() => {
        dialog.title = "新增标段";
        dialog.visible = true;
        dialog.editEnable = true;
        console.log("formData", formData);
      });
  }
}
function handleCloseDialog() {
  dialog.visible = false;
  handleQuery();
}

function handleDelete(guid?: string) {
  if (!guid) {
    ElMessage.warning("请选择要删除的标段");
    return;
  }
  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      ZxcgCgbdAPI.deleteByIds(guid)
        .then(() => {
          ElMessage.success("删除成功");
          handleQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

// 采购标段表单校验规则
const rules = reactive({});

/** 打开标段文件弹窗 */
function handleOpenDialog(editEnable: boolean, guid?: string) {
  dialog.visible = true;
  dialog.editEnable = editEnable;
  console.log("handleOpenDialog", editEnable, guid);
  if (guid) {
    dialog.title = "修改标段文件";
    dialog.guid = guid;
    ZxcgCgbdAPI.getFormData(guid)
      .then((res) => {
        Object.assign(formData, res);
        console.log("formData", formData);
      })
      .finally(() => {});
  } else {
    dialog.title = "新增标段文件";
    dialog.guid = "";
  }
}

const handleSave = async () => {
  dataFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      await ZxcgCgbdAPI.save(formData)
        .then(async (res) => {
          ElMessage.success("保存成功");
        })
        .catch((error) => {
          ElMessage.error("操作失败，失败原因：" + error.message);
        })
        .finally(() => {});
    }
  });
};

const handleNext = async () => {
  emits("nowstep", 3);
};

const handlePrevious = async () => {
  emits("nowstep", 1);
};

onMounted(() => {
  handleQuery();
});
</script>
<style scoped></style>
