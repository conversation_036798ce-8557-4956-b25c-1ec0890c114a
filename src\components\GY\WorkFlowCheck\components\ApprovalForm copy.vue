<template>
  <div class="approval-form approval-card">
    <!-- 审批表单卡片 -->
    <!-- 审批意见 -->
    <div v-if="showApprovalInput" class="approval-opinion">
      <div class="opinion-title">审批意见</div>
      <el-form ref="approvalFormRef" :model="approvalForm" :rules="approvalRules">
        <el-form-item prop="shyj">
          <el-input
            v-model="approvalForm.shyj"
            type="textarea"
            :rows="3"
            placeholder="请输入审批意见"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div v-if="showActions" class="approval-actions">
      <el-button type="primary" @click="handleApprove">同意</el-button>
      <el-button type="danger" @click="handleReject">驳回</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>

    <!-- 审批流程图 -->
    <div class="approval-flow">
      <div class="flow-title">审批流程</div>
      <el-steps :active="activeStep" finish-status="success" simple>
        <el-step
          v-for="(step, index) in steps"
          :key="index"
          :title="step.title"
          :description="step.description"
        />
      </el-steps>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, PropType } from "vue";
import WorkFlowAPI from "@/api/system/workflow";
import { ElMessage, ElMessageBox, FormInstance } from "element-plus";

defineOptions({
  name: "ApprovalForm",
});

// 定义组件属性
const props = defineProps({
  // 表单标题
  title: {
    type: String,
    default: "审批表单",
  },
  // 表单数据
  guid: {
    type: String,
    required: true,
  },
  // 表单数据
  netcode: {
    type: String,
    required: true,
  },
  // 表单数据
  sjmc: {
    type: String,
    default: "",
  },
  // 审批状态：0-待审批，1-已通过，2-已驳回
  status: {
    type: Number,
    default: 0,
  },
  // 当前激活的步骤
  activeStep: {
    type: Number,
    default: 0,
  },
  // 是否显示审批输入框
  showApprovalInput: {
    type: Boolean,
    default: true,
  },
  // 是否显示操作按钮
  showActions: {
    type: Boolean,
    default: true,
  },
});

// 定义事件
const emit = defineEmits(["Forward", "After", "cancel"]);

// 表单引用
const approvalFormRef = ref<FormInstance>();
const steps = ref<any[]>([]);
// 审批表单数据
const approvalForm = reactive({
  shyj: "",
  guid: props.guid || "",
  nowCode: props.netcode || "",
  sjmc: props.sjmc || "",
});

// 表单验证规则
const approvalRules = {
  shyj: [
    { required: true, message: "请输入审批意见", trigger: "blur" },
    { min: 2, max: 200, message: "长度在 2 到 200 个字符", trigger: "blur" },
  ],
};

// 获取状态类型
const getStatusType = (status: number) => {
  const statusMap: Record<number, string> = {
    0: "warning", // 待审批
    1: "success", // 已通过
    2: "danger", // 已驳回
  };
  return statusMap[status] || "info";
};

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: "待审批",
    1: "已通过",
    2: "已驳回",
  };
  return statusMap[status] || "未知状态";
};

// 提交审批
const submitApproval = async (active: string) => {
  if (!approvalFormRef.value) return;

  await approvalFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const data: any = {
          ...approvalForm,
          active,
        };

        await WorkFlowAPI.workflowActive(data);

        // 触发对应事件
        if (active === "Forward") {
          emit("Forward", approvalForm);
        } else if (active === "After") {
          emit("After", approvalForm);
        }
      } catch (error) {
        console.error("审批操作失败:", error);
        ElMessage.error("操作失败，请重试");
      }
    }
  });
};

// 同意处理
const handleApprove = () => {
  console.log("123", props.guid, props.netcode, "11");
  ElMessageBox.confirm("确认同意此审批?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "success",
  })
    .then(() => {
      submitApproval("Forward");
    })
    .catch(() => {
      // 取消操作
    });
};

// 驳回处理
const handleReject = () => {
  ElMessageBox.confirm("确认驳回此审批?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      submitApproval("After");
    })
    .catch(() => {
      // 取消操作
    });
};

// 取消处理
const handleCancel = () => {
  console.log("取消操作");
  emit("cancel");
};
</script>

<style lang="scss" scoped>
.approval-form {
  width: 100%;
}

.approval-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: 16px;
      font-weight: bold;
    }
  }

  .approval-info {
    margin-bottom: 20px;
  }

  .approval-flow {
    margin: 20px 0;

    .flow-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 15px;
      color: var(--el-text-color-primary);
    }
  }

  .approval-history {
    margin: 20px 0;

    .history-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 15px;
      color: var(--el-text-color-primary);
    }

    .timeline-content {
      h4 {
        margin: 0;
        font-size: 14px;
        color: var(--el-text-color-primary);
      }

      p {
        margin: 5px 0 0;
        font-size: 13px;
        color: var(--el-text-color-secondary);
      }
    }
  }

  .approval-opinion {
    margin: 20px 0;

    .opinion-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 15px;
      color: var(--el-text-color-primary);
    }
  }

  .approval-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
  }
}
</style>
