import request from "@/utils/request";

const ZxcgCght_BASE_URL = "/api/v1/zxcgCghts";

const ZxcgCghtAPI = {
  /** 获取合同分页数据 */
  getPage(queryParams?: ZxcgCghtPageQuery) {
    return request<any, PageResult<ZxcgCghtPageVO[]>>({
      url: `${ZxcgCght_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 获取合同表单数据
   *
   * @param id ZxcgCghtID
   * @returns ZxcgCght表单数据
   */
  getFormData(guid: string) {
    return request<any, ZxcgCghtForm>({
      url: `${ZxcgCght_BASE_URL}/${guid}/form`,
      method: "get",
    });
  },

  /** 添加合同*/
  add(data: ZxcgCghtForm) {
    return request({
      url: `${ZxcgCght_BASE_URL}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新合同
   *
   * @param id ZxcgCghtID
   * @param data ZxcgCght表单数据
   */
  update(id: number, data: ZxcgCghtForm) {
    return request({
      url: `${ZxcgCght_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 批量删除合同，多个以英文逗号(,)分割
   *
   * @param ids 合同ID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${ZxcgCght_BASE_URL}/${ids}`,
      method: "delete",
    });
  },
};

export default ZxcgCghtAPI;

/** 合同分页查询参数 */
export interface ZxcgCghtPageQuery extends PageQuery {
  /** 合同编号 */
  htbh?: string;
  /** 合同名称 */
  htmc?: string;
  pagetype?: string;
}

/** 合同表单对象 */
export interface ZxcgCghtForm {
  /** 采购合同ID   */
  htid?: number;
  /** guid */
  guid?: string;
  /** 合同编号 */
  htbh?: string;
  /** 合同名称 */
  htmc?: string;
  /** 合同类型 */
  htlx?: string;
  /** 文件ID */
  wjid?: number;
  /** 标段ID */
  bdid?: number;
  /** 合同甲方 */
  htjf?: string;
  /** 甲方联系人 */
  jflxr?: string;
  /** 甲方联系方式 */
  jflxfs?: string;
  /** 合同乙方 */
  htyf?: string;
  /** 乙方企业统一社会信用编码 */
  htyfcode?: string;
  /** 乙方联系人 */
  yflxr?: string;
  /** 乙方联系方式 */
  yflxfs?: string;
  /** 合同签订日期 */
  htqdrq?: Date;
  /** 合同金额(元) */
  htje?: number;
  /** 付款方式 */
  fkfs?: string;
  /** 合同内容 */
  htnr?: string;
  /** 备注 */
  notes?: string;
  /** 登记人 */
  djr?: string;
  /** 登记部门 */
  djbm?: string;
  /** 登记时间 */
  djsj?: Date;
  /** 流程节点 */
  netcode?: string;
  /** 数据状态 */
  sjzt?: string;
  /** 下一审批人 */
  nexter?: string;
  /** 下一审批部门 */
  nextbm?: string;
  /** 合同模板 */
  templateid?: string;
  /** 合同签订人 */
  htqdr?: string;
  /** 审批人 */
  spr?: string;
  /** 标识 */
  flag?: number;
  /** 状态 */
  state?: number;
  /** 合同状态 */
  zxzt?: number;
  /** 保证金 */
  zbj?: string;
  /** 保证金缴纳 */
  zbjjn?: string;
  /** 保证金退回 */
  zbjth?: string;
  /** 申请id */
  sqid?: number;
  /** 部门负责人 */
  bmfzr?: string;
  /** 负责人职工号 */
  fzrbm?: string;
  /** 申请部门编码 */
  sqbm?: string;
  /** 主管部门 */
  zgbm?: string;
  /** 归档编号 */
  gdbh?: string;
  /** 归档时间 */
  gdsj?: Date;
  /** 归档地点 */
  gddd?: string;
  /** 合同预付额 */
  by1?: string;
  /** 履约保证金 */
  by2?: string;
  /** 付余款时间 */
  by3?: string;
  /** 验收后付款 */
  by4?: string;
  /** 返还质保金期限 */
  by5?: string;
  /** 交货期限 */
  by6?: string;
  /** 交货地点 */
  by7?: string;
  /** 提出异议期限 */
  by8?: string;
  /** 免费上门维修期限 */
  by9?: string;
  /** 维修响应时间 */
  by10?: string;
  /** 货物名称 */
  by11?: string;
  /** 验收期限 */
  by12?: string;
  /** 验收后付款期限 */
  by13?: string;
  /** 货物数量 */
  by14?: string;
  /** ysxm.yslb(2-临时经费) */
  by15?: string;
  /** 货物质量 */
  by16?: string;
  /** 移交时间 */
  by17?: string;
  /** 移交状态 */
  by18?: string;
  /** 备用19 */
  by19?: string;
  /** 合同采购类型 */
  htcglx?: string;
  /** 合同属性类型 */
  htsxlx?: string;
  /** 使用情况 */
  syqk?: string;
  /** 实物资产 */
  swzc?: string;
  /** 所在位置 */
  szwz?: string;
  /** 场地位置 */
  cdwz?: string;
  /** 乙方法人 */
  yffr?: string;
  /** 乙方地址 */
  yfdz?: string;
  /** 乙方邮编 */
  yfyb?: string;
  /** 乙方传真 */
  yfcz?: string;
  /** 乙方邮箱 */
  yfyx?: string;
  /** 乙方开户银行 */
  yfkhyh?: string;
  /** 乙方银行账号 */
  yfyhzh?: string;
  /** 是否备案流程 */
  sfba?: string;
  /** 任务的备案方式 */
  bafs?: string;
}

/** 合同分页对象 */
export interface ZxcgCghtPageVO {
  /** 采购合同ID   */
  htid?: number;
  /** guid */
  guid?: string;
  /** 合同编号 */
  htbh?: string;
  /** 合同名称 */
  htmc?: string;
  /** 合同类型 */
  htlx?: string;
  /** 文件ID */
  wjid?: number;
  /** 标段ID */
  bdid?: number;
  /** 合同甲方 */
  htjf?: string;
  /** 甲方联系人 */
  jflxr?: string;
  /** 甲方联系方式 */
  jflxfs?: string;
  /** 合同乙方 */
  htyf?: string;
  /** 乙方企业统一社会信用编码 */
  htyfcode?: string;
  /** 乙方联系人 */
  yflxr?: string;
  /** 乙方联系方式 */
  yflxfs?: string;
  /** 合同签订日期 */
  htqdrq?: Date;
  /** 合同金额(元) */
  htje?: number;
  /** 付款方式 */
  fkfs?: string;
  /** 合同内容 */
  htnr?: string;
  /** 备注 */
  notes?: string;
  /** 登记人 */
  djr?: string;
  /** 登记部门 */
  djbm?: string;
  /** 登记时间 */
  djsj?: Date;
  /** 流程节点 */
  netcode?: string;
  /** 数据状态 */
  sjzt?: string;
  /** 下一审批人 */
  nexter?: string;
  /** 下一审批部门 */
  nextbm?: string;
  /** 合同模板 */
  templateid?: string;
  /** 合同签订人 */
  htqdr?: string;
  /** 审批人 */
  spr?: string;
  /** 标识 */
  flag?: number;
  /** 状态 */
  state?: number;
  /** 合同状态 */
  zxzt?: number;
  /** 保证金 */
  zbj?: string;
  /** 保证金缴纳 */
  zbjjn?: string;
  /** 保证金退回 */
  zbjth?: string;
  /** 申请id */
  sqid?: number;
  /** 部门负责人 */
  bmfzr?: string;
  /** 负责人职工号 */
  fzrbm?: string;
  /** 申请部门编码 */
  sqbm?: string;
  /** 主管部门 */
  zgbm?: string;
  /** 归档编号 */
  gdbh?: string;
  /** 归档时间 */
  gdsj?: Date;
  /** 归档地点 */
  gddd?: string;
  /** 合同预付额 */
  by1?: string;
  /** 履约保证金 */
  by2?: string;
  /** 付余款时间 */
  by3?: string;
  /** 验收后付款 */
  by4?: string;
  /** 返还质保金期限 */
  by5?: string;
  /** 交货期限 */
  by6?: string;
  /** 交货地点 */
  by7?: string;
  /** 提出异议期限 */
  by8?: string;
  /** 免费上门维修期限 */
  by9?: string;
  /** 维修响应时间 */
  by10?: string;
  /** 货物名称 */
  by11?: string;
  /** 验收期限 */
  by12?: string;
  /** 验收后付款期限 */
  by13?: string;
  /** 货物数量 */
  by14?: string;
  /** ysxm.yslb(2-临时经费) */
  by15?: string;
  /** 货物质量 */
  by16?: string;
  /** 移交时间 */
  by17?: string;
  /** 移交状态 */
  by18?: string;
  /** 备用19 */
  by19?: string;
  /** 合同采购类型 */
  htcglx?: string;
  /** 合同属性类型 */
  htsxlx?: string;
  /** 使用情况 */
  syqk?: string;
  /** 实物资产 */
  swzc?: string;
  /** 所在位置 */
  szwz?: string;
  /** 场地位置 */
  cdwz?: string;
  /** 乙方法人 */
  yffr?: string;
  /** 乙方地址 */
  yfdz?: string;
  /** 乙方邮编 */
  yfyb?: string;
  /** 乙方传真 */
  yfcz?: string;
  /** 乙方邮箱 */
  yfyx?: string;
  /** 乙方开户银行 */
  yfkhyh?: string;
  /** 乙方银行账号 */
  yfyhzh?: string;
  /** 是否备案流程 */
  sfba?: string;
  /** 任务的备案方式 */
  bafs?: string;
}
