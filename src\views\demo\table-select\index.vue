<!-- 列表选择器示例 -->
<script setup lang="ts">
import selectConfig from "./config/select";

interface IUser {
  id: number;
  username: string;
  nickname: string;
  mobile: string;
  genderLabel: string;
  avatar: string;
  email: string | null;
  status: number;
  deptName: string;
  roleNames: string;
  createTime: string;
}
const selectedUser = ref<IUser[]>([]);
function handleConfirm(data: IUser[]) {
  selectedUser.value = data;
}
const text = computed(() => {
  if (!selectedUser.value || selectedUser.value.length === 0) {
    return "请选择用户";
  }
  // 使用数组映射和join方法更简洁地处理字符串拼接
  const nicknames = selectedUser.value.map(item => item.nickname || "未知用户");
  return nicknames.join("、");
});
</script>

<template>
  <div class="app-container">
    <el-link href="https://gitee.com/youlaiorg/vue3-element-admin/blob/master/src/views/demo/table-select/index.vue"
      type="primary" target="_blank" class="mb-10">
      示例源码 请点击>>>>
    </el-link>
    <table-select :text="text" :select-config="selectConfig" @confirm-click="handleConfirm">
      <template #status="scope">
        <el-tag :type="scope.row[scope.prop] == 1 ? 'success' : 'info'">
          {{ scope.row[scope.prop] == 1 ? "启用" : "禁用" }}
        </el-tag>
      </template>
    </table-select>
  </div>
</template>
