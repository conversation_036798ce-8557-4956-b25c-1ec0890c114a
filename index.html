<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Vue3 + Vite5 + TypeScript5 + Element-Plus 的后台管理模板，配套接口文档和后端源码，vue-element-admin 的 Vue3 版本"
    />
    <meta
      name="keywords"
      content="vue,element-plus,typescript,vue-element-admin,vue3-element-admin"
    />
    <title>浙江工越信息科技有限公司组建库系统</title>
  </head>

  <body>
    <div id="app">
      <div class="loader"></div>
    </div>

    <script src="https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/1.2.0-beta.5/libs/cn/index.js"></script>
    <script>
      new CozeWebSDK.WebChatClient({
        config: {
          bot_id: "7486364971224530944",
        },
        componentProps: {
          title: "Coze",
        },
        auth: {
          type: "token",
          token: "pat_G8qK89Zc8YWFg0SMdid2cyhzYzcJ8m6qZzUmnDYBVMaxMsEXvfu1eqSl1Y9LBIOW",
          onRefreshToken: function () {
            return "pat_G8qK89Zc8YWFg0SMdid2cyhzYzcJ8m6qZzUmnDYBVMaxMsEXvfu1eqSl1Y9LBIOW";
          },
        },
      });
    </script>
  </body>
  <script type="module" src="/src/main.ts"></script>

  <style>
    html,
    body,
    #app {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }

    .loader {
      --d: 22px;

      width: 4px;
      height: 4px;
      color: #25b09b;
      border-radius: 50%;
      box-shadow:
        calc(1 * var(--d)) calc(0 * var(--d)) 0 0,
        calc(0.707 * var(--d)) calc(0.707 * var(--d)) 0 1px,
        calc(0 * var(--d)) calc(1 * var(--d)) 0 2px,
        calc(-0.707 * var(--d)) calc(0.707 * var(--d)) 0 3px,
        calc(-1 * var(--d)) calc(0 * var(--d)) 0 4px,
        calc(-0.707 * var(--d)) calc(-0.707 * var(--d)) 0 5px,
        calc(0 * var(--d)) calc(-1 * var(--d)) 0 6px;
      animation: l27 1s infinite steps(8);
    }

    @keyframes l27 {
      100% {
        transform: rotate(1turn);
      }
    }
  </style>
</html>
