<template>
  <div class="mobile-dept-search">
    <!-- 搜索栏 -->
    <SearchBar
      v-model="searchKeyword"
      placeholder="搜索部门名称"
      :show-filter="false"
      @search="handleSearch"
      @clear="handleClear"
    />

    <!-- 搜索结果 -->
    <div class="search-results">
      <!-- 结果统计 -->
      <div v-if="hasSearched" class="result-stats">
        <span class="stats-text">找到 {{ filteredDeptList.length }} 个部门</span>
        <div class="view-toggle">
          <button
            class="toggle-btn"
            :class="{ active: viewMode === 'tree' }"
            @click="viewMode = 'tree'"
          >
            <svg-icon icon-class="tree" size="14px" />
            树形
          </button>
          <button
            class="toggle-btn"
            :class="{ active: viewMode === 'list' }"
            @click="viewMode = 'list'"
          >
            <svg-icon icon-class="list" size="14px" />
            列表
          </button>
        </div>
      </div>

      <!-- 部门列表 -->
      <div class="dept-list" :class="viewMode">
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <span>加载中...</span>
        </div>

        <div v-else-if="filteredDeptList.length === 0 && hasSearched" class="empty-result">
          <svg-icon icon-class="empty" size="48px" />
          <div class="empty-text">未找到相关部门</div>
          <div class="empty-tips">请尝试调整搜索条件</div>
        </div>

        <div v-else class="dept-items">
          <!-- 树形视图 -->
          <template v-if="viewMode === 'tree'">
            <DeptTreeItem
              v-for="dept in treeData"
              :key="dept.id"
              :dept="dept"
              :search-keyword="searchKeyword"
              @select="selectDept"
              @expand="toggleExpand"
            />
          </template>

          <!-- 列表视图 -->
          <template v-else>
            <div
              v-for="dept in filteredDeptList"
              :key="dept.id"
              class="dept-item list-item"
              @click="selectDept(dept)"
            >
              <div class="dept-icon">
                <svg-icon icon-class="department" size="20px" />
              </div>
              
              <div class="dept-info">
                <div class="dept-header">
                  <span class="dept-name" v-html="highlightKeyword(dept.name)"></span>
                  <span class="dept-level">{{ getDeptLevel(dept) }}级</span>
                </div>
                
                <div class="dept-details">
                  <div class="detail-row">
                    <span class="detail-label">负责人:</span>
                    <span class="detail-value">{{ dept.leader || '-' }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="detail-label">联系电话:</span>
                    <span class="detail-value">{{ dept.phone || '-' }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="detail-label">人员数量:</span>
                    <span class="detail-value">{{ dept.userCount || 0 }}人</span>
                  </div>
                </div>
              </div>

              <div class="dept-actions">
                <button class="action-btn" @click.stop="contactDept(dept)">
                  <svg-icon icon-class="phone" size="14px" />
                </button>
                <button class="action-btn" @click.stop="viewDeptUsers(dept)">
                  <svg-icon icon-class="user" size="14px" />
                </button>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 部门详情弹窗 -->
    <div v-if="showDeptDetail" class="dept-detail-overlay" @click="closeDeptDetail">
      <div class="dept-detail-panel" @click.stop>
        <div class="detail-header">
          <span class="detail-title">部门详情</span>
          <button class="close-btn" @click="closeDeptDetail">
            <svg-icon icon-class="close" size="16px" />
          </button>
        </div>
        
        <div class="detail-content">
          <div class="detail-icon">
            <svg-icon icon-class="department" size="48px" />
          </div>
          
          <div class="detail-info">
            <div class="info-item">
              <span class="info-label">部门名称</span>
              <span class="info-value">{{ selectedDept?.name }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">上级部门</span>
              <span class="info-value">{{ getParentDeptName(selectedDept) || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">部门负责人</span>
              <span class="info-value">{{ selectedDept?.leader || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">联系电话</span>
              <span class="info-value">{{ selectedDept?.phone || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">邮箱</span>
              <span class="info-value">{{ selectedDept?.email || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">人员数量</span>
              <span class="info-value">{{ selectedDept?.userCount || 0 }}人</span>
            </div>
            <div class="info-item">
              <span class="info-label">创建时间</span>
              <span class="info-value">{{ formatDate(selectedDept?.createTime) }}</span>
            </div>
          </div>
          
          <div class="detail-actions">
            <button class="detail-action-btn primary" @click="contactDept(selectedDept)">
              <svg-icon icon-class="phone" size="16px" />
              联系
            </button>
            <button class="detail-action-btn" @click="viewDeptUsers(selectedDept)">
              <svg-icon icon-class="user" size="16px" />
              查看人员
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import SearchBar from '@/components/mobile/SearchBar/index.vue'
import DeptTreeItem from './components/DeptTreeItem.vue'
import DeptAPI from '@/api/system/dept'

defineOptions({
  name: 'MobileDeptSearch'
})

// 搜索相关
const searchKeyword = ref('')
const hasSearched = ref(false)
const loading = ref(false)
const viewMode = ref<'tree' | 'list'>('tree')

// 数据
const deptList = ref<any[]>([])
const selectedDept = ref<any>(null)
const showDeptDetail = ref(false)
const expandedKeys = ref<Set<number>>(new Set())

// 树形数据
const treeData = computed(() => {
  return buildTree(deptList.value, 0)
})

// 过滤后的部门列表
const filteredDeptList = computed(() => {
  if (!searchKeyword.value) return deptList.value
  
  return deptList.value.filter(dept => 
    dept.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    (dept.leader && dept.leader.toLowerCase().includes(searchKeyword.value.toLowerCase()))
  )
})

// 处理搜索
const handleSearch = (keyword: string) => {
  searchKeyword.value = keyword
  hasSearched.value = true
}

// 处理清空
const handleClear = () => {
  searchKeyword.value = ''
  hasSearched.value = false
}

// 构建树形结构
const buildTree = (list: any[], parentId: number): any[] => {
  return list
    .filter(item => item.parentId === parentId)
    .map(item => ({
      ...item,
      children: buildTree(list, item.id),
      expanded: expandedKeys.value.has(item.id)
    }))
}

// 切换展开状态
const toggleExpand = (deptId: number) => {
  if (expandedKeys.value.has(deptId)) {
    expandedKeys.value.delete(deptId)
  } else {
    expandedKeys.value.add(deptId)
  }
}

// 选择部门
const selectDept = (dept: any) => {
  selectedDept.value = dept
  showDeptDetail.value = true
}

// 关闭部门详情
const closeDeptDetail = () => {
  showDeptDetail.value = false
  selectedDept.value = null
}

// 联系部门
const contactDept = (dept: any) => {
  if (dept.phone) {
    window.location.href = `tel:${dept.phone}`
  } else {
    ElMessage.warning('该部门未设置联系电话')
  }
}

// 查看部门人员
const viewDeptUsers = (dept: any) => {
  ElMessage.info('跳转到部门人员页面')
}

// 获取上级部门名称
const getParentDeptName = (dept: any) => {
  if (!dept || !dept.parentId) return null
  const parent = deptList.value.find(d => d.id === dept.parentId)
  return parent?.name
}

// 获取部门层级
const getDeptLevel = (dept: any) => {
  let level = 1
  let current = dept
  while (current.parentId && current.parentId !== 0) {
    level++
    current = deptList.value.find(d => d.id === current.parentId)
    if (!current) break
  }
  return level
}

// 高亮关键词
const highlightKeyword = (text: string) => {
  if (!searchKeyword.value) return text
  const regex = new RegExp(`(${searchKeyword.value})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

// 加载部门数据
const loadDeptList = async () => {
  loading.value = true
  try {
    const response = await DeptAPI.getDeptList()
    deptList.value = response || []
  } catch (error) {
    console.error('加载部门数据失败:', error)
    ElMessage.error('加载部门数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadDeptList()
})
</script>

<style lang="scss" scoped>
.mobile-dept-search {
  background: #f5f5f5;
  min-height: 100vh;
}

.search-results {
  padding: 0 16px;
}

.result-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 12px;
}

.stats-text {
  font-size: 14px;
  color: #666;
}

.view-toggle {
  display: flex;
  background: white;
  border-radius: 16px;
  padding: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggle-btn {
  padding: 6px 12px;
  border: none;
  background: transparent;
  border-radius: 14px;
  color: #666;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  
  &.active {
    background: var(--el-color-primary);
    color: white;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: #666;
  font-size: 14px;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f0f0f0;
  border-top: 2px solid var(--el-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.empty-result {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-text {
  font-size: 16px;
  margin: 16px 0 8px;
}

.empty-tips {
  font-size: 12px;
}

// 列表视图样式
.dept-list.list .dept-items {
  .list-item {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    
    &:active {
      background: #f8f8f8;
    }
  }
}

.dept-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f7ff;
  border-radius: 50%;
  margin-right: 12px;
  color: var(--el-color-primary);
}

.dept-info {
  flex: 1;
}

.dept-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.dept-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  
  :deep(mark) {
    background: #fff2e6;
    color: #fa8c16;
    padding: 0 2px;
  }
}

.dept-level {
  padding: 2px 6px;
  background: #f0f0f0;
  border-radius: 8px;
  font-size: 10px;
  color: #666;
}

.dept-details {
  .detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2px;
    font-size: 12px;
  }
  
  .detail-label {
    color: #999;
  }
  
  .detail-value {
    color: #666;
  }
}

.dept-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f5f5f5;
  border-radius: 16px;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:active {
    background: #e8e8e8;
  }
}

// 部门详情弹窗样式
.dept-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.dept-detail-panel {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-title {
  font-size: 18px;
  font-weight: 500;
}

.close-btn {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
}

.detail-content {
  padding: 20px;
  text-align: center;
}

.detail-icon {
  margin-bottom: 20px;
  color: var(--el-color-primary);
}

.detail-info {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  color: #666;
  font-size: 14px;
}

.info-value {
  color: #333;
  font-size: 14px;
}

.detail-actions {
  display: flex;
  gap: 12px;
}

.detail-action-btn {
  flex: 1;
  height: 40px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  
  &.primary {
    background: var(--el-color-primary);
    color: white;
  }
  
  &:not(.primary) {
    background: #f5f5f5;
    color: #666;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
