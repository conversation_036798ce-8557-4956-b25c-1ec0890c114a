import request from "@/utils/request";

const STORE_BASE_URL = "/api/v1/store";

const storeAPI = {
  getCzCodeTree() {
    return request<any, OptionType[]>({
      url: `${STORE_BASE_URL}/czcode/tree`,
      method: "get",
    });
  },

  getCzCodeList(queryParams?: CzQuery) {
    return request<any, OptionType[]>({
      url: `${STORE_BASE_URL}/czcode/list`,
      method: "get",
      params: queryParams,
    });
  },
  getCzCodeForm(id: number) {
    return request<any, ZcFlbForm>({
      url: `${STORE_BASE_URL}/czcode/${id}/form`,
      method: "get",
    });
  },

  updateCzcode(id: number, data: ZcFlbForm) {
    return request({
      url: `${STORE_BASE_URL}/czcode/${id}`,
      method: "put",
      data: data,
    });
  },

  getJyCodeTree() {
    return request<any, OptionType[]>({
      url: `${STORE_BASE_URL}/jycode/tree`,
      method: "get",
    });
  },
  //新增入库单相同字段/api/v1/store/addstoresame
  addsame(data: any): Promise<number> {
    return request({
      url: `${STORE_BASE_URL}/add-storesame`,
      method: "post",
      data: data,
    });
  },
  //删除入库单/api/v1/store/deletestoreform
  deleteByIds(guids: string) {
    return request({
      url: `${STORE_BASE_URL}/delete/${guids}`,
      method: "post",
    });
  },

  //获取表单信息api/v1/store/getdifferentstoreform
  getFormDataDiff(data: any) {
    return request<any, any>({
      url: `${STORE_BASE_URL}/get-differentstore`,
      method: "post",
      data: data,
    });
  },
  getFormDataSame(data: any) {
    return request<any, any>({
      url: `${STORE_BASE_URL}/get-samestore`,
      method: "post",
      data: data,
    });
  },

  //入库单分页列表/api/v1/store/getstorepage
  getPage(params?: any) {
    return request<any, PageResult<any[]>>({
      url: `${STORE_BASE_URL}/page`,
      method: "get",
      params: params,
    });
  },

  // 修改入库单/api/v1/store/updatestoredifferent
  updateDiff(data: ZcinfoDifferentForm) {
    return request({
      url: `${STORE_BASE_URL}/update-storedifferent`,
      method: "post",
      data: data,
    });
  },
  updateSame(data: any) {
    return request({
      url: `${STORE_BASE_URL}/update-storesame`,
      method: "post",
      data: data,
    });
  },

  submit(guid: string, data?: any) {
    return request({
      url: `${STORE_BASE_URL}/submit/${guid}`,
      method: "post",
      data: data,
    });
  },
  callback(guid: string) {
    return request({
      url: `${STORE_BASE_URL}/callback/${guid}`,
      method: "post",
    });
  },
  /**
   * 导入用户
   *
   * @param deptId 部门ID
   * @param file 导入文件
   */
  import(file: File) {
    const formData = new FormData();
    formData.append("file", file);
    return request({
      url: `${STORE_BASE_URL}/import`,
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },
  /** 下载资产导入模板 */
  downloadTemplate() {
    return request({
      url: `${STORE_BASE_URL}/template`,
      method: "get",
      responseType: "blob",
    });
  },

  copyByIds(guids: string) {
    return request({
      url: `${STORE_BASE_URL}/copy/${guids}`,
      method: "post",
    });
  },

  updateKjpzhByIds(guids: string, data: any) {
    return request({
      url: `${STORE_BASE_URL}/update-kjpzh/${guids}`,
      method: "post",
      data: data,
    });
  },
};

export default storeAPI;

/**
 * 入库差异对象
 */

export interface ZcinfoDifferentForm {
  /** 唯一标识符 */
  guid?: string;

  /** 品牌 */
  pp?: string;

  /** 规格型号 */
  ggxh?: string;

  /** 资产类型编号（必填） */
  zclxbh: string;

  /** 车辆用途：0领导用车、1公务用车、2专业用车、3生活用车、4接待用车、5其他用车 */
  clyt?: string;

  /** 车牌号 */
  cph?: string;

  /** 车架号 */
  cjh?: string;

  /** 编制情况：0财政在编、1非财政在编 */
  bzqk?: string;

  /** 排气量（升） */
  pql?: number;

  /** 车辆产地：0国产(含组装)、1进口 */
  clcd?: string;

  /** 发证时间 */
  fzsj?: string;

  /** 车辆行驶证所有人 */
  clxszsyr?: string;

  /** 权属证号 */
  qszh?: string;

  /** 发动机号码 */
  fdjhm?: string;

  /** 车身颜色 */
  csys?: string;

  /** 产权形式 分类集 */
  cqxs?: string;

  /** 建筑结构：0砖混、1钢混、2砖木、3其它 */
  jzjg?: string;

  /** 房屋总面积 */
  fwzmj?: number;

  /** 出租出借面积 */
  czcjmj?: number;

  /** 自用面积 */
  zymj?: number;

  /** 损毁待报废面积 */
  dbfmj?: number;

  /** 闲置面积 */
  xzmj?: number;

  /** 其中危房面积 */
  wfmj?: number;

  /** 其他面积 */
  qtmj?: number;

  /** 产权单位 */
  cqdw?: string;

  zldd?: string;

  ycqqk?: string;

  /** 无产权情况：0无偿占用、1租用、2临时规划许可 */
  wcqqk?: string;

  /** 产权形式：0有产权、1无产权 */
  /** 批准单位 */
  pzdw?: string;

  /** 批准文号 */
  pzwh?: string;

  /** 法人代表 */
  frdb?: string;

  /** 法人身份证 */
  frsfz?: string;

  /** 实收资本 */
  sszb?: number;

  /** 批准日期 */
  pzrq?: string;

  /** 注册资产，万元 */
  zczb?: number;

  /** 实际出资金额,万元 */
  sjczje?: number;

  /** 联系电话 */
  lxdh?: string;

  /** 开始时间 */
  kssj?: string;

  /** 截止时间 */
  jzsj?: string;

  /** 持股比例 */
  cgbl?: string;

  /** 是否实际控制：1是 0否 */
  sfsjkz?: string | boolean;

  /** 登记机关 */
  djjg?: string;

  /** 统一社会信用代码 */
  tydm?: string;

  /** 单位类型 xcode.name */
  dwlx?: string;

  /** 配资说明 */
  pzsm?: string;

  /** 组织形式，xcode.name */
  zzxs?: string;

  /** 经营范围 */
  jyfw?: string;

  /** 企业级别 */
  qyjb?: string;

  /** 是否上市：1是 0否 */
  sfss?: string | boolean;

  /** 土地总面积（m2） */
  tdzmj?: number;

  /** 土地证载明面积（m2） */
  tdzzmmj?: number;

  /** 出借面积（m2） */
  cjmj?: number;

  /** 建筑面积（m2） */
  jzmj?: number;

  /** 土地证号 */
  tdzh?: string;

  /** 土地等级 */
  tddj?: string;

  /** 土地图号 */
  tdth?: string;

  /** 所有权人 */
  syqr?: string;

  /** 权属证明 */
  qszm?: string;

  /** 权属证号 */
  qsxz?: string;

  /** 经纬度 */
  jwd?: string;

  /** 采购申请人 */
  cgsqr?: string;

  /** 生产厂家 */
  sccj?: string;

  /** 采购申请人手机 */
  cgsqrsj?: string;

  /** 采购申请时间 */
  cgsqsj?: string;

  /** 出厂日期 */
  ccrq?: string;

  /** 备用4,供货商负责人 */
  by4?: string;

  /** 备用5 */
  by5?: string;

  /** 出厂号 */
  chuchanghao?: string;

  /** 校区 */
  xiaoqu?: string;

  /** 英文名称 */
  ywmc?: string;

  /** 主要功能 */
  zygn?: string;

  /** 主要技术指标 */
  zyjszb?: string;

  /** 追溯码 采购申请单继承 */
  tracecode?: string;

  /** 物品编码 必填项 */
  cgwpbm?: string;

  /** 采购组织形式：0政府集中采购、1部门集中采购、2分散采购、3其他 */
  cgzzxs?: string;

  xmfzr?: string;

  /** 申购单位编号 分类集格式：学校组织机构编号 */
  sgdwbh?: string;

  /** 合同编号：从采购系统引入 */
  htbh?: string;

  /** 合同名称：从采购系统引入 */
  htmc?: string;

  /** 合同金额：从采购系统引入 */
  htje?: number;

  /** 合同日期：从采购系统引入 */
  htrq?: string;

  /** 注册登记机关 */
  zcdjjg?: string;

  /** 注册登记时间 */
  zcdjsj?: string;

  /** 专利号 */
  zlh?: string;

  /** 发明人 */
  fmr?: string;

  /** 知识产权类型：发明专利、实用专利、科技成果、软件著作权(作废) */
  cqlx?: string;

  /** 财政资产分类 原始数据,用于统计 */
  czcode1?: string;

  /** 资产类型编号 ,原始数据,用于统计 */
  zclxbh1?: string;

  /** 教育资产分类 ,原始数据,用于统计 */
  jycode1?: string;

  syfx?: string;

  gysmc?: string;

  by3?: string;
}

export interface CzCodeVO {
  /** 子部门 */
  children?: CzCodeVO[];
  /** 序号 */
  xh?: number;
  /** 统一编码 */
  guid?: string;
  /** 父部门ID */
  parentXh?: number;
  /** 资产分类名称 */
  xcode?: string;
  /** 资产分类编号 */
  xcodename?: string;
  /** 财政分类编号 */
  ycode?: string;
  /** 财政分类名称 */
  ycodename?: string;
  /** 教育分类编号 */
  zcode?: string;
  /** 教育分类名称 */
  zcodename?: string;
}

export interface CzCodeForm {
  /** 序号 */
  xh?: number;
  /** 统一编码 */
  guid?: string;
  /** 财政分类编号 */
  ycode?: string;
  /** 财政分类名称 */
  ycodename?: string;
  /** 教育分类编号 */
  zcode?: string;
  /** 教育分类名称 */
  zcodename?: string;
  /** 资产分类编号 */
  xcodename?: string;
  /** 资产分类名称 */
  xcode?: string;
}

/** 财政分类表表单对象 */
export interface ZcFlbForm {
  /** 序号 */
  xh?: number;
  /** 统一编码 */
  guid?: string;
  /** 资产分类编码（内部编码） */
  xcode?: string;
  /** 资产分类名称 */
  xcodename?: string;
  /** 是否叶节点，1表示是 */
  codelevel?: string;
  /** 财政资产分类编码 */
  ycode?: string;
  /** 财政分类名称 */
  ycodename?: string;
  /** 教育资产分类编码 */
  zcode?: string;
  zcodename?: string;
  /** 资产分类的特殊对应,必须大于等于4位 */
  codetable?: string;
  /** 链接地址 */
  linkaddr?: string;
  /** 使用次数 */
  gzms?: string;
  synx?: number;
  /** 排序号 */
  sort?: number;
  /** 编码状态 */
  codestate?: string;
  /** 备注 */
  notes?: string;
  /** 父级序号 */
  parentxh?: number;
}

/** 财政分类表表单对象 */
export interface CzQuery {
  /** 资产分类名称 */
  xcode?: string;
  xcodename?: string;
  /** 财政资产分类编码 */
  ycode?: string;
  ycodename?: string;
  /** 教育资产分类编码 */
  zcode?: string;
  zcodename?: string;
}
