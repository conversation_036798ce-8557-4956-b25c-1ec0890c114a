import request from "@/utils/request";

const ZXCGCGBD_BASE_URL = "/api/v1/zxcgCgbds";

const ZxcgCgbdAPI = {
  /** 获取采购标段分页数据 */
  getPage(queryParams?: ZxcgCgbdPageQuery) {
    return request<any, PageResult<ZxcgCgbdPageVO[]>>({
      url: `${ZXCGCGBD_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取采购标段表单数据
   *
   * @param id ZxcgCgbdID
   * @returns ZxcgCgbd表单数据
   */
  getFormData(guid: string) {
    return request<any, ZxcgCgbdForm>({
      url: `${ZXCGCGBD_BASE_URL}/${guid}/form`,
      method: "get",
    });
  },

  /** 添加采购标段*/
  save(data: ZxcgCgbdForm) {
    return request({
      url: `${ZXCGCGBD_BASE_URL}/save`,
      method: "post",
      data: data,
    });
  },
  submit(guid: string) {
    return request({
      url: `${ZXCGCGBD_BASE_URL}/submit/${guid}`,
      method: "post",
    });
  },
  /**
   * 更新采购标段
   *
   * @param id ZxcgCgbdID
   * @param data ZxcgCgbd表单数据
   */
  update(id: number, data: ZxcgCgbdForm) {
    return request({
      url: `${ZXCGCGBD_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 批量删除采购标段，多个以英文逗号(,)分割
   *
   * @param ids 采购标段ID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${ZXCGCGBD_BASE_URL}/${ids}`,
      method: "delete",
    });
  },
};

export default ZxcgCgbdAPI;

/** 采购标段分页查询参数 */
export interface ZxcgCgbdPageQuery extends PageQuery {
  /** 文件guid */
  wjid?: number;
  /** 标段编号 */
  bdbh?: string;
  /** 标段名称 */
  bdmc?: string;
  /** 页面类型 */
  pagetype?: string;
}

/** 采购标段表单对象 */
export interface ZxcgCgbdForm {
  /** 采购标段ID|hidden */
  bdid?: number;
  /** guid|hidden */
  guid?: string;
  /** 标段编号 */
  bdbh?: string;
  /** 标段名称 */
  bdmc?: string;
  /** 采购文件ID|hidden */
  wjid?: number;
  /** 采购方式 */
  cgfs?: string;
  cgfsname?: string;
  /** 标段内容 */
  bdnr?: string;
  /** 预算金额 */
  ysjf?: number;
  /** 保证金 */
  bzj?: number;
  /** 备注 */
  notes?: string;
  /** 标段状态：1正常、2终止、3废标|hidden */
  bdzt?: string;
  /** 执行人 */
  zxr?: string;
  /** 中标供应商ID-1|hidden */
  gysid?: number;
  /** 供应商机构编码 */
  jgdm?: string;
  /** 中标供应商名称 */
  zbgysmc?: string;
  /** 供应商联系人 */
  gyslxr?: string;
  /** 联系电话 */
  lxdh?: string;
  /** 中标价 */
  zbj?: number;
  /** 中标时间 */
  zbsj?: Date;
  /** 商务满分 */
  swmf?: number;
  /** 技术满分 */
  jsmf?: number;
  /** 资信满分 */
  zxmf?: number;
  /** 基准价 */
  jzj?: number;
  /** 商务评分方法 */
  swff?: string;
  /** 技术评分方法 */
  jsff?: string;
  /** 评分方法 */
  pfff?: string;
  /** 基准价评分方法 */
  jzff?: string;
  /** 标段明细IDS */
  mxids?: string;
  /** 废标标段生成的新文件id */
  nid?: number;
  /** 评审小组成员 */
  psxzcy?: string;
  /** 负责人工号 */
  fzrbm?: string;
  /** 申请部门 */
  sqbm?: string;
  /** 主管部门 */
  zgbm?: string;
  /** 申请人 */
  sqr?: string;
  /** 节点编码|hidden */
  netcode?: string;
  /** 节点状态|hidden */
  sjzt?: string;
  /** 下一审批人|hidden */
  nexter?: string;
  /** 下一审批部门|hidden */
  nextbm?: string;
  /** 委托ID */
  by1?: string;
  /** 2临时经费 */
  by2?: string;
  /** 是否开标 1 已开标  */
  by3?: string;
  /**  0 项目  1电子卖场 2自行采购 */
  by4?: string;
  /** 是否需要签订合同 */
  sfxyht?: string;
  /** 部门负责人 */
  bmfzr?: string;
  djr?: string;
  djbm?: string;
  djsj?: Date;
  /** 最高限价 */
  zgxj?: string;
  /** 合同履约期限 */
  lyqx?: string;
  /** 资格要求 */
  zgyq?: string;
  /** 执行状态用于判断是进行一般流程，简易流程，网超流程，询价，竞价 */
  zxbj?: string;
  /** 判断是政采云还是校采通：0校采通，1政采云 */
  zxfs?: string;
  /** 中医药的有效供应商数量 */
  by6?: string;
  /** 中医药的采购情况通报 */
  by7?: string;
  /** 中医药的采购情况通报编号，4位年份+3位流水号 */
  by8?: string;
  /** 中医药的采购情况发送给谁的OA */
  by9?: string;
  by10?: string;
  /** 资金节约率 */
  zjjyl?: string;
  /** 有效供应商：供应商名用中文顿号间隔 */
  yxgys?: string;
  /** 供应商类型：¨大型企业 ¨中型企业 ¨小型企业 ¨微型企业 */
  lx?: string;
  /** 谈判或磋商内容 */
  tpcsnr?: string;
  /** 采购情况是否已发送 1是  */
  qkfszt?: string;
  /** 询价小组成员，多人姓名用逗号隔开 */
  xjxzcy?: string;
  /** 是否登记合同 1是 */
  sfdjht?: string;
  /** 是否冻结 */
  sfdj?: number;
  /*确认书编号 */
  qrsbh?: string;
  /*银行账号*/
  yhzh?: string;
  /*开户银行 */
  khyh?: string;
  /** 收款人户名 */
  skrhm?: string;
  /** 联行号 */
  lhh?: string;
  /** 联系人邮箱 */
  lxremail?: string;
}

/** 采购标段分页对象 */
export interface ZxcgCgbdPageVO {
  /** 采购标段ID|hidden */
  bdid?: number;
  /** guid|hidden */
  guid?: string;
  /** 标段编号 */
  bdbh?: string;
  /** 标段名称 */
  bdmc?: string;
  /** 采购文件ID|hidden */
  wjid?: number;
  /** 采购方式 */
  cgfs?: string;
  cgfsname?: string;
  /** 标段内容 */
  bdnr?: string;
  /** 预算金额 */
  ysjf?: number;
  /** 保证金 */
  bzj?: number;
  mxcount?: number;
  /** 备注 */
  notes?: string;
  /** 标段状态：1正常、2终止、3废标|hidden */
  bdzt?: string;
  /** 执行人 */
  zxr?: string;
  /** 中标供应商ID-1|hidden */
  gysid?: number;
  /** 供应商机构编码 */
  jgdm?: string;
  /** 中标供应商名称 */
  zbgysmc?: string;
  /** 供应商联系人 */
  gyslxr?: string;
  /** 联系电话 */
  lxdh?: string;
  /** 中标价 */
  zbj?: number;
  /** 中标时间 */
  zbsj?: Date;
  /** 商务满分 */
  swmf?: number;
  /** 技术满分 */
  jsmf?: number;
  /** 资信满分 */
  zxmf?: number;
  /** 基准价 */
  jzj?: number;
  /** 商务评分方法 */
  swff?: string;
  /** 技术评分方法 */
  jsff?: string;
  /** 评分方法 */
  pfff?: string;
  /** 基准价评分方法 */
  jzff?: string;
  /** 标段明细IDS */
  mxids?: string;
  /** 废标标段生成的新文件id */
  nid?: number;
  /** 评审小组成员 */
  psxzcy?: string;
  /** 负责人工号 */
  fzrbm?: string;
  /** 申请部门 */
  sqbm?: string;
  /** 主管部门 */
  zgbm?: string;
  /** 申请人 */
  sqr?: string;
  /** 节点编码|hidden */
  netcode?: string;
  netcodename?: string;
  /** 节点状态|hidden */
  sjzt?: string;
  /** 下一审批人|hidden */
  nexter?: string;
  /** 下一审批部门|hidden */
  nextbm?: string;
  /** 委托ID */
  by1?: string;
  /** 2临时经费 */
  by2?: string;
  /** 是否开标 1 已开标  */
  by3?: string;
  /**  0 项目  1电子卖场 2自行采购 */
  by4?: string;
  /** 是否需要签订合同 */
  sfxyht?: string;
  /** 部门负责人 */
  bmfzr?: string;
  djr?: string;
  djbm?: string;
  djsj?: Date;
  /** 最高限价 */
  zgxj?: string;
  /** 合同履约期限 */
  lyqx?: string;
  /** 资格要求 */
  zgyq?: string;
  /** 执行状态用于判断是进行一般流程，简易流程，网超流程，询价，竞价 */
  zxbj?: string;
  /** 判断是政采云还是校采通：0校采通，1政采云 */
  zxfs?: string;
  /** 中医药的有效供应商数量 */
  by6?: string;
  /** 中医药的采购情况通报 */
  by7?: string;
  /** 中医药的采购情况通报编号，4位年份+3位流水号 */
  by8?: string;
  /** 中医药的采购情况发送给谁的OA */
  by9?: string;
  by10?: string;
  /** 资金节约率 */
  zjjyl?: string;
  /** 有效供应商：供应商名用中文顿号间隔 */
  yxgys?: string;
  /** 供应商类型：¨大型企业 ¨中型企业 ¨小型企业 ¨微型企业 */
  lx?: string;
  /** 谈判或磋商内容 */
  tpcsnr?: string;
  /** 采购情况是否已发送 1是  */
  qkfszt?: string;
  /** 询价小组成员，多人姓名用逗号隔开 */
  xjxzcy?: string;
  /** 是否登记合同 1是 */
  sfdjht?: string;
  /** 是否冻结 */
  sfdj?: number;
}
