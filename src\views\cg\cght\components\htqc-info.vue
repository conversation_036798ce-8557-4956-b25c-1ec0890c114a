<template>
  <Title name="项目信息">
    <div>
      <el-button type="danger" @click="handleNext()">提交</el-button>
    </div>
  </Title>

  <el-form
    ref="dataFormRef"
    :model="formData"
    :rules="rules"
    label-width="80px"
    :inline="false"
    :disabled="!editable"
  >
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="合同编号" prop="htbh">
          <span>{{ formData.htbh }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="合同名称" prop="htmc">
          <el-input v-if="false" v-model="formData.guid" />
          <el-input v-model="formData.htmc" placeholder="合同名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="甲方联系人" prop="jflxr">
          <el-input v-model="formData.jflxr" placeholder="甲方联系人" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="甲方联系方式" prop="jflxfs">
          <el-input v-model="formData.jflxfs" placeholder="甲方联系方式" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="合同金额" prop="htje">
          <span>{{ formData.htje }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="乙方联系人" prop="yflxr">
          <el-input v-model="formData.yflxr" placeholder="乙方联系人" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="乙方联系方式" prop="yflxfs">
          <el-input v-model="formData.yflxfs" placeholder="乙方联系方式" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注" prop="notes">
          <el-input v-model="formData.notes" placeholder="备注" type="textarea" :rows="2" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <FileUpload v-if="editable" ref="fileRef" :key="props.guid" :guid="props.guid" code="021539" />
</template>

<script setup lang="ts">
import ZxcgcghtAPI, { ZxcgCghtForm } from "@/api/cg/cght";
import FileUpload from "@/components/Upload/FileUpload.vue"; // 导入 FileUpload 组件

const props = defineProps({
  //guid
  guid: {
    type: String,
    required: true,
  },
  //是否可编辑
  editable: {
    type: Boolean,
    required: true,
  },
});
const dataFormRef = ref(ElForm);
const loading = ref(false);
// 采购合同表单数据
const formData = reactive<ZxcgCghtForm>({});
const emits = defineEmits(["nowstep"]);

// 采购合同表单校验规则
const rules = reactive({});
/** 初始化表单信息 */
function handleFormQuery() {
  ZxcgcghtAPI.getFormData(props.guid)
    .then((res) => {
      Object.assign(formData, res);
      console.log("formData", formData);
    })
    .finally(() => {});
}

const handleSave = async () => {
  dataFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      await ZxcgcghtAPI.add(formData)
        .then(async (res) => {
          ElMessage.success("保存成功");
        })
        .catch((error) => {
          ElMessage.error("操作失败，失败原因：" + error.message);
        })
        .finally(() => {});
    }
  });
};

const handleNext = async () => {
  dataFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      await ZxcgcghtAPI.add(formData)
        .then(async (res) => {
          ElMessage.success("保存成功");
          emits("nowstep", 2);
        })
        .catch((error) => {
          ElMessage.error("操作失败，失败原因：" + error.message);
        })
        .finally(() => {});
    }
  });
};

onMounted(() => {
  handleFormQuery();
});
</script>
<style scoped></style>
