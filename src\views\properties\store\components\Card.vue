<!-- 入库单 -->
<template>
  <div>
    <el-card>
      <template #header>
        <!-- ********************** 页面批量操作按钮 ********************** -->
        <Title name="卡片信息">
          <div v-if="editable">
            <el-button plain type="primary" icon="plus" @click="handleAddCard">新增卡片</el-button>
            <el-button
              :disabled="selectedVals.length === 0"
              plain
              type="danger"
              icon="Refresh"
              @click="handleBatchUpdate"
            >
              批量修改
            </el-button>
            <el-button
              :disabled="selectedVals.length === 0"
              plain
              type="danger"
              icon="Switch"
              @click="handleSplitCard"
            >
              拆分卡片
            </el-button>
            <el-button
              :disabled="selectedVals.length === 0"
              plain
              type="danger"
              icon="Delete"
              @click="handleDelete"
            >
              删除卡片
            </el-button>
            <el-button plain type="danger" icon="DeleteFilled" @click="handleDeleteAll">
              全部删除
            </el-button>
          </div>
        </Title>
      </template>
      <!-- ********************** 列表内容 ********************** -->
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
        :disabled="!props.editable"
      >
        <el-table-column type="selection" width="50" align="center" fixed />
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="资产编号" prop="zcbh" width="120" fixed />
        <el-table-column label="资产名称" prop="zcmc" width="200" fixed />
        <el-table-column label="原值(元)" prop="je" width="100" />
        <el-table-column label="数量" prop="sl" min-width="100">
          <template #default="scope">
            <el-input
              v-model="scope.row.sl"
              :disabled="!props.editable"
              @blur="handleUpdateCard(scope.row.id, { sl: scope.row.sl })"
            />
          </template>
        </el-table-column>
        <el-table-column label="保管员" prop="syr" width="180">
          <template #default="scope">
            <el-select
              v-model="scope.row.syr"
              :disabled="!props.editable"
              filterable
              placeholder="请选择保管员"
              @change="
                (val) => {
                  handleUpdateCard(scope.row.id, { syr: val });
                }
              "
            >
              <el-option
                v-for="item in userOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="使用方向" prop="syfx" width="150">
          <template #default="scope">
            <el-select
              v-model="scope.row.syfx"
              :disabled="!props.editable"
              filterable
              placeholder="请选择使用方向"
              @change="(val) => handleUpdateCard(scope.row.id, { syfx: val })"
            >
              <el-option
                v-for="item in directionOptions"
                :key="item.xcode"
                :label="item.name"
                :value="item.xcode"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="使用/管理部门" prop="sybm" width="200">
          <template #default="scope">
            <el-tree-select
              v-model="scope.row.sybm"
              :disabled="!props.editable"
              filterable
              check-strictly
              :render-after-expand="false"
              placeholder="请选择部门"
              :data="deptOptions"
              @change="
                (val) => {
                  handleUpdateCard(scope.row.id, { sybm: val });
                }
              "
            />
          </template>
        </el-table-column>
        <el-table-column label="设备用途" prop="sbyt" width="150" v-if="isEquipment">
          <template #default="scope">
            <el-select
              v-model="scope.row.sbyt"
              :disabled="!props.editable"
              filterable
              placeholder="请选择设备用途"
              @change="(val) => handleUpdateCard(scope.row.id, { sbyt: val })"
            >
              <el-option
                v-for="item in equipmentOptions"
                :key="item.xcode"
                :label="item.name"
                :value="item.xcode"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="存放地点" prop="cfdd" width="200">
          <template #default="scope">
            <div>
              <el-cascader
                v-model="scope.row.cfdd"
                class="cascader"
                style="width: 100%"
                :options="locationOptions"
                :props="cascaderProps"
                :disabled="!props.editable"
                :placeholder="scope.row.cfddname || '请选择存放地点'"
                filterable
                clearable
                @change="
                  (val) => {
                    handleUpdateCard(scope.row.id, {
                      cfdd: val,
                    });
                  }
                "
              />
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 10"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <!-- 批量修改 -->
    <el-drawer v-model="batchUpdateVisible" append-to-body size="40%">
      <BatchUpdate
        :ids="selectedVals.join(',')"
        :handleQuery="handleQuery"
        @closebatch="colseBatchUpdate()"
      />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import cardAPI, { batchUpdateParam } from "@/api/properties/card";
import BatchUpdate from "@/views/properties/book/components/BatchUpdate.vue";
import UserAPI from "@/api/system/user";
import DeptAPI from "@/api/system/dept";
import SysAPI from "@/api/system/sys";
import SysAcodeApi, { SysAcodePageQuery } from "@/api/system/sys-acode";
import type { CascaderProps, CascaderOption } from "element-plus";
// 选项数据
const userOptions = ref<any[]>([]);
const deptOptions = ref<any[]>([]);
const directionOptions = ref<any[]>([]);
const equipmentOptions = ref<any[]>([]);

const locationOptions = ref<CascaderOption[]>([]);

const cascaderProps = reactive<CascaderProps>({
  lazy: true,
  emitPath: false,
  lazyLoad(node, resolve) {
    const params = reactive<SysAcodePageQuery>({
      preCode: "0101",
      pageNum: 1,
      pageSize: 100,
    });

    const { value } = node;
    //第一次加载判断是否是根节点
    if (node.root) {
      params.preCode = "0101";
    } else {
      params.preCode = value as string;
    }

    SysAcodeApi.getAcodelist(params).then((res) => {
      const nodes: CascaderOption[] = res.list.map((item: any) => ({
        value: item.xcode,
        label: item.name,
        leaf: item.xcode.length === 12,
      }));
      resolve(nodes);
    });
  },
});

// 加载选项数据
const loadOptions = () => {
  try {
    console.log("开始加载选项数据...");

    // 加载保管员选项
    if (userOptions.value.length === 0) {
      UserAPI.getOptionsByCode().then((res) => {
        userOptions.value = res;
        console.log("保管员选项加载完成:", userOptions.value.length);
      });
    }

    if (deptOptions.value.length === 0) {
      // 加载部门选项
      DeptAPI.getOptionsCode().then((res) => {
        deptOptions.value = res;
        console.log("部门选项加载完成:", deptOptions.value.length);
      });
    }

    if (directionOptions.value.length === 0) {
      // 加载使用方向选项
      SysAPI.getXcodeListByCode("020204").then((res) => {
        directionOptions.value = res;
        console.log("使用方向选项加载完成:", directionOptions.value.length);
      });
    }

    if (equipmentOptions.value.length === 0) {
      // 加载使用方向选项
      SysAPI.getXcodeListByCode("02024C").then((res) => {
        equipmentOptions.value = res;
        console.log("设备用途选项加载完成:", equipmentOptions.value.length);
      });
    }

    console.log("所有选项数据加载完成");
  } catch (error) {
    console.error("加载选项数据失败:", error);
    ElMessage.error("加载选项数据失败");
  }
};

const props = defineProps({
  editable: {
    type: Boolean,
    required: true,
  },
  guid: {
    type: String,
    required: true,
  },
  isEquipment: {
    type: Boolean,
    required: true,
  },
});
// 获取路由参数,view/add/balance/check
const loading = ref(false);
const selectedVals = ref<number[]>([]);
const selectedGuids = ref<string[]>([]);

const total = ref(0);
// 列表数据
const pageData = ref<any[]>([]);
const queryParams = reactive<any>({
  pageNum: 1,
  pageSize: 10,
  guid: "",
}); //主查询方法
function handleQuery() {
  loading.value = true;
  queryParams.guid = props.guid;
  console.log(queryParams);
  cardAPI
    .getPageByStore(queryParams)
    .then((res: any) => {
      pageData.value = res.list;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
// 选中项发生变化
function handleSelectionChange(selection: any[]) {
  selectedVals.value = selection.map((item: any) => item.id);
  selectedGuids.value = selection.map((item: any) => item.guid);
}
/** 删除 */
function handleDelete() {
  const ids = selectedVals.value.join(",");
  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      cardAPI
        .delete(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}
/** 删除 */
function handleDeleteAll() {
  ElMessageBox.confirm("确认清空卡片数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      cardAPI
        .deleteByStore(props.guid)
        .then(() => {
          ElMessage.success("删除成功");
          handleQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}
//批量更新方法
const batchUpdateVisible = ref(false);
const handleBatchUpdate = () => {
  if (selectedVals.value.length === 0) {
    ElMessage.warning("请选择要更新的卡片");
    return;
  }
  batchUpdateVisible.value = true;
};

const colseBatchUpdate = () => {
  batchUpdateVisible.value = false;
};
//更新卡片字段
const handleUpdateCard = (id: string, updateFields: batchUpdateParam) => {
  // 特殊处理存放地点相关字段
  if ("cfdd" in updateFields) {
    console.log("处理存放地点更新 - cfdd:", updateFields.cfdd);
  }

  // 确保所有字段都有有效值，但允许null值（用于清空字段）
  const validFields: batchUpdateParam = {};
  Object.entries(updateFields).forEach(([key, value]) => {
    if (value !== undefined) {
      // 允许null值，用于清空字段
      validFields[key as keyof batchUpdateParam] = value;
      console.log(`设置字段 ${key}:`, value);
    } else {
      console.warn(`跳过未定义的字段 ${key}`);
    }
  });

  if (Object.keys(validFields).length === 0) {
    console.warn("没有需要更新的字段");
    return;
  }

  const updateParam = reactive<batchUpdateParam>(validFields);
  console.log("最终更新参数:", updateParam);

  loading.value = true;
  cardAPI
    .batchUpdate(id, updateParam)
    .then(() => {
      console.log("更新成功 - 参数:", updateParam);
      ElMessage.success("更新成功");
      handleQuery();
    })
    .catch((error) => {
      console.error("更新失败:", error);
      ElMessage.error("更新失败: " + (error.message || "未知错误"));
    })
    .finally(() => {
      loading.value = false;
      console.log("更新操作完成");
    });
};
const handleAddCard = () => {
  ElMessageBox.confirm("确认新增一条数量为1的卡片?", "", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      cardAPI
        .addCard(props.guid)
        .then(() => {
          ElMessage.success("操作成功");
          handleQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("操作已取消");
    }
  );
};
const handleSplitCard = () => {
  ElMessageBox.confirm("确认将卡片数量拆分至1?", "", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      cardAPI
        .splitCard(selectedGuids.value.join(","))
        .then(() => {
          ElMessage.success("操作成功");
          handleQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("操作已取消");
    }
  );
};
//————————————————————————生命周期
onMounted(() => {
  console.log("组件已挂载");
  try {
    loadOptions();
    handleQuery();
  } catch (error) {
    console.error("组件挂载过程中出错:", error);
    ElMessage.error("初始化数据失败，请刷新页面重试");
  }
});
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 330px;
}
</style>
