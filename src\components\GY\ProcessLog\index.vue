<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="flex-x-between">
          <div class="flex-y-center">流程日志</div>
        </div>
      </template>

      <el-table v-loading="loading" stripe :data="flowList" highlight-current-row :border="true">
        <el-table-column label="序号" type="index" width="55" align="center" fixed>
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="资产编号" prop="zcbh" width="120" />
        <el-table-column label="资产名称" prop="zcmc" width="200" />
        <el-table-column label="金额(元)" prop="je" width="100" />
        <el-table-column label="数量" prop="sl" min-width="80" />
        <el-table-column label="资产状态" prop="locName" width="150">
          <template #default="scope">
            <span>{{ scope.row.locName === "" ? "正常" : scope.row.locName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="使用人" prop="syrname" width="150" />
        <el-table-column label="使用部门" prop="sybmname" width="200" />
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />

      <!-- <el-timeline>
        <el-timeline-item
          v-for="(item, index) in flowList"
          :key="index"
          center
          type="primary"
          :hollow="true"
        >
          <el-card shadow="hover">
            <span class="headerClass">{{ item.netcodename }}</span>
            <div style="float: right">
              {{ item.activename }}
            </div>
            <div>
              <span class="contentClass">{{ item.czrnamenick }}：{{ item.shyj }}</span>
            </div>
            <div>{{ item.czsj }}</div>
          </el-card>
        </el-timeline-item>
      </el-timeline> -->
    </el-card>
  </div>
</template>

<script setup lang="ts">
import WorkFlowAPI, { pageLcrzVo } from "@/api/system/workflow";
//根据guid获取节点信息
const props = defineProps({
  guid: {
    type: String,
    required: true,
  },
});
const total = ref(0);
const loading = ref(false);

const queryParams = reactive({
  guid: props.guid,
  pageNum: 0,
  pageSize: 10,
});

const flowList = ref<pageLcrzVo[]>([]);

const handleQuery = () => {
  WorkFlowAPI.getPageLcrz(queryParams).then((res) => {
    flowList.value = res.list;
  });
};

// 获取节点信息
onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.headerClass {
  font-weight: bold;
  font-size: 16px;
  line-height: 30px;
}
.contentClass {
  line-height: 40px;
}
</style>
