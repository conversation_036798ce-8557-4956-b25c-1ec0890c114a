<template>
  <div class="flow-chart-container" ref="containerRef">
    <div class="flow-chart" :style="{ width: chartWidth + 'px', height: chartHeight + 'px' }">
      <!-- SVG连接线 -->
      <svg class="flow-connections" :width="chartWidth" :height="chartHeight">
        <path
          v-for="connection in connections"
          :key="connection.id"
          :d="connection.path"
          :stroke="connection.color"
          :stroke-width="connection.width"
          :stroke-dasharray="connection.dashArray"
          fill="none"
          class="connection-path"
          :class="connection.statusClass"
        />
      </svg>

      <!-- 流程节点 -->
      <div
        v-for="node in layoutNodes"
        :key="node.netcode"
        class="flow-node"
        :class="getNodeClass(node)"
        :style="getNodeStyle(node)"
        @click="handleNodeClick(node)"
      >
        <div class="node-icon">
          <el-icon :size="20">
            <component :is="getNodeIcon(node)" />
          </el-icon>
        </div>
        <div class="node-content">
          <div class="node-title">{{ node.netname }}</div>
          <div class="node-subtitle">{{ node.nickname }}</div>
          <div v-if="node.time" class="node-time">
            {{ format(node.time, "MM-DD HH:mm") }}
          </div>
        </div>
        <div v-if="getNodeStatus(node) === 'current'" class="node-pulse"></div>
      </div>
    </div>

    <!-- 节点详情弹窗 -->
    <el-dialog v-model="dialogVisible" title="节点详情" width="500px">
      <div v-if="selectedNode">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="节点名称">{{ selectedNode.netname }}</el-descriptions-item>
          <el-descriptions-item label="审批人">{{ selectedNode.nickname }}</el-descriptions-item>
          <el-descriptions-item label="操作时间">
            {{ selectedNode.time ? format(selectedNode.time, "YYYY-MM-DD HH:mm:ss") : "待处理" }}
          </el-descriptions-item>
          <el-descriptions-item label="审批意见">
            {{ selectedNode.content || "无" }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(selectedNode)">
              {{ getStatusText(selectedNode) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, PropType } from "vue";
import { WorkFlowChartVO } from "@/api/system/workflow";
import { format } from "@/utils/day";
import { CircleCheck, Aim, Remove, Clock } from "@element-plus/icons-vue";

defineOptions({
  name: "FlowChart",
});

interface LayoutNode extends WorkFlowChartVO {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface Connection {
  id: string;
  path: string;
  from: string;
  to: string;
  color: string;
  width: number;
  dashArray?: string;
  type: "serial" | "parallel" | "merge";
  statusClass: string;
  status: "completed" | "current" | "pending";
}

const props = defineProps({
  steps: {
    type: Array as PropType<WorkFlowChartVO[]>,
    required: true,
  },
  currentNetcode: {
    type: String,
    required: true,
  },
});

const containerRef = ref<HTMLElement>();
const dialogVisible = ref(false);
const selectedNode = ref<WorkFlowChartVO | null>(null);

// 图表尺寸
const chartWidth = ref(1200);
const chartHeight = ref(400);

// 节点布局配置
const NODE_WIDTH = 160;
const NODE_HEIGHT = 80;
const LEVEL_WIDTH = 220; // 改为水平间距
const BRANCH_HEIGHT = 120; // 改为垂直间距

// 计算节点布局
const layoutNodes = computed<LayoutNode[]>(() => {
  const nodes = [...props.steps];

  // 如果没有层级信息，按顺序排列（兼容旧数据）
  if (!nodes.some((node) => node.level !== undefined)) {
    return nodes.map((node, index) => ({
      ...node,
      x: index * LEVEL_WIDTH + 80, // 增加左边距
      y: 80, // 增加顶部边距
      width: NODE_WIDTH,
      height: NODE_HEIGHT,
    }));
  }

  // 按层级和分支布局（从左到右）
  const layoutMap = new Map<string, LayoutNode>();
  const levelGroups = new Map<number, WorkFlowChartVO[]>();

  // 按层级分组
  nodes.forEach((node) => {
    const level = node.level || 0;
    if (!levelGroups.has(level)) {
      levelGroups.set(level, []);
    }
    levelGroups.get(level)!.push(node);
  });

  // 计算每层的布局（水平布局）
  levelGroups.forEach((levelNodes, level) => {
    const x = level * LEVEL_WIDTH + 80; // 水平位置基于层级，增加左边距

    // 按分支索引排序
    levelNodes.sort((a, b) => (a.branchIndex || 0) - (b.branchIndex || 0));

    const totalHeight = levelNodes.length * BRANCH_HEIGHT;
    // 确保顶部有足够的边距，避免节点被遮住
    const minTopMargin = 80;
    const startY = Math.max(minTopMargin, (chartHeight.value - totalHeight) / 2);

    levelNodes.forEach((node, index) => {
      const y = startY + index * BRANCH_HEIGHT;
      layoutMap.set(node.netcode!, {
        ...node,
        x,
        y,
        width: NODE_WIDTH,
        height: NODE_HEIGHT,
      });
    });
  });

  // 更新图表尺寸
  const maxLevel = Math.max(...Array.from(levelGroups.keys()));
  chartWidth.value = Math.max(1200, (maxLevel + 1) * LEVEL_WIDTH + 240); // 增加右边距

  // 计算所需的最小高度
  const maxNodesInLevel = Math.max(
    ...Array.from(levelGroups.values()).map((nodes) => nodes.length)
  );
  const requiredHeight = maxNodesInLevel * BRANCH_HEIGHT + 160; // 160为上下边距
  chartHeight.value = Math.max(400, requiredHeight);

  return Array.from(layoutMap.values());
});

// 计算连接线
const connections = computed<Connection[]>(() => {
  const conns: Connection[] = [];
  const nodeMap = new Map<string, LayoutNode>();
  const sortedNodes = [...layoutNodes.value].sort((a, b) => a.order! - b.order!);

  layoutNodes.value.forEach((node) => {
    nodeMap.set(node.netcode!, node);
  });

  // 按层级分组节点
  const levelGroups = new Map<number, LayoutNode[]>();
  sortedNodes.forEach((node) => {
    const level = node.level || 0;
    if (!levelGroups.has(level)) {
      levelGroups.set(level, []);
    }
    levelGroups.get(level)!.push(node);
  });

  // 为每个节点创建连接
  sortedNodes.forEach((currentNode) => {
    const currentLevel = currentNode.level || 0;
    const nextLevel = currentLevel + 1;

    if (levelGroups.has(nextLevel)) {
      const nextLevelNodes = levelGroups.get(nextLevel)!;

      if (currentNode.flowType === "serial" || !currentNode.flowType) {
        // 串行流程：连接到下一层级的节点
        if (nextLevelNodes.length === 1) {
          // 只有一个下级节点，直接连接
          conns.push(createConnection(currentNode, nextLevelNodes[0]));
        } else if (nextLevelNodes.length > 1) {
          // 多个下级节点（并行分支），连接到所有节点
          nextLevelNodes.forEach((nextNode) => {
            conns.push(createConnection(currentNode, nextNode));
          });
        }
      } else if (currentNode.flowType === "parallel") {
        // 并行流程：连接到下一层级的所有并行节点
        nextLevelNodes.forEach((nextNode) => {
          if (nextNode.flowType === "parallel") {
            conns.push(createConnection(currentNode, nextNode));
          }
        });
      }
    }
  });

  // 处理并行节点汇聚到第一个serial节点的连接
  levelGroups.forEach((nodes, level) => {
    const parallelNodes = nodes.filter((node) => node.flowType === "parallel");
    if (parallelNodes.length > 0) {
      // 查找后续层级的merge节点
      for (let nextLevel = level + 1; nextLevel <= Math.max(...levelGroups.keys()); nextLevel++) {
        const nextLevelNodes = levelGroups.get(nextLevel) || [];
        const mergeNode = nextLevelNodes.find((node) => node.flowType === "serial");

        if (mergeNode) {
          // 所有并行节点都连接到merge节点
          parallelNodes.forEach((parallelNode) => {
            conns.push(createConnection(parallelNode, mergeNode));
          });
          break; // 找到第一个merge节点后停止
        }
      }
    }
  });

  return conns;
});

// 创建连接线路径
function createConnection(
  from: LayoutNode,
  to: LayoutNode,
  connectionType?: "serial" | "parallel" | "merge"
): Connection {
  const fromX = from.x + from.width;
  const fromY = from.y + from.height / 2;
  const toX = to.x;
  const toY = to.y + to.height / 2;

  const type = connectionType || getConnectionType(from, to);

  // 创建连接线路径
  let path: string;

  if (Math.abs(fromY - toY) < 10) {
    // 同一水平线上，使用直线
    path = `M ${fromX} ${fromY} L ${toX} ${toY}`;
  } else {
    // 不同水平线，根据连接类型选择路径
    if (type === "parallel" || type === "merge") {
      // 并行节点之间使用曲线连接
      const controlOffset = Math.min(50, Math.abs(toX - fromX) / 3);
      path = `M ${fromX} ${fromY} C ${fromX + controlOffset} ${fromY}, ${toX - controlOffset} ${toY}, ${toX} ${toY}`;
    } else {
      // 串行节点使用简单的折线
      const midX = fromX + (toX - fromX) * 0.6;
      path = `M ${fromX} ${fromY} L ${midX} ${fromY} L ${midX} ${toY} L ${toX} ${toY}`;
    }
  }

  // 简化状态计算
  const fromStatus = getNodeStatus(from);
  let color: string;
  let statusClass: string;
  let dashArray: string | undefined;

  // 简化状态判断
  if (fromStatus === "completed") {
    color = "#67c23a";
    statusClass = "connection-completed";
  } else if (fromStatus === "current" || fromStatus === "processing") {
    color = "#e6a23c";
    statusClass = "connection-current";
    dashArray = "6,6";
  } else {
    color = "#c0c4cc";
    statusClass = "connection-pending";
  }

  const width = type === "merge" ? 3 : 2;

  return {
    id: `${from.netcode}-${to.netcode}`,
    path,
    from: from.netcode!,
    to: to.netcode!,
    color,
    width,
    dashArray,
    type,
    statusClass,
    status:
      fromStatus === "completed"
        ? "completed"
        : fromStatus === "current" || fromStatus === "processing"
          ? "current"
          : "pending",
  };
}

// 获取连接类型
function getConnectionType(from: LayoutNode, to: LayoutNode): "serial" | "parallel" | "merge" {
  if (to.flowType === "merge") {
    return "merge";
  }
  if (from.flowType === "parallel" || to.flowType === "parallel") {
    return "parallel";
  }
  return "serial";
}

// 获取节点状态
function getNodeStatus(node: WorkFlowChartVO): "completed" | "current" | "pending" | "processing" {
  // 如果是当前节点
  if (node.netcode === props.currentNetcode) {
    return node.netstate === "1" ? "processing" : "current";
  }

  // 优先根据节点自身的状态来判断
  switch (node.netstate) {
    case "1":
      return "completed"; // 已完成
    case "0":
      return "pending"; // 未完成
    case "2":
    default:
      // 对于并行节点，需要特殊处理
      if (node.flowType === "parallel") {
        // 获取当前节点信息
        const currentNode = props.steps.find((step) => step.netcode === props.currentNetcode);
        if (currentNode && currentNode.level !== undefined && node.level !== undefined) {
          // 如果是同层级的并行节点，且当前层级已激活，则应该显示为待审状态
          if (currentNode.level === node.level && currentNode.flowType === "parallel") {
            return "current"; // 同层级并行节点都应该显示为待审状态
          }
          // 如果当前层级已经到达或超过该并行节点层级，则该节点可以执行
          if (currentNode.level >= node.level) {
            return "current"; // 可以审核的并行节点显示为待审状态
          }
        }
        return "pending"; // 还未到达的层级显示为pending
      }

      // 对于串行节点和merge节点，按照顺序逻辑
      const currentIndex = props.steps.findIndex((step) => step.netcode === props.currentNetcode);
      const nodeIndex = props.steps.findIndex((step) => step.netcode === node.netcode);

      if (nodeIndex < currentIndex) {
        return "completed";
      } else if (nodeIndex === currentIndex + 1) {
        return "current"; // 下一个串行节点显示为待审状态
      } else {
        return "pending";
      }
  }
}

// 获取节点样式类
function getNodeClass(node: WorkFlowChartVO): string {
  const status = getNodeStatus(node);
  return `node-${status}`;
}

// 获取节点样式
function getNodeStyle(node: LayoutNode): Record<string, string> {
  return {
    left: `${node.x}px`,
    top: `${node.y}px`,
    width: `${node.width}px`,
    height: `${node.height}px`,
  };
}

// 获取节点图标
function getNodeIcon(node: WorkFlowChartVO) {
  const status = getNodeStatus(node);
  switch (status) {
    case "completed":
      return CircleCheck;
    case "current":
      return Aim;
    case "processing":
      return Clock;
    default:
      return Remove;
  }
}

// 获取状态标签类型
function getStatusTagType(
  node: WorkFlowChartVO
): "success" | "warning" | "info" | "primary" | "danger" {
  const status = getNodeStatus(node);
  switch (status) {
    case "completed":
      return "success";
    case "current":
    case "processing":
      return "warning";
    default:
      return "info";
  }
}

// 获取状态文本
function getStatusText(node: WorkFlowChartVO): string {
  const status = getNodeStatus(node);
  switch (status) {
    case "completed":
      return "已完成";
    case "current":
      return "当前节点";
    case "processing":
      return "处理中";
    default:
      return "待处理";
  }
}

// 处理节点点击
function handleNodeClick(node: WorkFlowChartVO) {
  selectedNode.value = node;
  dialogVisible.value = true;
}

// 响应式调整
onMounted(() => {
  nextTick(() => {
    if (containerRef.value) {
      // 水平布局下，确保有足够的高度显示所有节点
      const containerHeight = containerRef.value.clientHeight - 40; // 减去padding
      chartHeight.value = Math.max(400, containerHeight);
    }
  });
});
</script>

<style lang="scss" scoped>
.flow-chart-container {
  width: 100%;
  height: 450px;
  overflow-x: auto;
  overflow-y: auto;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  background: var(--el-bg-color-page);
  padding: 0;
  box-sizing: border-box;
}

.flow-chart {
  position: relative;
  min-width: 100%;
  min-height: 100%;
  background:
    linear-gradient(90deg, transparent 24px, rgba(0, 0, 0, 0.04) 25px, transparent 26px),
    linear-gradient(transparent 24px, rgba(0, 0, 0, 0.04) 25px, transparent 26px);
  background-size: 25px 25px;
  padding: 20px;
  box-sizing: border-box;
}

.flow-connections {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;

  .connection-path {
    stroke-linecap: round;
    stroke-linejoin: round;

    // 已完成状态的连接线
    &.connection-completed {
      stroke: #67c23a !important;
    }

    // 当前进行中状态的连接线
    &.connection-current {
      stroke: #e6a23c !important;
      stroke-dasharray: 6, 6;
    }

    // 待处理状态的连接线
    &.connection-pending {
      stroke: #c0c4cc !important;
      opacity: 0.6;
    }
  }
}

.flow-node {
  position: absolute;
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .node-icon {
    flex-shrink: 0;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
  }

  .node-content {
    flex: 1;
    min-width: 0;

    .node-title {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .node-subtitle {
      font-size: 12px;
      color: var(--el-text-color-secondary);
      margin-bottom: 2px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .node-time {
      font-size: 11px;
      color: var(--el-text-color-placeholder);
    }
  }

  .node-pulse {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #f56c6c;
  }
}

// 节点状态样式
.node-completed {
  background: #f0f9ff;
  border: 2px solid #67c23a;
  color: #67c23a;

  .node-icon {
    background: #67c23a;
    color: white;
  }

  .node-title {
    color: #67c23a;
  }
}

.node-current {
  background: #fff7e6;
  border: 2px solid #e6a23c;
  color: #e6a23c;

  .node-icon {
    background: #e6a23c;
    color: white;
  }

  .node-title {
    color: #e6a23c;
  }
}

.node-processing {
  background: #fff2f0;
  border: 2px solid #f56c6c;
  color: #f56c6c;

  .node-icon {
    background: #f56c6c;
    color: white;
  }

  .node-title {
    color: #f56c6c;
  }
}

.node-pending {
  background: #f5f7fa;
  border: 2px solid #c0c4cc;
  color: #909399;

  .node-icon {
    background: #c0c4cc;
    color: white;
  }

  .node-title {
    color: #909399;
  }
}
</style>
