<template>
  <div>
    <el-card>
      <template #header>
        <div class="flex-x-between">
          <div class="flex-y-center">{{ props.name }}</div>
          <!-- <el-button type="primary" size="small" plain @click="listDialogVisible = true">
            更多
          </el-button> -->
        </div>
      </template>
      <el-scrollbar :height="props.height">
        <el-row>
          <el-col class="col" :span="6" v-for="(item, index) in pagedata.tableData">
            <div style="text-align: center; cursor: pointer" @click="router.push(item.urlstr)">
              <el-image style="width: 40px; height: 35px" :src="item.data" fit="fill" />
              <div>{{ item.name }}</div>
            </div>
          </el-col>
        </el-row>
      </el-scrollbar>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import img1 from "@/assets/gyIcons/basket.png";
import img2 from "@/assets/gyIcons/blueprint.png";
import img3 from "@/assets/gyIcons/callsettings.png";
import img4 from "@/assets/gyIcons/file-text.png";
import img5 from "@/assets/gyIcons/geometry.png";
import img6 from "@/assets/gyIcons/microscope.png";
import img7 from "@/assets/gyIcons/news.png";
import img8 from "@/assets/gyIcons/open-in-browser.png";
import img9 from "@/assets/gyIcons/stack.png";
import img10 from "@/assets/gyIcons/basket.png";
import { useRouter } from "vue-router";

// 在 setup 中
const router = useRouter();

//组件参数
const props = defineProps({
  name: {
    type: String,
    default: "快捷面板",
  },
  //展示高度，默认200
  height: {
    type: String,
    default: "300",
  },
});

const pagedata = reactive({
  tableData: [
    { name: "资产登记", data: img1, urlstr: "/store/add?type=add" },
    { name: "调拨申请", data: img2, urlstr: "/use/transfer/add?type=add" },
    { name: "处置申请", data: img3, urlstr: "/dispose/add" },
    { name: "台账管理", data: img4, urlstr: "/book/view" },
    { name: "资产盘点", data: img5, urlstr: "/inventory/doInv?type=add" } /*,
     { name: "功能F", data: img6, urlstr: "" },
    { name: "功能G", data: img7, urlstr: "" },
    { name: "功能H", data: img8, urlstr: "" },
    { name: "功能I", data: img9, urlstr: "" },*/,
  ],
});

onMounted(() => {});
</script>
<style lang="scss" scoped>
.col {
  height: 130px;
}
</style>
