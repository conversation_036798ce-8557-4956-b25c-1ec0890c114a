<template>
  <div class="approval-form approval-card">
    <!-- 审批表单内容 -->
    <el-form ref="approvalFormRef" :model="approvalForm" :rules="approvalRules" label-width="120px">
      <!-- 审批意见输入 -->
      <el-form-item label="审批意见：" prop="shyj">
        <el-input
          v-model="approvalForm.shyj"
          type="textarea"
          :rows="4"
          placeholder="请输入审批意见"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
      <!-- 审批结果选择 -->
      <el-form-item label="审批结果：" prop="actionType">
        <el-radio-group v-model="actionType" @change="handleActionChange">
          <el-radio value="approve" size="large" border>同意</el-radio>
          <el-radio value="reject" size="large" border>不同意</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 退回节点选择 - 仅在选择退回时显示 -->
      <el-form-item v-if="showBackNodeSelect" label="退回节点：" prop="afterId">
        <el-select v-model="approvalForm.afterId" placeholder="请选择退回节点" style="width: 100%">
          <el-option
            v-for="node in backNodes"
            :key="node.netcode"
            :label="node.nodemc + '(' + node.username + ')'"
            :value="node.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="showBackNodeSelect" label="返回方式：" prop="returnType">
        <el-radio-group v-model="approvalForm.returnType">
          <el-radio :value="0" size="large" border>逐级返回</el-radio>
          <el-radio :value="1" size="large" border>直接跳回</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, PropType, onMounted } from "vue";
import WorkFlowAPI from "@/api/system/workflow";
import { ElLoading, ElMessage, ElMessageBox, FormInstance } from "element-plus";

defineOptions({
  name: "CheckForm",
});
// 操作类型：approve-同意，reject-退回
const actionType = ref<"approve" | "reject">("approve");
// 是否显示退回节点选择
const showBackNodeSelect = ref(false);
// 处理审批结果变更
const handleActionChange = async (value: any) => {
  const actionValue = value as "approve" | "reject"; // 类型断言
  // 如果是退回操作，获取可退回节点
  if (actionValue === "reject") {
    await getBackNodes();
    showBackNodeSelect.value = true;
    approvalForm.shyj = "";
    approvalForm.afterId = "";
    approvalForm.returnType = 0;
  } else {
    showBackNodeSelect.value = false;
    approvalForm.shyj = "同意";
    approvalForm.afterId = "";
    approvalForm.returnType = 0;
  }
};
// 定义组件属性
const props = defineProps({
  // 表单标题
  title: {
    type: String,
    default: "审批表单",
  },
  // 表单数据
  guid: {
    type: String,
    required: true,
  },
  // 表单数据
  netcode: {
    type: String,
    required: true,
  },
  // 表单数据
  sjmc: {
    type: String,
    default: "",
  },
});

// 定义事件
const emit = defineEmits(["BeforeSubmit", "AfterSubmit"]);

// 表单引用
const approvalFormRef = ref<FormInstance>();
// 退回节点列表
const backNodes = ref<any[]>([]);
// 退回对话框可见性
const rejectDialogVisible = ref(false);
// 审批表单数据
const approvalForm = reactive({
  shyj: "同意",
  guid: props.guid || "",
  nowCode: props.netcode || "",
  sjmc: props.sjmc || "",
  afterId: "", // 退回节点
  returnType: 0,
});

// 处理同意操作
const handleApprove = async () => {
  if (!approvalFormRef.value) return;

  approvalForm.shyj = approvalForm.shyj || "同意";

  await approvalFormRef.value.validate(async (valid) => {
    if (valid) {
      ElMessageBox.confirm("确认提交?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const dialogloading = ElLoading.service({
          lock: true,
          text: "处理中",
        });

        submitApproval("Forward")
          .catch((error) => {
            ElMessage.error(error.message);
            dialogloading.close();
          })
          .finally(() => {
            dialogloading.close();
          });
      });
    }
  });
};

// 处理退回操作 - 打开退回对话框
const handleReject = async () => {
  try {
    // 获取可退回节点
    await getBackNodes();

    if (backNodes.value.length === 0) {
      ElMessage.warning("没有可退回的节点");
      return;
    }
    console.log("returnType:", approvalForm.returnType);
    // 清空退回相关字段
    approvalForm.afterId = "";
    approvalForm.returnType = 0;
    console.log("returnType:", approvalForm.returnType);
    // 显示退回对话框
    rejectDialogVisible.value = true;
  } catch (error) {
    console.error("获取退回节点失败:", error);
    ElMessage.error("获取退回节点失败");
  }
};

// 确认退回操作
const confirmReject = async () => {
  // 验证退回节点和退回方式是否已选择
  if (!approvalForm.afterId) {
    ElMessage.warning("请选择退回节点");
    return;
  }

  if (!approvalFormRef.value) return;

  await approvalFormRef.value.validate(async (valid) => {
    if (valid) {
      // 关闭退回对话框
      rejectDialogVisible.value = false;

      const dialogloading = ElLoading.service({
        lock: true,
        text: "处理中",
      });

      submitApproval("After")
        .catch((error) => {
          ElMessage.error(error.message);
        })
        .finally(() => {
          dialogloading.close();
        });
    }
  });
};

// 表单验证规则
const approvalRules = {
  shyj: [
    { required: true, message: "请输入审批意见", trigger: "blur" },
    { min: 2, max: 200, message: "长度在 2 到 200 个字符", trigger: "blur" },
  ],
  afterId: [{ required: true, message: "请选择退回节点", trigger: "change" }],
  returnType: [{ required: true, message: "请选择返回方式", trigger: "change" }],
};

// 获取可退回节点
const getBackNodes = async () => {
  try {
    if (props.guid && props.netcode) {
      const res = await WorkFlowAPI.getBackNodes(props.guid);
      console.log("退回节点列表:", res);
      backNodes.value = res || [];
    }
  } catch (error) {
    console.error("获取退回节点失败:", error);
    ElMessage.error("获取退回节点失败");
    throw error; // 向上抛出错误，让调用者知道发生了错误
  }
};

// 提交审批
const submitApproval = async (active: string) => {
  if (!approvalFormRef.value) return;
  await approvalFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const data: any = {
          ...approvalForm,
          active,
          afterId: active === "After" ? approvalForm.afterId : undefined,
          returnType: active === "After" ? approvalForm.returnType : undefined,
        };
        console.log("data", data);

        //提交前先调用
        emit("BeforeSubmit", async (result: boolean) => {
          console.log("CompB 的返回值:", result);
          if (result) {
            await WorkFlowAPI.workflowActive(data);

            // 触发对应事件
            if (active === "Forward") {
              ElMessage.success("提交成功");
              emit("AfterSubmit", approvalForm);
            } else if (active === "After") {
              ElMessage.success("退回成功");
              emit("AfterSubmit", approvalForm);
            }
          } else {
            ElMessage.error("操作失败，请重试");
          }
        });
      } catch (error) {
        console.error("审批操作失败:", error);
        ElMessage.error("操作失败，请重试");
      }
    }
  });
};

const getFormData = async (active: string): Promise<any> => {
  let actionValue = actionType.value === "approve" ? "Forward" : "After";
  if (active !== actionValue) return Promise.reject(new Error("操作类型不匹配，请重新提交"));

  if (!approvalFormRef.value) return Promise.reject(new Error("Form is not initialized"));
  const isValid = await approvalFormRef.value.validate();
  console.log("isValid", isValid);
  return {
    ...approvalForm,
    active,
    afterId: active === "After" ? approvalForm.afterId : undefined,
    returnType: active === "After" ? approvalForm.returnType : undefined,
  };
};

defineExpose({
  getFormData,
});
</script>

<style lang="scss" scoped>
.approval-form {
  width: 100%;
}

.approval-card {
  margin-bottom: 20px;

  .card-title {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
  }

  .approval-flow {
    margin: 20px 0;

    .flow-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 15px;
      color: var(--el-text-color-primary);
    }
  }

  .approval-history {
    margin: 20px 0;

    .history-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 15px;
      color: var(--el-text-color-primary);
    }

    .timeline-content {
      h4 {
        margin: 0;
        font-size: 14px;
        color: var(--el-text-color-primary);
      }

      p {
        margin: 5px 0 0;
        font-size: 13px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}
.radio-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.back-node-radio-item {
  margin: 5px 0;
}
.red {
  color: red;
}
</style>
