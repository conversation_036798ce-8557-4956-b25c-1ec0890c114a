<template>
  <el-tabs type="border-card" class="demo-tabs">
    <el-tab-pane v-if="pageType === 'check' && checkstatus == 0">
      <template #label>
        <span class="custom-tabs-label">
          <el-icon><Checked /></el-icon>
          <span>审批表单</span>
        </span>
      </template>
      <Title name="审批单" />
      <CheckTest ref="compBRef" />
      <CheckForm ref="CheckFormRef" :netcode="props.netcode" :guid="props.guid" sjmc="计划审核" />
      <el-form label-width="120px">
        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="getDataFromChild('Forward')">提交</el-button>
          <el-button type="danger" @click="getDataFromChild('After')">退回</el-button>
        </el-form-item>
      </el-form>
    </el-tab-pane>
    <el-tab-pane>
      <template #label>
        <span class="custom-tabs-label">
          <el-icon><calendar /></el-icon>
          <span>基本信息</span>
        </span>
      </template>
      <TaskEdit
        :id="props.id"
        :key="props.guid"
        :guid="props.guid"
        :editable="props.editable"
        dcbm=""
        :RefreshFatherDrawer="props.RefreshFatherDrawer"
      />
    </el-tab-pane>
    <el-tab-pane v-if="pageType === 'check'">
      <template #label>
        <span class="custom-tabs-label">
          <el-icon><Timer /></el-icon>
          <span>流程信息</span>
        </span>
      </template>
      <CheckList :guid="props.guid" :netcode="props.netcode" />
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
defineOptions({
  name: "taskView",
});
import taskAPI from "@/api/properties/task";
import TaskEdit from "./TaskEdit.vue";
import CheckForm from "@/components/GY/WorkFlowCheck/components/CheckForm.vue";
import CheckList from "@/components/GY/WorkFlowCheck/components/CheckList.vue";
import CheckTest from "@/components/GY/WorkFlowCheck/components/CheckTest.vue";
import { ElLoading } from "element-plus";
//————————————————————————————————————————————暴露的方法,和请求参数
const route = useRoute();
const pageType = ref(route.query.type?.toString() || "view");
const props = defineProps({
  guid: {
    type: String,
    required: true,
  },
  id: {
    type: Number,
  },
  netcode: {
    type: String,
    required: true,
  },
  editable: {
    type: Boolean,
    required: true,
  },
  dcbm: {
    type: String,
    required: true,
  },
  checkstatus: {
    type: Number,
  },
  //保存成功后需要关闭抽屉并刷新数据
  RefreshFatherDrawer: {
    type: Function,
    required: true,
  },
});

//——————————————————————————————————————————————————操作相关
// 声明 compBRef 的类型
interface ApprovalTestExposed {
  submitData?: () => boolean;
}
const compBRef = ref<ComponentPublicInstance<{}, ApprovalTestExposed> | null>(null);
const handleBeforeSubmit = async (callback: (result: boolean) => void) => {
  console.log("调用其他方法");
  callback(true);
};

const CheckFormRef = ref<InstanceType<typeof CheckForm> | null>(null);
const childData = ref<any | null>(null);
const submitData = reactive<any>({
  dto: ref<any>({}),
  data: ref<any>({}),
});
const getDataFromChild = async (active: string) => {
  if (CheckFormRef.value) {
    childData.value = await CheckFormRef.value.getFormData(active);
    console.log("子页面数据:", childData.value);
    submitData.dto = childData.value;
    const dialogloading = ElLoading.service({
      lock: true,
      text: "处理中",
    });
    taskAPI
      .submit(props.guid, submitData)
      .then(() => {
        //第三层提交完成
        active == "Forward" ? ElMessage.success("提交成功") : ElMessage.success("退回成功");
        props.RefreshFatherDrawer(true);
      })
      .finally(() => {
        dialogloading.close();
      })
      .catch((error) => {
        ElMessage.error(error.message);
      });
  }
};

// 定义事件处理函数
const handleAfterSubmit = () => {
  // 处理同意逻辑
  props.RefreshFatherDrawer(true);
};

onMounted(() => {
  // console.log("id", props.id);
  // console.log("guid", props.guid);
});
</script>
<style lang="scss" scoped></style>
