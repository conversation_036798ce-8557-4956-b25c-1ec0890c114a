import request from "@/utils/request";

/**
 * 资料下载分页查询参数
 */
export interface MaterialPageQuery {
  /** 当前页码 */
  pageNum: number;
  /** 每页显示条数 */
  pageSize: number;
  /** 标题 */
  title?: string;
  /** 类型 */
  type?: string;
}

/**
 * 资料下载分页视图对象
 */
export interface MaterialPageVO {
  /** 资料ID */
  id: number;
  /** 标题 */
  title: string;
  /** 描述 */
  description: string;
  /** 类型 */
  type: string;
  /** 下载次数 */
  downloadCount: number;
  /** 上传人ID */
  uploaderId: number;
  /** 上传人名称 */
  uploaderName: string;
  /** 创建时间 */
  createTime: string;
  /** 附件GUID */
  fileGuid: string;
}

/**
 * 资料下载表单对象
 */
export interface MaterialForm {
  /** 资料ID */
  id?: number;
  /** 标题 */
  title: string;
  /** 描述 */
  description: string;
  /** 类型 */
  type: string;
  /** 附件GUID */
  fileGuid?: string;
}

/**
 * 资料下载API
 */
const MaterialAPI = {
  /**
   * 获取资料下载分页列表
   *
   * @param queryParams 查询参数
   * @returns 资料下载分页列表
   */
  getPage(queryParams: MaterialPageQuery) {
    return request<any, PageResult<MaterialPageVO>>({
      url: "/api/v1/materials/page",
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 获取资料下载表单数据
   *
   * @param id 资料ID
   * @returns 资料下载表单数据
   */
  getFormData(id: number) {
    return request<any, MaterialForm>({
      url: `/api/v1/materials/${id}/form`,
      method: "get",
    });
  },

  /**
   * 添加资料下载
   *
   * @param data 资料下载表单数据
   * @returns 响应结果
   */
  add(data: MaterialForm) {
    return request({
      url: "/api/v1/materials",
      method: "post",
      data: data,
    });
  },

  /**
   * 修改资料下载
   *
   * @param id 资料ID
   * @param data 资料下载表单数据
   * @returns 响应结果
   */
  update(id: number, data: MaterialForm) {
    return request({
      url: `/api/v1/materials/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 删除资料下载
   *
   * @param ids 资料ID，多个以逗号分隔
   * @returns 响应结果
   */
  deleteByIds(ids: string) {
    return request({
      url: `/api/v1/materials/${ids}`,
      method: "delete",
    });
  },

  /**
   * 增加下载次数
   *
   * @param id 资料ID
   * @returns 响应结果
   */
  increaseDownloadCount(id: number) {
    return request({
      url: `/api/v1/materials/${id}/download`,
      method: "put",
    });
  },
};

export default MaterialAPI;
