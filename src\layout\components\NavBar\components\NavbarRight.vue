<template>
  <div class="navbar__right">
    <!-- 非手机设备（窄屏）才显示 -->
    <template v-if="!isMobile">
      <span class="platform-link" @click="router.push('/assetManagement')">
        <SvgIcon icon-class="monitor" class="platform-icon" />
        <span>资产大厅</span>
      </span>

      <span class="platform-link" @click="router.push('/warehouse')">
        <SvgIcon icon-class="document" class="platform-icon" />
        <span>公物仓平台</span>
      </span>

      <!-- 搜索 -->
      <MenuSearch />

      <!-- 全屏 -->
      <Fullscreen />

      <!-- 布局大小 -->
      <SizeSelect />

      <!-- 语言选择 -->
      <LangSelect />

      <!-- 消息通知 -->
      <Notification />
    </template>

    <!-- 用户头像（个人中心、注销登录等） -->
    <UserProfile />

    <!-- 设置面板 -->
    <div v-if="defaultSettings.showSettings" @click="settingStore.settingsVisible = true">
      <SvgIcon icon-class="setting" />
    </div>
  </div>
</template>
<script setup lang="ts">
import defaultSettings from "@/settings";
import { DeviceEnum } from "@/enums/DeviceEnum";

import { useAppStore, useSettingsStore } from "@/store";

import UserProfile from "./UserProfile.vue";
import Notification from "./Notification.vue";
import router from "@/router";
import SvgIcon from "@/components/SvgIcon/index.vue";

const appStore = useAppStore();
const settingStore = useSettingsStore();

const isMobile = computed(() => appStore.device === DeviceEnum.MOBILE);
</script>

<style lang="scss" scoped>
.navbar__right {
  display: flex;
  align-items: center;
  justify-content: center;

  & > * {
    display: inline-block;
    min-width: 40px;
    height: $navbar-height;
    line-height: $navbar-height;
    color: var(--el-text-color);
    text-align: center;
    cursor: pointer;

    &:hover {
      background: rgb(0 0 0 / 10%);
    }
  }

  .platform-link {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px;
    margin: 0 2px;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
    overflow: hidden;
    min-width: auto;

    &::before {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 2px;
      background-color: var(--el-color-primary);
      transition: width 0.3s ease;
    }

    &:hover {
      background: rgba(var(--el-color-primary-rgb), 0.1);
      color: var(--el-color-primary);

      &::before {
        width: 80%;
      }

      .platform-icon {
        transform: translateY(-2px);
      }
    }

    .platform-icon {
      margin-right: 4px;
      font-size: 16px;
      transition: transform 0.3s ease;
    }
  }
}

:deep(.el-divider--horizontal) {
  margin: 10px 0;
}

.dark .navbar__right > *:hover {
  background: rgb(255 255 255 / 20%);
}

.dark .navbar__right .platform-link:hover {
  background: rgba(var(--el-color-primary-rgb), 0.2);
}

.layout-top .navbar__right > *,
.layout-mix .navbar__right > * {
  color: #fff;
}
</style>
